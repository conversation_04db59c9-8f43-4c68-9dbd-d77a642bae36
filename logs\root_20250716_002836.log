2025-07-16 00:28:37 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:28:37 - main - INFO - 已确保输出目录结构存在: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output
2025-07-16 00:28:37 - main - INFO - 已保存数据文件路径到缓存: C:\Users\<USER>\Desktop\parameters\nodule2.csv
2025-07-16 00:28:37 - main - INFO - 训练和优化 RandomForest
2025-07-16 00:28:37 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 160, 'max_depth': 10, 'min_samples_split': 13, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9835
2025-07-16 00:29:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250716_002911.html
2025-07-16 00:29:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250716_002911.html
2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 33.61 秒
2025-07-16 00:29:11 - main - INFO - 使用最佳参数重新训练 RandomForest, 最佳分数: 0.9835
2025-07-16 00:29:11 - model_training - INFO - 模型名称: Random Forest
2025-07-16 00:29:11 - model_training - INFO - 准确率: 0.8750
2025-07-16 00:29:11 - model_training - INFO - AUC: 0.9488
2025-07-16 00:29:11 - model_training - INFO - 混淆矩阵:
2025-07-16 00:29:11 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-16 00:29:11 - model_training - INFO - 
分类报告:
2025-07-16 00:29:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 00:29:11 - model_training - INFO - 训练时间: 0.13 秒
2025-07-16 00:29:11 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 00:29:11 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
