2025-07-15 22:28:20 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-15 22:28:30 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 80, 'max_depth': 29, 'min_samples_split': 9, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-07-15 22:28:30 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9835
2025-07-15 22:28:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:31 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250715_222831.html
2025-07-15 22:28:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:31 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250715_222831.html
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 10.95 秒
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 20
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 212, 'max_depth': 7, 'learning_rate': 0.10312727703132145, 'subsample': 0.6779021185855993, 'colsample_bytree': 0.525587286744166}
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9869
2025-07-15 22:28:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250715_222835.html
2025-07-15 22:28:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250715_222835.html
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.51 秒
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 20
2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 56, 'max_depth': 10, 'learning_rate': 0.2070769447095247, 'feature_fraction': 0.5184131039958577, 'bagging_fraction': 0.8988346967121249}
2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9854
2025-07-15 22:28:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250715_222838.html
2025-07-15 22:28:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250715_222838.html
2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.28 秒
2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 20
2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 4.922693618620555, 'solver': 'lbfgs'}
2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-07-15 22:28:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250715_222839.html
2025-07-15 22:28:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250715_222839.html
2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.66 秒
2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 20
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 8.842081287127442, 'kernel': 'rbf'}
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9919
2025-07-15 22:28:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250715_222840.html
2025-07-15 22:28:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250715_222840.html
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.73 秒
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 20
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 6}
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9753
2025-07-15 22:28:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250715_222840.html
2025-07-15 22:28:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250715_222840.html
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.56 秒
