2025-07-15 21:16:00 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:16:00 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:00 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:16:00 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:00 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:16:00 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 21:16:00 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:16:00 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:16:00 - model_training - INFO - 模型名称: Random Forest
2025-07-15 21:16:00 - model_training - INFO - 准确率: 0.9000
2025-07-15 21:16:00 - model_training - INFO - AUC: 0.9394
2025-07-15 21:16:00 - model_training - INFO - 混淆矩阵:
2025-07-15 21:16:00 - model_training - INFO - 
[[28  2]
 [ 4 26]]
2025-07-15 21:16:00 - model_training - INFO - 
分类报告:
2025-07-15 21:16:00 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.93      0.90        30
           1       0.93      0.87      0.90        30

    accuracy                           0.90        60
   macro avg       0.90      0.90      0.90        60
weighted avg       0.90      0.90      0.90        60

2025-07-15 21:16:00 - model_training - INFO - 训练时间: 0.09 秒
2025-07-15 21:16:00 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 21:16:00 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:16:00 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:16:00 - model_training - INFO - 模型名称: XGBoost
2025-07-15 21:16:00 - model_training - INFO - 准确率: 0.8667
2025-07-15 21:16:00 - model_training - INFO - AUC: 0.9022
2025-07-15 21:16:00 - model_training - INFO - 混淆矩阵:
2025-07-15 21:16:00 - model_training - INFO - 
[[27  3]
 [ 5 25]]
2025-07-15 21:16:00 - model_training - INFO - 
分类报告:
2025-07-15 21:16:00 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.90      0.87        30
           1       0.89      0.83      0.86        30

    accuracy                           0.87        60
   macro avg       0.87      0.87      0.87        60
weighted avg       0.87      0.87      0.87        60

2025-07-15 21:16:00 - model_training - INFO - 训练时间: 0.09 秒
2025-07-15 21:16:00 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 21:16:00 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:16:00 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:16:00 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:16:00 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:16:00 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:16:00 - model_ensemble - INFO -     voting_soft - 准确率: 0.8667, F1: 0.8665
2025-07-15 21:16:00 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:16:00 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:16:00 - model_ensemble - INFO -     voting_hard - 准确率: 0.8833, F1: 0.8830
2025-07-15 21:16:00 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:00 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:16:00 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:00 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 21:16:00 - model_ensemble - INFO - 最佳F1分数: 0.8830
2025-07-15 21:16:00 - model_ensemble - INFO - 最佳准确率: 0.8833
2025-07-15 21:16:00 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:16:00 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8667, 精确率: 0.8683, 召回率: 0.8667, F1: 0.8665, AUC: 0.9278
2025-07-15 21:16:00 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8833, 精确率: 0.8872, 召回率: 0.8833, F1: 0.8830, AUC: 0.0000
2025-07-15 21:16:00 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 21:16:00 - plot_ensemble - INFO - Starting to generate ensemble learning visualization charts
2025-07-15 21:16:00 - plot_ensemble - INFO - Generating performance comparison chart
2025-07-15 21:16:00 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 21:16:01 - safe_visualization - INFO - Ensemble performance plot saved to: test_font_fix_output\performance_comparison.png
2025-07-15 21:16:01 - plot_ensemble - INFO - Generating summary report
2025-07-15 21:16:01 - safe_visualization - INFO - Summary report saved to: test_font_fix_output\ensemble_summary_report.txt
2025-07-15 21:16:01 - plot_ensemble - INFO - Performance comparison chart generated successfully
2025-07-15 21:16:01 - plot_ensemble - INFO - Summary report generated successfully
2025-07-15 21:16:01 - plot_ensemble - INFO - All visualization charts saved to: test_font_fix_output
2025-07-15 21:16:01 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:01 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:16:01 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:01 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:16:01 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 21:16:01 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:16:01 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:16:01 - model_training - INFO - 模型名称: Random Forest
2025-07-15 21:16:01 - model_training - INFO - 准确率: 0.9000
2025-07-15 21:16:01 - model_training - INFO - AUC: 0.9750
2025-07-15 21:16:01 - model_training - INFO - 混淆矩阵:
2025-07-15 21:16:01 - model_training - INFO - 
[[ 8  2]
 [ 0 10]]
2025-07-15 21:16:01 - model_training - INFO - 
分类报告:
2025-07-15 21:16:01 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.80      0.89        10
           1       0.83      1.00      0.91        10

    accuracy                           0.90        20
   macro avg       0.92      0.90      0.90        20
weighted avg       0.92      0.90      0.90        20

2025-07-15 21:16:01 - model_training - INFO - 训练时间: 0.09 秒
2025-07-15 21:16:01 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 21:16:01 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 21:16:01 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:16:01 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:16:01 - model_training - INFO - 模型名称: XGBoost
2025-07-15 21:16:01 - model_training - INFO - 准确率: 0.8500
2025-07-15 21:16:01 - model_training - INFO - AUC: 1.0000
2025-07-15 21:16:01 - model_training - INFO - 混淆矩阵:
2025-07-15 21:16:01 - model_training - INFO - 
[[ 7  3]
 [ 0 10]]
2025-07-15 21:16:01 - model_training - INFO - 
分类报告:
2025-07-15 21:16:01 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.70      0.82        10
           1       0.77      1.00      0.87        10

    accuracy                           0.85        20
   macro avg       0.88      0.85      0.85        20
weighted avg       0.88      0.85      0.85        20

2025-07-15 21:16:01 - model_training - INFO - 训练时间: 0.04 秒
2025-07-15 21:16:01 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 21:16:01 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 21:16:01 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:16:01 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:16:01 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:16:01 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:16:01 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:16:01 - model_ensemble - INFO -     voting_soft - 准确率: 0.8500, F1: 0.8465
2025-07-15 21:16:01 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:16:01 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:16:01 - model_ensemble - INFO -     voting_hard - 准确率: 0.9000, F1: 0.8990
2025-07-15 21:16:01 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 21:16:01 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 21:16:02 - model_ensemble - INFO -   stacking - 准确率: 0.8500, F1: 0.8465
2025-07-15 21:16:02 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:02 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:16:02 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:02 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 21:16:02 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-15 21:16:02 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-15 21:16:02 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:16:02 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8500, 精确率: 0.8846, 召回率: 0.8500, F1: 0.8465, AUC: 0.9800
2025-07-15 21:16:02 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9000, 精确率: 0.9167, 召回率: 0.9000, F1: 0.8990, AUC: 0.0000
2025-07-15 21:16:02 - model_ensemble - INFO -   stacking        - 准确率: 0.8500, 精确率: 0.8846, 召回率: 0.8500, F1: 0.8465, AUC: 0.9800
2025-07-15 21:16:02 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:16:02 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_211602.joblib
