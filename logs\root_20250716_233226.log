2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:32:26 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 23:32:26 - model_training - INFO - 准确率: 0.7722
2025-07-16 23:32:26 - model_training - INFO - AUC: 0.8330
2025-07-16 23:32:26 - model_training - INFO - 混淆矩阵:
2025-07-16 23:32:26 - model_training - INFO - 
[[62 28]
 [13 77]]
2025-07-16 23:32:26 - model_training - INFO - 
分类报告:
2025-07-16 23:32:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.69      0.75        90
           1       0.73      0.86      0.79        90

    accuracy                           0.77       180
   macro avg       0.78      0.77      0.77       180
weighted avg       0.78      0.77      0.77       180

2025-07-16 23:32:26 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 23:32:26 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7880
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:32:26 - model_training - INFO - 模型名称: Random Forest
2025-07-16 23:32:26 - model_training - INFO - 准确率: 0.8556
2025-07-16 23:32:26 - model_training - INFO - AUC: 0.9344
2025-07-16 23:32:26 - model_training - INFO - 混淆矩阵:
2025-07-16 23:32:26 - model_training - INFO - 
[[72 18]
 [ 8 82]]
2025-07-16 23:32:26 - model_training - INFO - 
分类报告:
2025-07-16 23:32:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.80      0.85        90
           1       0.82      0.91      0.86        90

    accuracy                           0.86       180
   macro avg       0.86      0.86      0.86       180
weighted avg       0.86      0.86      0.86       180

2025-07-16 23:32:26 - model_training - INFO - 训练时间: 0.15 秒
2025-07-16 23:32:26 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8757
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:32:26 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 23:32:26 - model_training - INFO - 准确率: 0.7944
2025-07-16 23:32:26 - model_training - INFO - AUC: 0.8772
2025-07-16 23:32:26 - model_training - INFO - 混淆矩阵:
2025-07-16 23:32:26 - model_training - INFO - 
[[66 24]
 [13 77]]
2025-07-16 23:32:26 - model_training - INFO - 
分类报告:
2025-07-16 23:32:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.73      0.78        90
           1       0.76      0.86      0.81        90

    accuracy                           0.79       180
   macro avg       0.80      0.79      0.79       180
weighted avg       0.80      0.79      0.79       180

2025-07-16 23:32:26 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:32:26 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8155
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:32:26 - model_training - INFO - 模型名称: SVM
2025-07-16 23:32:26 - model_training - INFO - 准确率: 0.8611
2025-07-16 23:32:26 - model_training - INFO - AUC: 0.9434
2025-07-16 23:32:26 - model_training - INFO - 混淆矩阵:
2025-07-16 23:32:26 - model_training - INFO - 
[[73 17]
 [ 8 82]]
2025-07-16 23:32:26 - model_training - INFO - 
分类报告:
2025-07-16 23:32:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.81      0.85        90
           1       0.83      0.91      0.87        90

    accuracy                           0.86       180
   macro avg       0.86      0.86      0.86       180
weighted avg       0.86      0.86      0.86       180

2025-07-16 23:32:26 - model_training - INFO - 训练时间: 0.03 秒
2025-07-16 23:32:26 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8821
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:32:26 - model_training - INFO - 模型名称: XGBoost
2025-07-16 23:32:26 - model_training - INFO - 准确率: 0.8500
2025-07-16 23:32:26 - model_training - INFO - AUC: 0.9210
2025-07-16 23:32:26 - model_training - INFO - 混淆矩阵:
2025-07-16 23:32:26 - model_training - INFO - 
[[71 19]
 [ 8 82]]
2025-07-16 23:32:26 - model_training - INFO - 
分类报告:
2025-07-16 23:32:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.79      0.84        90
           1       0.81      0.91      0.86        90

    accuracy                           0.85       180
   macro avg       0.86      0.85      0.85       180
weighted avg       0.86      0.85      0.85       180

2025-07-16 23:32:26 - model_training - INFO - 训练时间: 0.14 秒
2025-07-16 23:32:26 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8683
2025-07-16 23:32:26 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 23:32:26 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.126
2025-07-16 23:32:26 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:32:26 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.213
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.126
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.213
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.768, 多样性=0.232
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.711, 多样性=0.289
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.695, 多样性=0.305
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.741, 多样性=0.259
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.892, 多样性=0.108
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.909, 多样性=0.091
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.925, 多样性=0.075
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.857, 多样性=0.143
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.864, 多样性=0.136
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.937, 多样性=0.063
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.225
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.300
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 3, 'linear': 2}
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 5 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.822 (多样性: 0.178)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.183
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.627 (多样性: 0.373)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.364
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.780 (多样性: 0.220)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.592 (多样性: 0.408)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.384
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.716 (多样性: 0.284)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.211
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.078 (多样性: 0.922)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.572 (多样性: 0.428)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.419
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.803 (多样性: 0.197)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.189
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.615 (多样性: 0.385)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.374
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.928 (多样性: 0.072)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.128
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.111 (多样性: 0.889)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.741 (多样性: 0.259)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.289
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.944 (多样性: 0.056)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.094
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.809 (多样性: 0.191)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.265
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.122 (多样性: 0.878)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.899 (多样性: 0.101)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.214
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.133
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.106 (多样性: 0.894)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.730 (多样性: 0.270)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.297
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   Logistic_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.935 (多样性: 0.065)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.122
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.752 (多样性: 0.248)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.282
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   SVM_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.966 (多样性: 0.034)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.078
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.106 (多样性: 0.894)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.843 (多样性: 0.157)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.244
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.313
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.064
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.214
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.419
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.313
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.205
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   熵多样性: 0.241
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始评估 10 个模型组合...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.822 (多样性: 0.178)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.183
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.627 (多样性: 0.373)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.364
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.780 (多样性: 0.220)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.592 (多样性: 0.408)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.384
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.928 (多样性: 0.072)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.128
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.111 (多样性: 0.889)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.741 (多样性: 0.259)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.289
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.346
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.041
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.289
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.384
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'Logistic'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.826
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.346
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.586
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.822 (多样性: 0.178)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.183
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.627 (多样性: 0.373)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.364
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.716 (多样性: 0.284)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.211
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.078 (多样性: 0.922)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.572 (多样性: 0.428)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.419
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.944 (多样性: 0.056)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.094
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.809 (多样性: 0.191)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.265
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.349
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.064
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.265
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.419
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'SVM'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.849
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.349
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.599
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.822 (多样性: 0.178)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.183
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.627 (多样性: 0.373)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.364
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.803 (多样性: 0.197)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.189
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.615 (多样性: 0.385)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.374
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.122 (多样性: 0.878)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.899 (多样性: 0.101)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.214
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.317
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.073
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.214
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.374
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'XGBoost'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.844
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.317
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.581
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.780 (多样性: 0.220)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.592 (多样性: 0.408)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.384
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.716 (多样性: 0.284)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.211
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.078 (多样性: 0.922)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.572 (多样性: 0.428)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.419
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.133
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.106 (多样性: 0.894)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.730 (多样性: 0.270)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.297
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.367
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.051
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.297
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.419
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'SVM'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.829
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.367
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.598
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.780 (多样性: 0.220)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.592 (多样性: 0.408)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.384
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.803 (多样性: 0.197)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.189
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.615 (多样性: 0.385)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.374
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   Logistic_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.935 (多样性: 0.065)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.122
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.752 (多样性: 0.248)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.282
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.347
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.282
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.384
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'XGBoost'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.824
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.347
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.585
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.716 (多样性: 0.284)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.211
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.078 (多样性: 0.922)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.572 (多样性: 0.428)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.419
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.803 (多样性: 0.197)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.189
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.615 (多样性: 0.385)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.374
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   SVM_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.966 (多样性: 0.034)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.078
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.106 (多样性: 0.894)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.843 (多样性: 0.157)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.244
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.345
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.074
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.244
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.419
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'SVM', 'XGBoost'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.846
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.345
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.596
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.928 (多样性: 0.072)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.128
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.111 (多样性: 0.889)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.741 (多样性: 0.259)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.289
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.944 (多样性: 0.056)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.094
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.809 (多样性: 0.191)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.265
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.133
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.106 (多样性: 0.894)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.730 (多样性: 0.270)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.297
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.284
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.014
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.265
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.297
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'SVM'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.858
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.284
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.571
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.928 (多样性: 0.072)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.128
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.111 (多样性: 0.889)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.741 (多样性: 0.259)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.289
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.122 (多样性: 0.878)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.899 (多样性: 0.101)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.214
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   Logistic_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.935 (多样性: 0.065)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.122
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.752 (多样性: 0.248)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.282
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.262
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.034
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.214
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.289
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'XGBoost'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.853
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.262
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.558
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.944 (多样性: 0.056)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.094
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.094 (多样性: 0.906)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.809 (多样性: 0.191)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.265
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.122 (多样性: 0.878)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.899 (多样性: 0.101)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.214
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   SVM_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.966 (多样性: 0.034)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.078
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.106 (多样性: 0.894)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.843 (多样性: 0.157)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.244
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.241
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.021
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.214
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.265
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'SVM', 'XGBoost'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.875
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.241
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.558
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.133
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.106 (多样性: 0.894)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.730 (多样性: 0.270)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.297
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   Logistic_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.935 (多样性: 0.065)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.122
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.752 (多样性: 0.248)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.282
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   SVM_vs_XGBoost:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     Q统计量: 0.966 (多样性: 0.034)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     不一致性: 0.078
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     双错度量: 0.106 (多样性: 0.894)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     相关系数: 0.843 (多样性: 0.157)
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合多样性: 0.244
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   平均多样性: 0.274
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.022
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最小多样性: 0.244
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   最大多样性: 0.297
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'SVM', 'XGBoost'):
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     性能得分: 0.855
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性得分: 0.274
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     综合得分: 0.565
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:32:26 - quantified_diversity_evaluator - INFO - 最优组合排序完成，共评估 10 个组合
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'RandomForest', 'SVM']
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   性能得分: 0.8486
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3491
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   综合得分: 0.5988
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:32:26 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 23:32:26 - model_training - INFO - 准确率: 0.7167
2025-07-16 23:32:26 - model_training - INFO - AUC: 0.7594
2025-07-16 23:32:26 - model_training - INFO - 混淆矩阵:
2025-07-16 23:32:26 - model_training - INFO - 
[[50 10]
 [24 36]]
2025-07-16 23:32:26 - model_training - INFO - 
分类报告:
2025-07-16 23:32:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.68      0.83      0.75        60
           1       0.78      0.60      0.68        60

    accuracy                           0.72       120
   macro avg       0.73      0.72      0.71       120
weighted avg       0.73      0.72      0.71       120

2025-07-16 23:32:26 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:32:26 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7279
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:32:26 - model_training - INFO - 模型名称: Random Forest
2025-07-16 23:32:26 - model_training - INFO - 准确率: 0.8417
2025-07-16 23:32:26 - model_training - INFO - AUC: 0.9265
2025-07-16 23:32:26 - model_training - INFO - 混淆矩阵:
2025-07-16 23:32:26 - model_training - INFO - 
[[52  8]
 [11 49]]
2025-07-16 23:32:26 - model_training - INFO - 
分类报告:
2025-07-16 23:32:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        60
           1       0.86      0.82      0.84        60

    accuracy                           0.84       120
   macro avg       0.84      0.84      0.84       120
weighted avg       0.84      0.84      0.84       120

2025-07-16 23:32:26 - model_training - INFO - 训练时间: 0.11 秒
2025-07-16 23:32:26 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8630
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:32:26 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 23:32:26 - model_training - INFO - 准确率: 0.7500
2025-07-16 23:32:26 - model_training - INFO - AUC: 0.8403
2025-07-16 23:32:26 - model_training - INFO - 混淆矩阵:
2025-07-16 23:32:26 - model_training - INFO - 
[[44 16]
 [14 46]]
2025-07-16 23:32:26 - model_training - INFO - 
分类报告:
2025-07-16 23:32:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.73      0.75        60
           1       0.74      0.77      0.75        60

    accuracy                           0.75       120
   macro avg       0.75      0.75      0.75       120
weighted avg       0.75      0.75      0.75       120

2025-07-16 23:32:26 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:32:26 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7726
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:32:26 - model_training - INFO - 模型名称: SVM
2025-07-16 23:32:26 - model_training - INFO - 准确率: 0.8750
2025-07-16 23:32:26 - model_training - INFO - AUC: 0.9378
2025-07-16 23:32:26 - model_training - INFO - 混淆矩阵:
2025-07-16 23:32:26 - model_training - INFO - 
[[56  4]
 [11 49]]
2025-07-16 23:32:26 - model_training - INFO - 
分类报告:
2025-07-16 23:32:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.93      0.88        60
           1       0.92      0.82      0.87        60

    accuracy                           0.88       120
   macro avg       0.88      0.88      0.87       120
weighted avg       0.88      0.88      0.87       120

2025-07-16 23:32:26 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:32:26 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8912
2025-07-16 23:32:26 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 23:32:26 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.133
2025-07-16 23:32:26 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:32:27 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.435
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.133
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.435
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.744, 多样性=0.256
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.520, 多样性=0.480
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.670, 多样性=0.330
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.761, 多样性=0.239
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.915, 多样性=0.085
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.723, 多样性=0.277
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.653
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 开始计算 4 个模型间的量化多样性指标...
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.838 (多样性: 0.162)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.589 (多样性: 0.411)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.370
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.347 (多样性: 0.653)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.333
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.351 (多样性: 0.649)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.606
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.876 (多样性: 0.124)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.576 (多样性: 0.424)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.365
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.636 (多样性: 0.364)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.242
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.083 (多样性: 0.917)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.519 (多样性: 0.481)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.461
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.982 (多样性: 0.018)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.067
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.108 (多样性: 0.892)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.868 (多样性: 0.132)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.230
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.558 (多样性: 0.442)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.427
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   平均多样性: 0.410
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.113
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最小多样性: 0.230
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最大多样性: 0.606
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.410
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.277
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   熵多样性: 0.340
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'Logistic', 'SVM']
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 综合得分: 0.8423
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 综合得分: 0.4645
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 综合得分: 0.6206
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 开始评估 4 个模型组合...
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.838 (多样性: 0.162)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.589 (多样性: 0.411)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.370
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.347 (多样性: 0.653)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.333
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.351 (多样性: 0.649)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.606
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.636 (多样性: 0.364)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.242
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.083 (多样性: 0.917)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.519 (多样性: 0.481)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.461
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   平均多样性: 0.479
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.097
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最小多样性: 0.370
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最大多样性: 0.606
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'Logistic'):
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     性能得分: 0.788
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     多样性得分: 0.479
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合得分: 0.633
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.838 (多样性: 0.162)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.589 (多样性: 0.411)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.370
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.876 (多样性: 0.124)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.576 (多样性: 0.424)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.365
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.982 (多样性: 0.018)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.067
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.108 (多样性: 0.892)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.868 (多样性: 0.132)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.230
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   平均多样性: 0.322
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.065
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最小多样性: 0.230
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最大多样性: 0.370
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'SVM'):
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     性能得分: 0.827
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     多样性得分: 0.322
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合得分: 0.575
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.347 (多样性: 0.653)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.333
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.351 (多样性: 0.649)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.606
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.876 (多样性: 0.124)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.576 (多样性: 0.424)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.365
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.558 (多样性: 0.442)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.427
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   平均多样性: 0.466
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.102
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最小多样性: 0.365
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最大多样性: 0.606
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'SVM'):
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     性能得分: 0.797
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     多样性得分: 0.466
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合得分: 0.631
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.636 (多样性: 0.364)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.242
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.083 (多样性: 0.917)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.519 (多样性: 0.481)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.461
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.982 (多样性: 0.018)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.067
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.108 (多样性: 0.892)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.868 (多样性: 0.132)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.230
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     相关系数: 0.558 (多样性: 0.442)
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合多样性: 0.427
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   平均多样性: 0.373
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.102
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最小多样性: 0.230
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   最大多样性: 0.461
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'SVM'):
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     性能得分: 0.842
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     多样性得分: 0.373
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     综合得分: 0.607
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:32:27 - quantified_diversity_evaluator - INFO - 最优组合排序完成，共评估 4 个组合
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'RandomForest', 'Logistic']
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   性能得分: 0.7878
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   多样性得分: 0.4790
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   综合得分: 0.6334
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
