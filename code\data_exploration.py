#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据探索模块 - 专门用于分组分析和可视化
包含分组条形图、概率计算等功能
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

from logger import get_logger
from config import OUTPUT_PATH

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 学术论文风格配置
ACADEMIC_STYLE = {
    'figure.figsize': (12, 8),
    'font.size': 12,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 11,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight',
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'axes.axisbelow': True
}

# 专业配色方案
ACADEMIC_COLORS = {
    'primary': '#2E86AB',      # 深蓝色
    'secondary': '#A23B72',    # 深紫红色
    'accent': '#F18F01',       # 橙色
    'success': '#C73E1D',      # 深红色
    'neutral': '#6C757D',      # 灰色
    'light': '#E9ECEF',        # 浅灰色
    'palette': ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6C757D', '#28A745', '#17A2B8', '#FFC107']
}

logger = get_logger(__name__)

class DataExplorer:
    """数据探索分析器"""
    
    def __init__(self, output_dir: str = None):
        """
        初始化数据探索器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir) if output_dir else Path(OUTPUT_PATH) / "data_exploration"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 应用学术风格
        plt.rcParams.update(ACADEMIC_STYLE)
        
        logger.info(f"数据探索器初始化完成，输出目录: {self.output_dir}")
    
    def create_binned_probability_analysis(self, df: pd.DataFrame, 
                                         continuous_vars: List[str], 
                                         target_var: str,
                                         n_bins: int = 5,
                                         binning_method: str = 'qcut',
                                         save_plots: bool = True) -> Dict[str, Any]:
        """
        创建分组概率分析
        
        Args:
            df: 数据框
            continuous_vars: 连续变量列表
            target_var: 目标变量名
            n_bins: 分箱数量
            binning_method: 分箱方法 ('qcut' 或 'cut')
            save_plots: 是否保存图表
            
        Returns:
            dict: 分析结果
        """
        logger.info(f"开始分组概率分析，变量: {continuous_vars}, 目标: {target_var}")
        
        results = {}
        
        for var in continuous_vars:
            if var not in df.columns:
                logger.warning(f"变量 {var} 不存在于数据中，跳过")
                continue
                
            if var == target_var:
                logger.warning(f"跳过目标变量 {var}")
                continue
            
            # 执行分箱和概率计算
            var_result = self._analyze_single_variable(
                df, var, target_var, n_bins, binning_method
            )
            
            if var_result is not None:
                results[var] = var_result
                
                # 创建可视化
                if save_plots:
                    self._create_probability_bar_chart(
                        var_result, var, target_var
                    )
        
        # 创建综合比较图
        if len(results) > 1 and save_plots:
            self._create_comprehensive_comparison(results, target_var)
        
        logger.info(f"分组概率分析完成，共分析 {len(results)} 个变量")
        return results
    
    def _analyze_single_variable(self, df: pd.DataFrame, var: str, target_var: str,
                               n_bins: int, binning_method: str) -> Optional[Dict]:
        """分析单个变量的分组概率"""
        try:
            # 移除缺失值
            clean_df = df[[var, target_var]].dropna()
            
            if len(clean_df) == 0:
                logger.warning(f"变量 {var} 清理后无数据")
                return None
            
            # 执行分箱
            if binning_method == 'qcut':
                # 等频分箱
                bins, bin_edges = pd.qcut(clean_df[var], q=n_bins, retbins=True, duplicates='drop')
            else:
                # 等距分箱
                bins, bin_edges = pd.cut(clean_df[var], bins=n_bins, retbins=True)
            
            # 计算每组的统计信息
            grouped = clean_df.groupby(bins)[target_var].agg([
                'count', 'sum', 'mean'
            ]).reset_index()
            
            # 重命名列
            grouped.columns = ['bin', 'total_count', 'positive_count', 'probability']
            
            # 添加区间标签
            grouped['bin_label'] = grouped['bin'].astype(str)
            grouped['bin_center'] = grouped['bin'].apply(lambda x: x.mid if hasattr(x, 'mid') else x)
            
            # 计算置信区间
            grouped['ci_lower'], grouped['ci_upper'] = zip(*grouped.apply(
                lambda row: self._calculate_confidence_interval(
                    row['positive_count'], row['total_count']
                ), axis=1
            ))
            
            return {
                'variable': var,
                'target': target_var,
                'binning_method': binning_method,
                'n_bins': len(grouped),
                'bin_edges': bin_edges,
                'grouped_data': grouped,
                'total_samples': len(clean_df),
                'overall_probability': clean_df[target_var].mean()
            }
            
        except Exception as e:
            logger.error(f"分析变量 {var} 时出错: {e}")
            return None
    
    def _calculate_confidence_interval(self, positive_count: int, total_count: int,
                                     confidence: float = 0.95) -> Tuple[float, float]:
        """计算二项分布的置信区间"""
        if total_count == 0:
            return 0.0, 0.0

        p = positive_count / total_count

        # 对于极端情况的处理
        if positive_count == 0:
            return 0.0, min(1.0, 3.0 / total_count)  # 简单的上界估计
        elif positive_count == total_count:
            return max(0.0, 1.0 - 3.0 / total_count), 1.0  # 简单的下界估计

        z = 1.96  # 95% 置信区间

        # Wilson score interval
        n = total_count
        try:
            center = (p + z**2/(2*n)) / (1 + z**2/n)
            margin = z * np.sqrt((p*(1-p) + z**2/(4*n)) / n) / (1 + z**2/n)

            lower = max(0.0, center - margin)
            upper = min(1.0, center + margin)

            # 确保置信区间合理
            if lower > p:
                lower = max(0.0, p - 0.1)
            if upper < p:
                upper = min(1.0, p + 0.1)

            return lower, upper

        except (ZeroDivisionError, ValueError):
            # 回退到简单估计
            margin = 1.96 * np.sqrt(p * (1-p) / n) if n > 0 else 0.1
            return max(0.0, p - margin), min(1.0, p + margin)
    
    def _create_probability_bar_chart(self, var_result: Dict, var_name: str, target_name: str):
        """创建单变量概率条形图"""
        grouped_data = var_result['grouped_data']

        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 上图：概率条形图
        x_pos = np.arange(len(grouped_data))
        bars = ax1.bar(x_pos, grouped_data['probability'],
                      color=ACADEMIC_COLORS['primary'], alpha=0.8,
                      edgecolor='white', linewidth=1.5)

        # 添加误差线（置信区间）
        # 确保误差值不为负
        lower_err = np.maximum(0, grouped_data['probability'] - grouped_data['ci_lower'])
        upper_err = np.maximum(0, grouped_data['ci_upper'] - grouped_data['probability'])

        ax1.errorbar(x_pos, grouped_data['probability'],
                    yerr=[lower_err, upper_err],
                    fmt='none', color='black', capsize=5, capthick=2)

        # 计算标签位置，避免与误差条重叠
        max_upper_err = np.max(upper_err)
        label_offset = max(0.02, max_upper_err + 0.01)  # 动态调整标签位置

        # 添加数值标签，位置调整避免重叠
        for i, (bar, prob, count) in enumerate(zip(bars, grouped_data['probability'],
                                                  grouped_data['total_count'])):
            height = bar.get_height()
            # 检查数值是否有效
            if np.isfinite(height) and np.isfinite(prob) and np.isfinite(count):
                # 标签位置考虑误差条的高度
                label_y = height + upper_err[i] + label_offset
                ax1.text(bar.get_x() + bar.get_width()/2., label_y,
                        f'{prob:.3f}\n(n={int(count)})', ha='center', va='bottom',
                        fontsize=9, fontweight='bold')

        # 添加总体概率参考线
        overall_prob = var_result['overall_probability']
        ax1.axhline(y=overall_prob, color=ACADEMIC_COLORS['accent'],
                   linestyle='--', linewidth=2, alpha=0.8,
                   label=f'Overall Rate: {overall_prob:.3f}')

        ax1.set_xlabel(f'{var_name} Groups', fontsize=12, fontweight='bold')
        ax1.set_ylabel(f'{target_name} Probability', fontsize=12, fontweight='bold')
        ax1.set_title(f'Probability Analysis: {var_name} vs {target_name}',
                     fontsize=14, fontweight='bold', pad=20)
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels([f'Group {i+1}' for i in range(len(grouped_data))],
                           rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 调整y轴范围以容纳标签
        y_max = ax1.get_ylim()[1]
        ax1.set_ylim(0, y_max * 1.15)  # 增加15%的空间给标签

        # 下图：样本分布
        ax2.bar(x_pos, grouped_data['total_count'],
               color=ACADEMIC_COLORS['secondary'], alpha=0.7,
               edgecolor='white', linewidth=1.5)

        ax2.set_xlabel(f'{var_name} Groups', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Sample Count', fontsize=12, fontweight='bold')
        ax2.set_title('Sample Distribution Across Groups', fontsize=12, fontweight='bold')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels([f'Group {i+1}\n{label}' for i, label in
                            enumerate(grouped_data['bin_label'])],
                           rotation=45, ha='right', fontsize=9)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表为PDF格式
        save_path = self.output_dir / f"{var_name}_probability_analysis.pdf"
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"概率分析图表已保存: {save_path}")

    def _create_comprehensive_comparison(self, results: Dict[str, Dict], target_name: str):
        """创建综合比较图表"""
        n_vars = len(results)
        if n_vars == 0:
            return

        # 计算子图布局
        n_cols = min(3, n_vars)
        n_rows = (n_vars + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))
        if n_vars == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)

        # 确保有足够的颜色，如果变量数量超过预定义颜色，则循环使用
        base_colors = ACADEMIC_COLORS['palette']
        colors = [base_colors[i % len(base_colors)] for i in range(n_vars)]

        for idx, (var_name, var_result) in enumerate(results.items()):
            row = idx // n_cols
            col = idx % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]

            grouped_data = var_result['grouped_data']
            x_pos = np.arange(len(grouped_data))

            # 绘制条形图
            bars = ax.bar(x_pos, grouped_data['probability'],
                         color=colors[idx], alpha=0.8,
                         edgecolor='white', linewidth=1)

            # 添加总体概率参考线
            overall_prob = var_result['overall_probability']
            ax.axhline(y=overall_prob, color='red', linestyle='--',
                      linewidth=2, alpha=0.7)

            # 设置标签和标题
            ax.set_title(f'{var_name}', fontsize=12, fontweight='bold')
            ax.set_xlabel('Groups', fontsize=10)
            ax.set_ylabel(f'{target_name} Probability', fontsize=10)
            ax.set_xticks(x_pos)
            ax.set_xticklabels([f'G{i+1}' for i in range(len(grouped_data))])
            ax.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, prob in zip(bars, grouped_data['probability']):
                height = bar.get_height()
                # 检查数值是否有效
                if np.isfinite(height) and np.isfinite(prob):
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{prob:.2f}', ha='center', va='bottom', fontsize=9)

        # 隐藏多余的子图
        for idx in range(n_vars, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if n_rows > 1:
                axes[row, col].set_visible(False)
            else:
                axes[col].set_visible(False)

        plt.suptitle(f'Comprehensive Probability Analysis: {target_name}',
                    fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()

        # 保存图表为PDF格式
        save_path = self.output_dir / f"comprehensive_probability_comparison.pdf"
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"综合比较图表已保存: {save_path}")

    def create_correlation_heatmap(self, df: pd.DataFrame, target_var: str,
                                 save_plot: bool = True) -> Dict[str, Any]:
        """
        创建相关性热力图

        Args:
            df: 数据框
            target_var: 目标变量
            save_plot: 是否保存图表

        Returns:
            dict: 相关性分析结果
        """
        logger.info("开始相关性分析...")

        # 选择数值型变量，排除目标变量
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()

        # 确保目标变量是数值型但从热图中排除
        if target_var not in numeric_cols:
            logger.warning(f"目标变量 {target_var} 不是数值型，尝试转换")
            try:
                df[target_var] = pd.to_numeric(df[target_var])
            except:
                logger.error(f"无法转换目标变量 {target_var} 为数值型")
                return {}

        # 从热图特征中排除目标变量
        feature_cols = [col for col in numeric_cols if col != target_var]

        if len(feature_cols) < 2:
            logger.warning("特征变量少于2个，无法创建有意义的相关性热图")
            return {}

        # 计算特征间的相关性矩阵（不包含目标变量）
        corr_matrix = df[feature_cols].corr()

        if save_plot:
            # 创建热力图
            fig, ax = plt.subplots(figsize=(12, 10))

            # 创建掩码（隐藏上三角，但保留对角线）
            mask = np.triu(np.ones_like(corr_matrix, dtype=bool), k=1)

            # 绘制热力图
            sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdBu_r',
                       center=0, square=True, linewidths=0.5,
                       cbar_kws={"shrink": .8}, fmt='.3f',
                       annot_kws={'fontsize': 9})

            plt.title('Feature Correlation Heatmap (Excluding Target Variable)',
                     fontsize=16, fontweight='bold', pad=20)
            plt.xticks(rotation=45, ha='right')
            plt.yticks(rotation=0)
            plt.tight_layout()

            # 保存图表为PDF格式
            save_path = self.output_dir / "correlation_heatmap.pdf"
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            logger.info(f"相关性热力图已保存: {save_path}")

        # 计算特征与目标变量的相关性（单独计算）
        all_numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        if target_var not in all_numeric_cols:
            try:
                df[target_var] = pd.to_numeric(df[target_var])
                all_numeric_cols.append(target_var)
            except:
                pass

        if target_var in all_numeric_cols:
            target_correlations = df[feature_cols + [target_var]].corr()[target_var].drop(target_var).sort_values(
                key=abs, ascending=False
            )
        else:
            target_correlations = pd.Series(dtype=float)

        return {
            'correlation_matrix': corr_matrix,  # 特征间相关性矩阵
            'target_correlations': target_correlations,  # 特征与目标变量的相关性
            'strong_correlations': target_correlations[abs(target_correlations) > 0.3] if len(target_correlations) > 0 else pd.Series(dtype=float),
            'weak_correlations': target_correlations[abs(target_correlations) < 0.1] if len(target_correlations) > 0 else pd.Series(dtype=float)
        }

    def create_distribution_analysis(self, df: pd.DataFrame, continuous_vars: List[str],
                                   target_var: str, save_plots: bool = True) -> Dict[str, Any]:
        """
        创建分布分析图表

        Args:
            df: 数据框
            continuous_vars: 连续变量列表
            target_var: 目标变量
            save_plots: 是否保存图表

        Returns:
            dict: 分布分析结果
        """
        logger.info("开始分布分析...")

        results = {}

        for var in continuous_vars:
            if var not in df.columns or var == target_var:
                continue

            # 分析单个变量的分布
            var_result = self._analyze_distribution(df, var, target_var)
            if var_result:
                results[var] = var_result

                if save_plots:
                    self._create_distribution_plot(df, var, target_var)

        return results

    def _analyze_distribution(self, df: pd.DataFrame, var: str, target_var: str) -> Dict:
        """分析单个变量的分布"""
        try:
            clean_df = df[[var, target_var]].dropna()

            # 基本统计信息
            stats = clean_df[var].describe()

            # 按目标变量分组的统计
            group_stats = clean_df.groupby(target_var)[var].describe()

            # 正态性检验
            from scipy import stats
            _, normality_p = stats.normaltest(clean_df[var])

            # 偏度和峰度
            skewness = clean_df[var].skew()
            kurtosis = clean_df[var].kurtosis()

            return {
                'basic_stats': stats,
                'group_stats': group_stats,
                'normality_p_value': normality_p,
                'is_normal': normality_p > 0.05,
                'skewness': skewness,
                'kurtosis': kurtosis
            }
        except Exception as e:
            logger.error(f"分析变量 {var} 分布时出错: {e}")
            return {}

    def _create_distribution_plot(self, df: pd.DataFrame, var: str, target_var: str):
        """创建分布图"""
        clean_df = df[[var, target_var]].dropna()

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 整体分布直方图
        axes[0, 0].hist(clean_df[var], bins=30, alpha=0.7, color=ACADEMIC_COLORS['primary'],
                       edgecolor='white', linewidth=1)
        axes[0, 0].set_title(f'Distribution of {var}', fontweight='bold')
        axes[0, 0].set_xlabel(var)
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 按目标变量分组的分布
        for target_val in sorted(clean_df[target_var].unique()):
            subset = clean_df[clean_df[target_var] == target_val][var]
            axes[0, 1].hist(subset, bins=20, alpha=0.6,
                           label=f'{target_var}={target_val}',
                           edgecolor='white', linewidth=1)
        axes[0, 1].set_title(f'{var} Distribution by {target_var}', fontweight='bold')
        axes[0, 1].set_xlabel(var)
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 箱线图
        box_data = [clean_df[clean_df[target_var] == val][var].values
                   for val in sorted(clean_df[target_var].unique())]
        box_labels = [f'{target_var}={val}' for val in sorted(clean_df[target_var].unique())]

        bp = axes[1, 0].boxplot(box_data, labels=box_labels, patch_artist=True)
        for patch, color in zip(bp['boxes'], ACADEMIC_COLORS['palette']):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        axes[1, 0].set_title(f'{var} Box Plot by {target_var}', fontweight='bold')
        axes[1, 0].set_ylabel(var)
        axes[1, 0].grid(True, alpha=0.3)

        # 4. Q-Q图（正态性检验）
        from scipy import stats
        stats.probplot(clean_df[var], dist="norm", plot=axes[1, 1])
        axes[1, 1].set_title(f'Q-Q Plot: {var}', fontweight='bold')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表为PDF格式
        save_path = self.output_dir / f"{var}_distribution_analysis.pdf"
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"分布分析图表已保存: {save_path}")

    def generate_exploration_report(self, df: pd.DataFrame, continuous_vars: List[str],
                                  target_var: str, analysis_results: Dict) -> str:
        """
        生成数据探索报告

        Args:
            df: 数据框
            continuous_vars: 连续变量列表
            target_var: 目标变量
            analysis_results: 分析结果

        Returns:
            str: 报告文件路径
        """
        logger.info("生成数据探索报告...")

        report_content = f"""# 数据探索分析报告

## 数据概览
- 数据形状: {df.shape[0]} 行 × {df.shape[1]} 列
- 目标变量: {target_var}
- 分析变量: {', '.join(continuous_vars)}
- 生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

## 目标变量分布
"""

        # 目标变量统计
        target_counts = df[target_var].value_counts()
        target_props = df[target_var].value_counts(normalize=True)

        report_content += f"""
### 类别分布
"""
        for val in target_counts.index:
            count = target_counts[val]
            prop = target_props[val]
            report_content += f"- {target_var} = {val}: {count} 样本 ({prop:.1%})\n"

        # 分组概率分析结果
        if 'probability_analysis' in analysis_results:
            report_content += f"""
## 分组概率分析结果

"""
            for var, result in analysis_results['probability_analysis'].items():
                grouped_data = result['grouped_data']
                overall_prob = result['overall_probability']

                report_content += f"""
### {var} 分组分析
- 分箱方法: {result['binning_method']}
- 分组数量: {result['n_bins']}
- 总体概率: {overall_prob:.3f}

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
"""
                for i, row in grouped_data.iterrows():
                    report_content += f"| Group {i+1} | {row['total_count']} | {row['positive_count']} | {row['probability']:.3f} | [{row['ci_lower']:.3f}, {row['ci_upper']:.3f}] |\n"

        # 相关性分析结果
        if 'correlation_analysis' in analysis_results:
            corr_result = analysis_results['correlation_analysis']
            target_corr = corr_result['target_correlations']

            report_content += f"""
## 相关性分析结果

### 与目标变量的相关性排序
"""
            for var, corr in target_corr.head(10).items():
                report_content += f"- {var}: {corr:.3f}\n"

            strong_corr = corr_result['strong_correlations']
            if len(strong_corr) > 0:
                report_content += f"""
### 强相关变量 (|r| > 0.3)
"""
                for var, corr in strong_corr.items():
                    report_content += f"- {var}: {corr:.3f}\n"

        # 保存报告
        report_path = self.output_dir / "data_exploration_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"数据探索报告已保存: {report_path}")
        return str(report_path)
