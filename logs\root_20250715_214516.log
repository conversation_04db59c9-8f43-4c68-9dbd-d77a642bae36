2025-07-15 21:45:17 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:45:18 - GUI - INFO - GUI界面初始化完成
2025-07-15 21:45:55 - model_ensemble - INFO - ============================================================
2025-07-15 21:45:55 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:45:55 - model_ensemble - INFO - ============================================================
2025-07-15 21:45:55 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 21:45:55 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 21:45:55 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:45:55 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:45:55 - model_training - INFO - 模型名称: Random Forest
2025-07-15 21:45:55 - model_training - INFO - 准确率: 0.7250
2025-07-15 21:45:55 - model_training - INFO - AUC: 0.8184
2025-07-15 21:45:55 - model_training - INFO - 混淆矩阵:
2025-07-15 21:45:55 - model_training - INFO - 
[[19  4]
 [ 7 10]]
2025-07-15 21:45:55 - model_training - INFO - 
分类报告:
2025-07-15 21:45:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.73      0.83      0.78        23
           1       0.71      0.59      0.65        17

    accuracy                           0.72        40
   macro avg       0.72      0.71      0.71        40
weighted avg       0.72      0.72      0.72        40

2025-07-15 21:45:55 - model_training - INFO - 训练时间: 0.09 秒
2025-07-15 21:45:55 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 21:45:55 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 21:45:55 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:45:55 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:45:55 - model_training - INFO - 模型名称: XGBoost
2025-07-15 21:45:55 - model_training - INFO - 准确率: 0.8000
2025-07-15 21:45:55 - model_training - INFO - AUC: 0.8261
2025-07-15 21:45:55 - model_training - INFO - 混淆矩阵:
2025-07-15 21:45:55 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-07-15 21:45:55 - model_training - INFO - 
分类报告:
2025-07-15 21:45:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-07-15 21:45:55 - model_training - INFO - 训练时间: 0.11 秒
2025-07-15 21:45:55 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 21:45:55 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 21:45:55 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:45:55 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 21:45:57 - model_training - INFO - 模型名称: LightGBM
2025-07-15 21:45:57 - model_training - INFO - 准确率: 0.7750
2025-07-15 21:45:57 - model_training - INFO - AUC: 0.8465
2025-07-15 21:45:57 - model_training - INFO - 混淆矩阵:
2025-07-15 21:45:57 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-07-15 21:45:57 - model_training - INFO - 
分类报告:
2025-07-15 21:45:57 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-07-15 21:45:57 - model_training - INFO - 训练时间: 1.68 秒
2025-07-15 21:45:57 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 21:45:57 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 21:45:57 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 21:45:57 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 21:45:57 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 21:45:57 - model_training - INFO - 准确率: 0.7750
2025-07-15 21:45:57 - model_training - INFO - AUC: 0.8159
2025-07-15 21:45:57 - model_training - INFO - 混淆矩阵:
2025-07-15 21:45:57 - model_training - INFO - 
[[22  1]
 [ 8  9]]
2025-07-15 21:45:57 - model_training - INFO - 
分类报告:
2025-07-15 21:45:57 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.73      0.96      0.83        23
           1       0.90      0.53      0.67        17

    accuracy                           0.78        40
   macro avg       0.82      0.74      0.75        40
weighted avg       0.80      0.78      0.76        40

2025-07-15 21:45:57 - model_training - INFO - 训练时间: 0.00 秒
2025-07-15 21:45:57 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 21:45:57 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 21:45:57 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 21:45:57 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 21:45:57 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:45:57 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:45:57 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:45:57 - model_ensemble - INFO -     voting_soft - 准确率: 0.7750, F1: 0.7740
2025-07-15 21:45:57 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:45:57 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:45:57 - model_ensemble - INFO -     voting_hard - 准确率: 0.7250, F1: 0.7148
2025-07-15 21:45:57 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 21:45:57 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 21:45:59 - model_ensemble - INFO -   stacking - 准确率: 0.7750, F1: 0.7740
2025-07-15 21:45:59 - model_ensemble - INFO - ============================================================
2025-07-15 21:45:59 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:45:59 - model_ensemble - INFO - ============================================================
2025-07-15 21:45:59 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 21:45:59 - model_ensemble - INFO - 最佳F1分数: 0.7740
2025-07-15 21:45:59 - model_ensemble - INFO - 最佳准确率: 0.7750
2025-07-15 21:45:59 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:45:59 - model_ensemble - INFO -   voting_soft     - 准确率: 0.7750, 精确率: 0.7740, 召回率: 0.7750, F1: 0.7740, AUC: 0.8312
2025-07-15 21:45:59 - model_ensemble - INFO -   voting_hard     - 准确率: 0.7250, 精确率: 0.7295, 召回率: 0.7250, F1: 0.7148, AUC: 0.0000
2025-07-15 21:45:59 - model_ensemble - INFO -   stacking        - 准确率: 0.7750, 精确率: 0.7740, 召回率: 0.7750, F1: 0.7740, AUC: 0.8389
2025-07-15 21:45:59 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:45:59 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_214559.joblib
2025-07-15 21:45:59 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 21:45:59 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-15 21:45:59 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 21:45:59 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 21:45:59 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 21:46:04 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 21:46:04 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 21:46:09 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 21:46:09 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 21:46:12 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 21:46:16 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 21:46:16 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-15 21:46:16 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
