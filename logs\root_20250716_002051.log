2025-07-16 00:20:52 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:20:53 - GUI - INFO - GUI界面初始化完成
2025-07-16 00:21:03 - model_ensemble - INFO - ============================================================
2025-07-16 00:21:03 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 00:21:03 - model_ensemble - INFO - ============================================================
2025-07-16 00:21:03 - model_ensemble - INFO - 基础模型: ['RandomForest', 'KNN']
2025-07-16 00:21:03 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-16 00:21:03 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 00:21:03 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 00:21:03 - model_training - INFO - 模型名称: Random Forest
2025-07-16 00:21:03 - model_training - INFO - 准确率: 0.8250
2025-07-16 00:21:03 - model_training - INFO - AUC: 0.9015
2025-07-16 00:21:03 - model_training - INFO - 混淆矩阵:
2025-07-16 00:21:03 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-16 00:21:03 - model_training - INFO - 
分类报告:
2025-07-16 00:21:03 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 00:21:03 - model_training - INFO - 训练时间: 0.07 秒
2025-07-16 00:21:03 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 00:21:03 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-16 00:21:03 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 00:21:03 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 00:21:03 - model_training - INFO - 模型名称: KNN
2025-07-16 00:21:03 - model_training - INFO - 准确率: 0.9000
2025-07-16 00:21:03 - model_training - INFO - AUC: 0.8913
2025-07-16 00:21:03 - model_training - INFO - 混淆矩阵:
2025-07-16 00:21:03 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-07-16 00:21:03 - model_training - INFO - 
分类报告:
2025-07-16 00:21:03 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-16 00:21:03 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 00:21:03 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-16 00:21:03 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-16 00:21:03 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 00:21:03 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-16 00:21:03 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-16 00:21:03 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-16 00:21:03 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-16 00:21:03 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-16 00:21:03 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-16 00:21:03 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-16 00:21:03 - model_ensemble - INFO -     voting_hard - 准确率: 0.8500, F1: 0.8484
2025-07-16 00:21:03 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 00:21:03 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 00:21:04 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-16 00:21:04 - model_ensemble - INFO - ============================================================
2025-07-16 00:21:04 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 00:21:04 - model_ensemble - INFO - ============================================================
2025-07-16 00:21:04 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-16 00:21:04 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-16 00:21:04 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-16 00:21:04 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 00:21:04 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9233
2025-07-16 00:21:04 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8500, 精确率: 0.8513, 召回率: 0.8500, F1: 0.8484, AUC: 0.0000
2025-07-16 00:21:04 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9233
2025-07-16 00:21:04 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 00:21:04 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_002104.joblib
2025-07-16 00:21:04 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 00:21:04 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-16 00:21:04 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 00:21:04 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 00:21:04 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-16 00:21:04 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.07760585,  0.11212312,  0.05948353,  0.02199276,  0.02734335,
        0.00505526,  0.12901716, -0.00627927, -0.02234177])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.07760585, -0.11212312, -0.05948353, -0.02199276, -0.02734335,
       -0.00505526, -0.12901716,  0.00627927,  0.02234177])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.07553155,  0.10603958,  0.06663869,  0.02962798,  0.04200893,
       -0.02760506,  0.12926964,  0.00084107,  0.00164762])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.07553155, -0.10603958, -0.06663869, -0.02962798, -0.04200893,
        0.02760506, -0.12926964, -0.00084107, -0.00164762])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.10129732, -0.14478423, -0.12653452, -0.03683095, -0.03843452,
        0.02195208, -0.09124583, -0.01959315, -0.00423155])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.10129732,  0.14478423,  0.12653452,  0.03683095,  0.03843452,
       -0.02195208,  0.09124583,  0.01959315,  0.00423155])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.12671637, -0.07548839, -0.17609464,  0.01596369,  0.02196667,
       -0.0339128 , -0.15918304, -0.01179881, -0.01073631])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.12671637,  0.07548839,  0.17609464, -0.01596369, -0.02196667,
        0.0339128 ,  0.15918304,  0.01179881,  0.01073631])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.0597627 ,  0.11234038,  0.07466597,  0.02749752,  0.04301419,
       -0.02538046,  0.06348681,  0.02197639,  0.04663651])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.0597627 , -0.11234038, -0.07466597, -0.02749752, -0.04301419,
        0.02538046, -0.06348681, -0.02197639, -0.04663651])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.06682817,  0.10326062,  0.09011657, -0.01607659, -0.02128343,
        0.07111151,  0.04950169,  0.02321597,  0.0173255 ])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.06682817, -0.10326062, -0.09011657,  0.01607659,  0.02128343,
       -0.07111151, -0.04950169, -0.02321597, -0.0173255 ])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.07382569,  0.10347808,  0.09558611,  0.02118819,  0.02812956,
       -0.0176874 ,  0.13258105, -0.01611062, -0.01699067])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.07382569, -0.10347808, -0.09558611, -0.02118819, -0.02812956,
        0.0176874 , -0.13258105,  0.01611062,  0.01699067])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.06321696,  0.09314196,  0.06130893,  0.01522381, -0.0151378 ,
       -0.05431458, -0.22351994,  0.02307798,  0.03600268])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.06321696, -0.09314196, -0.06130893, -0.01522381,  0.0151378 ,
        0.05431458,  0.22351994, -0.02307798, -0.03600268])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.09540308, -0.1033251 , -0.18582629,  0.02330228,  0.01694722,
       -0.00161438,  0.07871716,  0.01725764,  0.01894454])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.09540308,  0.1033251 ,  0.18582629, -0.02330228, -0.01694722,
        0.00161438, -0.07871716, -0.01725764, -0.01894454])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([-0.0018998 , -0.10329921, -0.05509861, -0.03035129, -0.02659623,
        0.04292788, -0.1406004 , -0.00816052, -0.00792183])
2025-07-16 00:21:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:04 - shap - INFO - phi = array([ 0.0018998 ,  0.10329921,  0.05509861,  0.03035129,  0.02659623,
       -0.04292788,  0.1406004 ,  0.00816052,  0.00792183])
2025-07-16 00:21:04 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.02959385,  0.19504663, -0.00497034,  0.03617312,  0.03427639,
       -0.03677361,  0.12807163, -0.01648492, -0.00674504])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.02959385, -0.19504663,  0.00497034, -0.03617312, -0.03427639,
        0.03677361, -0.12807163,  0.01648492,  0.00674504])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.08614683,  0.10610367,  0.07029236,  0.02601349,  0.04150159,
       -0.02323681,  0.11973254, -0.00338591,  0.00083224])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.08614683, -0.10610367, -0.07029236, -0.02601349, -0.04150159,
        0.02323681, -0.11973254,  0.00338591, -0.00083224])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.06854494, -0.25139256, -0.03912113, -0.01752679, -0.02273274,
       -0.04143601, -0.0387994 , -0.01176756, -0.04967887])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([0.06854494, 0.25139256, 0.03912113, 0.01752679, 0.02273274,
       0.04143601, 0.0387994 , 0.01176756, 0.04967887])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.05788095, -0.21364643, -0.0747494 , -0.01463214, -0.02915238,
       -0.01268065,  0.00031071, -0.00786607, -0.05570268])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.05788095,  0.21364643,  0.0747494 ,  0.01463214,  0.02915238,
        0.01268065, -0.00031071,  0.00786607,  0.05570268])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.05637321, -0.10847827, -0.12539196,  0.01248095,  0.02795179,
       -0.00096071, -0.12891488, -0.00425893, -0.01705476])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.05637321,  0.10847827,  0.12539196, -0.01248095, -0.02795179,
        0.00096071,  0.12891488,  0.00425893,  0.01705476])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.09159524, -0.14505655,  0.1044744 ,  0.00253988, -0.0168119 ,
        0.01320268,  0.05732619, -0.03054494, -0.032725  ])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.09159524,  0.14505655, -0.1044744 , -0.00253988,  0.0168119 ,
       -0.01320268, -0.05732619,  0.03054494,  0.032725  ])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.05824573,  0.09247937,  0.03940258, -0.02631825, -0.04000248,
       -0.04704831, -0.26391438,  0.03008532, -0.00392956])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.05824573, -0.09247937, -0.03940258,  0.02631825,  0.04000248,
        0.04704831,  0.26391438, -0.03008532,  0.00392956])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.10026756, -0.05462232,  0.11477232, -0.0031247 , -0.01031042,
       -0.00203601,  0.0830375 , -0.01742976,  0.00344583])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.10026756,  0.05462232, -0.11477232,  0.0031247 ,  0.01031042,
        0.00203601, -0.0830375 ,  0.01742976, -0.00344583])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.08836915,  0.08020635,  0.09632837, -0.01236687, -0.01645794,
        0.07620694,  0.05810784,  0.02289623,  0.01570992])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.08836915, -0.08020635, -0.09632837,  0.01236687,  0.01645794,
       -0.07620694, -0.05810784, -0.02289623, -0.01570992])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.0600876 , -0.12742063, -0.04603165, -0.0256251 , -0.02840188,
       -0.0615376 , -0.19018968, -0.01994504,  0.01323919])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.0600876 ,  0.12742063,  0.04603165,  0.0256251 ,  0.02840188,
        0.0615376 ,  0.19018968,  0.01994504, -0.01323919])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.0011753 , -0.03071607,  0.09689732, -0.02106518, -0.03386994,
        0.08534851,  0.04720923, -0.00384494,  0.00286577])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.0011753 ,  0.03071607, -0.09689732,  0.02106518,  0.03386994,
       -0.08534851, -0.04720923,  0.00384494, -0.00286577])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.01614593,  0.02682063,  0.09691915, -0.01006002, -0.01982996,
        0.07408075,  0.10138254,  0.02260784,  0.01593313])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.01614593, -0.02682063, -0.09691915,  0.01006002,  0.01982996,
       -0.07408075, -0.10138254, -0.02260784, -0.01593313])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.09077202,  0.11093125,  0.08058006, -0.00813065, -0.00594315,
        0.08510417,  0.02857262,  0.03806637, -0.00095268])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.09077202, -0.11093125, -0.08058006,  0.00813065,  0.00594315,
       -0.08510417, -0.02857262, -0.03806637,  0.00095268])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([ 0.09147748,  0.11134355,  0.07591617,  0.03035694,  0.02974296,
       -0.02347252,  0.11640546, -0.00483234, -0.0029377 ])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.09147748, -0.11134355, -0.07591617, -0.03035694, -0.02974296,
        0.02347252, -0.11640546,  0.00483234,  0.0029377 ])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([-0.0782506 , -0.13723571, -0.10620119, -0.01173006, -0.0212378 ,
       -0.02580833, -0.16401637, -0.0068247 , -0.02469524])
2025-07-16 00:21:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:05 - shap - INFO - phi = array([0.0782506 , 0.13723571, 0.10620119, 0.01173006, 0.0212378 ,
       0.02580833, 0.16401637, 0.0068247 , 0.02469524])
2025-07-16 00:21:05 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.08204454,  0.08698829,  0.06886567, -0.00731498, -0.0059254 ,
        0.09873591,  0.02476151,  0.04764038,  0.02820407])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.08204454, -0.08698829, -0.06886567,  0.00731498,  0.0059254 ,
       -0.09873591, -0.02476151, -0.04764038, -0.02820407])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.13339772,  0.09932282, -0.24391974,  0.02056716,  0.01906151,
       -0.01412659,  0.05256776, -0.02267361, -0.02340159])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.13339772, -0.09932282,  0.24391974, -0.02056716, -0.01906151,
        0.01412659, -0.05256776,  0.02267361,  0.02340159])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.08066022, -0.21314901,  0.10272272,  0.02868581,  0.03959593,
       -0.02808502,  0.06912063,  0.00743998,  0.06700873])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.08066022,  0.21314901, -0.10272272, -0.02868581, -0.03959593,
        0.02808502, -0.06912063, -0.00743998, -0.06700873])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.00784772, -0.01465972,  0.02970784, -0.0366999 , -0.02152817,
       -0.07785169, -0.27244722, -0.01657073, -0.01379812])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.00784772,  0.01465972, -0.02970784,  0.0366999 ,  0.02152817,
        0.07785169,  0.27244722,  0.01657073,  0.01379812])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.10415952, -0.18753185, -0.1670125 ,  0.02830804,  0.04065417,
        0.0139994 ,  0.09439643,  0.00723869, -0.01689286])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.10415952,  0.18753185,  0.1670125 , -0.02830804, -0.04065417,
       -0.0139994 , -0.09439643, -0.00723869,  0.01689286])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.07074236,  0.14862718,  0.05804385, -0.01142728, -0.00188532,
        0.00597927,  0.12115813, -0.01527847, -0.01195972])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.07074236, -0.14862718, -0.05804385,  0.01142728,  0.00188532,
       -0.00597927, -0.12115813,  0.01527847,  0.01195972])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.08056587, -0.15469772, -0.08443343, -0.02548016, -0.02587659,
       -0.02417361, -0.15586617, -0.01679623, -0.00311022])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([0.08056587, 0.15469772, 0.08443343, 0.02548016, 0.02587659,
       0.02417361, 0.15586617, 0.01679623, 0.00311022])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.06290516, -0.1113004 , -0.01269623, -0.02226141, -0.00208909,
       -0.03135784, -0.22813819, -0.01600099,  0.00574931])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.06290516,  0.1113004 ,  0.01269623,  0.02226141,  0.00208909,
        0.03135784,  0.22813819,  0.01600099, -0.00574931])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.05527847,  0.16438433,  0.00345992, -0.01394931, -0.01587847,
        0.11272361,  0.00516855,  0.00879117, -0.00042133])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.05527847, -0.16438433, -0.00345992,  0.01394931,  0.01587847,
       -0.11272361, -0.00516855, -0.00879117,  0.00042133])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.05709901,  0.16748849, -0.10608889,  0.03866766,  0.02564236,
       -0.00055496,  0.19288135,  0.02536379,  0.02269921])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.05709901, -0.16748849,  0.10608889, -0.03866766, -0.02564236,
        0.00055496, -0.19288135, -0.02536379, -0.02269921])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.05429613, -0.01656399,  0.01007619,  0.00502857,  0.01696577,
       -0.0333247 ,  0.12772679, -0.00629137, -0.00032113])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.05429613,  0.01656399, -0.01007619, -0.00502857, -0.01696577,
        0.0333247 , -0.12772679,  0.00629137,  0.00032113])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.1831369 ,  0.1039375 , -0.17375565,  0.01842411,  0.01763214,
       -0.00338363,  0.08876399,  0.02721696,  0.03330149])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.1831369 , -0.1039375 ,  0.17375565, -0.01842411, -0.01763214,
        0.00338363, -0.08876399, -0.02721696, -0.03330149])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.08164018,  0.0654119 ,  0.10405565, -0.01383542, -0.02183274,
       -0.03067024,  0.10558452, -0.02493929, -0.00641458])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.08164018, -0.0654119 , -0.10405565,  0.01383542,  0.02183274,
        0.03067024, -0.10558452,  0.02493929,  0.00641458])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.05764048, -0.09990327,  0.03536935, -0.03166548, -0.02587827,
       -0.05332738, -0.25532173, -0.01588065, -0.00175208])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.05764048,  0.09990327, -0.03536935,  0.03166548,  0.02587827,
        0.05332738,  0.25532173,  0.01588065,  0.00175208])
2025-07-16 00:21:06 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([ 0.10062579,  0.10379573,  0.09224246, -0.00556944, -0.0053379 ,
        0.02593294,  0.10729782, -0.00015694,  0.00516954])
2025-07-16 00:21:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:06 - shap - INFO - phi = array([-0.10062579, -0.10379573, -0.09224246,  0.00556944,  0.0053379 ,
       -0.02593294, -0.10729782,  0.00015694, -0.00516954])
2025-07-16 00:21:11 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_soft
2025-07-16 00:21:11 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-16 00:21:11 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 00:21:11 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 00:21:11 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-16 00:21:11 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-16 00:21:11 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-16 00:21:11 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:11 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:11 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:11 - shap - INFO - phi = array([-0.09020833, -0.08199405, -0.02943452, -0.02047619, -0.03252976,
       -0.00050595, -0.13199405,  0.00446429,  0.00767857])
2025-07-16 00:21:11 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:12 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:12 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:12 - shap - INFO - phi = array([-0.09915675, -0.06338294, -0.04269841, -0.03037698, -0.04171627,
        0.01599206, -0.10385913, -0.0040377 , -0.00576389])
2025-07-16 00:21:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:12 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:12 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:12 - shap - INFO - phi = array([ 0.19661706,  0.13152778,  0.10569444,  0.05771825,  0.03483135,
       -0.03659722,  0.10634921,  0.00998016,  0.01887897])
2025-07-16 00:21:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:13 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:13 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:13 - shap - INFO - phi = array([ 0.18071429,  0.12136905,  0.10363095, -0.02035714, -0.02535714,
        0.03047619,  0.20830357,  0.01321429,  0.01300595])
2025-07-16 00:21:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:13 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:13 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:13 - shap - INFO - phi = array([-0.05814484, -0.08406746, -0.04040675, -0.02016865, -0.02871032,
        0.00736111, -0.06915675, -0.02186508, -0.05984127])
2025-07-16 00:21:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:13 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:13 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:13 - shap - INFO - phi = array([-0.08662698, -0.0856746 , -0.04909722,  0.01161706,  0.00060516,
       -0.06570437, -0.0734127 , -0.00924603, -0.01746032])
2025-07-16 00:21:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:14 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:14 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:14 - shap - INFO - phi = array([-0.09789683, -0.07414683, -0.04667659, -0.01611111, -0.03378968,
        0.00650794, -0.11935516,  0.00493056,  0.0015377 ])
2025-07-16 00:21:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:14 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:14 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:14 - shap - INFO - phi = array([-0.12372024, -0.18157738, -0.04330357, -0.06214286, -0.02154762,
        0.04502976,  0.10863095, -0.03544643, -0.06092262])
2025-07-16 00:21:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:15 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:15 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:15 - shap - INFO - phi = array([ 0.20467262,  0.17119048,  0.2802381 , -0.03422619,  0.04369048,
        0.0196131 , -0.03181548, -0.02041667, -0.00794643])
2025-07-16 00:21:15 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:15 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:15 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:15 - shap - INFO - phi = array([ 0.11197421,  0.18986111,  0.06411706,  0.07902778,  0.02917659,
       -0.03671627,  0.15887897,  0.00819444,  0.02048611])
2025-07-16 00:21:15 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:16 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:16 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:16 - shap - INFO - phi = array([-0.00275794, -0.19085317, -0.00751984, -0.04626984, -0.03647817,
        0.02718254, -0.13076389,  0.00950397,  0.00295635])
2025-07-16 00:21:16 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:16 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:16 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:16 - shap - INFO - phi = array([-0.10766865, -0.06225198, -0.04775794, -0.02781746, -0.03963294,
        0.01123016, -0.09659722, -0.0002877 , -0.00421627])
2025-07-16 00:21:16 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:16 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:16 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:17 - shap - INFO - phi = array([0.10489087, 0.29959325, 0.03435516, 0.01992063, 0.02134921,
       0.05271825, 0.03578373, 0.02578373, 0.03060516])
2025-07-16 00:21:17 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:17 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:17 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:17 - shap - INFO - phi = array([0.09930556, 0.35353175, 0.05222222, 0.0134127 , 0.03219246,
       0.02359127, 0.00144841, 0.01165675, 0.03763889])
2025-07-16 00:21:17 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:18 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:18 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:18 - shap - INFO - phi = array([ 0.16669643,  0.16880952,  0.08401786, -0.01720238, -0.02404762,
        0.00303571,  0.22077381,  0.00604167,  0.016875  ])
2025-07-16 00:21:18 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:18 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:18 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:18 - shap - INFO - phi = array([-0.2097619 ,  0.05806548, -0.10065476, -0.01008929, -0.00252976,
       -0.04744048, -0.07803571, -0.00345238,  0.01889881])
2025-07-16 00:21:18 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:19 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:19 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:19 - shap - INFO - phi = array([-0.02027778, -0.08444444,  0.03040675,  0.07987103,  0.13323413,
        0.11037698,  0.35814484, -0.0122123 ,  0.02990079])
2025-07-16 00:21:19 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:19 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:19 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:19 - shap - INFO - phi = array([-0.18602183,  0.01394841, -0.0997123 ,  0.00141865, -0.00075397,
       -0.0109623 , -0.1024504 ,  0.00769841,  0.00183532])
2025-07-16 00:21:19 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:20 - shap - INFO - phi = array([-0.12155754, -0.0578373 , -0.06295635,  0.00430556, -0.00051587,
       -0.05429563, -0.05399802, -0.01286706, -0.01527778])
2025-07-16 00:21:20 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:20 - shap - INFO - phi = array([ 0.0659127 ,  0.14749008,  0.04052579,  0.0321627 ,  0.0340377 ,
        0.06650794,  0.23246032,  0.01409722, -0.00819444])
2025-07-16 00:21:20 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:20 - shap - INFO - phi = array([-0.01300595, -0.02470238, -0.17973214,  0.0185119 ,  0.01875   ,
       -0.06916667, -0.11735119,  0.00190476, -0.01020833])
2025-07-16 00:21:20 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:21 - shap - INFO - phi = array([-0.03574405, -0.03666667, -0.09669643,  0.00714286,  0.00994048,
       -0.04479167, -0.15580357, -0.00675595, -0.015625  ])
2025-07-16 00:21:21 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:21 - shap - INFO - phi = array([-0.11198413, -0.08638889, -0.05933532,  0.00015873, -0.0025496 ,
       -0.06710317, -0.03180556, -0.01850198,  0.00250992])
2025-07-16 00:21:21 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:21 - shap - INFO - phi = array([-0.1118254 , -0.06813492, -0.05069444, -0.03146825, -0.02801587,
        0.01186508, -0.09977183,  0.00228175,  0.00076389])
2025-07-16 00:21:21 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:22 - shap - INFO - phi = array([0.11868056, 0.17993056, 0.05930556, 0.0081746 , 0.01207341,
       0.02412698, 0.1925496 , 0.00746032, 0.02269841])
2025-07-16 00:21:22 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:22 - shap - INFO - phi = array([-0.10882937, -0.06963294, -0.03549603,  0.00105159, -0.00862103,
       -0.06856151, -0.03897817, -0.02016865, -0.02576389])
2025-07-16 00:21:22 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:23 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:23 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:23 - shap - INFO - phi = array([ 0.04771825, -0.28326389,  0.05703373, -0.02281746, -0.01996032,
        0.00643849, -0.17171627,  0.00825397,  0.00331349])
2025-07-16 00:21:23 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:23 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:23 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:23 - shap - INFO - phi = array([-0.16083333,  0.09267857, -0.11571429, -0.04175595, -0.04863095,
        0.02136905, -0.08818452, -0.01261905, -0.02130952])
2025-07-16 00:21:23 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:23 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:23 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:23 - shap - INFO - phi = array([ 0.0806746 ,  0.00109127, -0.03256944,  0.05010913,  0.01945437,
        0.08829365,  0.38609127,  0.01263889,  0.01921627])
2025-07-16 00:21:23 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:24 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:24 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:24 - shap - INFO - phi = array([ 0.19352183,  0.30664683,  0.23572421, -0.02731151, -0.0212996 ,
       -0.00722222, -0.0752877 , -0.00454365,  0.02477183])
2025-07-16 00:21:24 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:25 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:25 - shap - INFO - phi = array([-0.09313492, -0.11304563, -0.02218254,  0.00356151, -0.00947421,
       -0.02176587, -0.12599206,  0.00507937,  0.00195437])
2025-07-16 00:21:25 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:25 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:25 - shap - INFO - phi = array([0.12931548, 0.17345238, 0.07568452, 0.02904762, 0.0260119 ,
       0.01535714, 0.1547619 , 0.01607143, 0.00529762])
2025-07-16 00:21:25 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:26 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:26 - shap - INFO - phi = array([ 0.17058532,  0.09924603,  0.02076389,  0.03588294,  0.03356151,
        0.02882937,  0.22112103,  0.01838294, -0.00337302])
2025-07-16 00:21:26 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:26 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:26 - shap - INFO - phi = array([ 0.02818452, -0.24857143,  0.02574405,  0.01672619,  0.01630952,
       -0.14282738, -0.06866071, -0.01011905,  0.00821429])
2025-07-16 00:21:26 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:27 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:27 - shap - INFO - phi = array([ 0.03326389, -0.19277778,  0.05171627, -0.03474206, -0.01500992,
       -0.00640873, -0.19587302, -0.0112004 , -0.00396825])
2025-07-16 00:21:27 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:27 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:27 - shap - INFO - phi = array([-0.01934524, -0.06946429, -0.07988095, -0.04377976, -0.04443452,
        0.02345238, -0.14875   ,  0.00833333, -0.00113095])
2025-07-16 00:21:27 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:28 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:28 - shap - INFO - phi = array([ 0.08236111, -0.28001984,  0.06146825, -0.02016865, -0.01609127,
        0.0015873 , -0.15531746, -0.01948413, -0.02933532])
2025-07-16 00:21:28 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:28 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:28 - shap - INFO - phi = array([-0.11712302, -0.07944444, -0.07256944,  0.00561508,  0.0037996 ,
        0.00826389, -0.13239087,  0.00862103,  0.00022817])
2025-07-16 00:21:28 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:29 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:29 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:29 - shap - INFO - phi = array([ 0.09518849,  0.08358135, -0.01847222,  0.0487004 ,  0.03572421,
        0.05881944,  0.29896825,  0.01709325,  0.00539683])
2025-07-16 00:21:29 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:30 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:30 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:30 - shap - INFO - phi = array([-0.13465278, -0.07367063, -0.0490873 ,  0.00314484, -0.00304563,
       -0.02795635, -0.08694444,  0.0015377 , -0.0043254 ])
2025-07-16 00:21:36 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_hard
2025-07-16 00:21:36 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-16 00:21:36 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 00:21:36 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 00:21:36 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-16 00:21:36 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 00:21:36 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-16 00:21:36 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-16 00:21:36 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([ 0.06804355,  0.1024334 ,  0.05424881,  0.01484867,  0.01764763,
       -0.00090272,  0.12200956, -0.00709572, -0.01696215])
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([-0.06804355, -0.1024334 , -0.05424881, -0.01484867, -0.01764763,
        0.00090272, -0.12200956,  0.00709572,  0.01696215])
2025-07-16 00:21:36 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([ 0.06538988,  0.08615599,  0.05675962,  0.02562019,  0.03438916,
       -0.02127505,  0.11302127,  0.00022536,  0.00100882])
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([-0.06538988, -0.08615599, -0.05675962, -0.02562019, -0.03438916,
        0.02127505, -0.11302127, -0.00022536, -0.00100882])
2025-07-16 00:21:36 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([-0.09500694, -0.13828997, -0.11916495, -0.03013379, -0.03063788,
        0.02110416, -0.08123191, -0.01335578, -0.00636912])
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([ 0.09500694,  0.13828997,  0.11916495,  0.03013379,  0.03063788,
       -0.02110416,  0.08123191,  0.01335578,  0.00636912])
2025-07-16 00:21:36 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([-0.11214972, -0.06850042, -0.15550956,  0.01603326,  0.01775494,
       -0.02958743, -0.14681581, -0.01070072, -0.01004322])
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([ 0.11214972,  0.06850042,  0.15550956, -0.01603326, -0.01775494,
        0.02958743,  0.14681581,  0.01070072,  0.01004322])
2025-07-16 00:21:36 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([ 0.05002042,  0.09569248,  0.06262058,  0.02000424,  0.03072017,
       -0.01932112,  0.05838011,  0.01946563,  0.04371272])
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([-0.05002042, -0.09569248, -0.06262058, -0.02000424, -0.03072017,
        0.01932112, -0.05838011, -0.01946563, -0.04371272])
2025-07-16 00:21:36 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([ 0.06148239,  0.09179316,  0.07994568, -0.01281583, -0.01290944,
        0.0594276 ,  0.04705221,  0.01801404,  0.01452112])
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([-0.06148239, -0.09179316, -0.07994568,  0.01281583,  0.01290944,
       -0.0594276 , -0.04705221, -0.01801404, -0.01452112])
2025-07-16 00:21:36 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([ 0.06540544,  0.09187922,  0.0802358 ,  0.01414026,  0.01918406,
       -0.01346686,  0.1216615 , -0.01236362, -0.01240477])
2025-07-16 00:21:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:36 - shap - INFO - phi = array([-0.06540544, -0.09187922, -0.0802358 , -0.01414026, -0.01918406,
        0.01346686, -0.1216615 ,  0.01236362,  0.01240477])
2025-07-16 00:21:36 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([ 0.06460155,  0.09314105,  0.05978258,  0.01646155, -0.01244744,
       -0.05358366, -0.20196785,  0.02597624,  0.04237834])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.06460155, -0.09314105, -0.05978258, -0.01646155,  0.01244744,
        0.05358366,  0.20196785, -0.02597624, -0.04237834])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.10033165, -0.111983  , -0.18727795,  0.01944512,  0.01226713,
       -0.00134065,  0.07340457,  0.01567042,  0.01675505])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([ 0.10033165,  0.111983  ,  0.18727795, -0.01944512, -0.01226713,
        0.00134065, -0.07340457, -0.01567042, -0.01675505])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.01013287, -0.10984217, -0.05889568, -0.02924275, -0.02602068,
        0.03700987, -0.14053026, -0.00873338, -0.01114866])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([ 0.01013287,  0.10984217,  0.05889568,  0.02924275,  0.02602068,
       -0.03700987,  0.14053026,  0.00873338,  0.01114866])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.01610424,  0.19129976, -0.00100641,  0.03016334,  0.02843247,
       -0.03107078,  0.12224197, -0.01387904, -0.00621362])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([ 0.01610424, -0.19129976,  0.00100641, -0.03016334, -0.02843247,
        0.03107078, -0.12224197,  0.01387904,  0.00621362])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([ 0.07368986,  0.08697906,  0.05979663,  0.02252989,  0.03522202,
       -0.01830427,  0.10459934, -0.00340974,  0.00019244])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.07368986, -0.08697906, -0.05979663, -0.02252989, -0.03522202,
        0.01830427, -0.10459934,  0.00340974, -0.00019244])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.05974407, -0.2439991 , -0.03572701, -0.01373658, -0.01881071,
       -0.03604643, -0.03596544, -0.01126711, -0.03778974])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([0.05974407, 0.2439991 , 0.03572701, 0.01373658, 0.01881071,
       0.03604643, 0.03596544, 0.01126711, 0.03778974])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.05217765, -0.21765785, -0.06985986, -0.01194279, -0.02610062,
       -0.01445266, -0.00654758, -0.00662524, -0.04781085])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([0.05217765, 0.21765785, 0.06985986, 0.01194279, 0.02610062,
       0.01445266, 0.00654758, 0.00662524, 0.04781085])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.0733911 , -0.11140162, -0.11819276,  0.01210874,  0.02367047,
       -0.00183849, -0.12965494, -0.00417373, -0.01614121])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([ 0.0733911 ,  0.11140162,  0.11819276, -0.01210874, -0.02367047,
        0.00183849,  0.12965494,  0.00417373,  0.01614121])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([ 0.09450343, -0.12028892,  0.10775519,  0.00389909, -0.013492  ,
        0.01765702,  0.06151193, -0.02668319, -0.03083618])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.09450343,  0.12028892, -0.10775519, -0.00389909,  0.013492  ,
       -0.01765702, -0.06151193,  0.02668319,  0.03083618])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([ 5.38668162e-02,  8.66698852e-02,  3.60718138e-02, -2.75950534e-02,
       -4.01652913e-02, -4.66367262e-02, -2.65494317e-01,  3.29123803e-02,
       -2.46454171e-04])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-5.38668162e-02, -8.66698852e-02, -3.60718138e-02,  2.75950534e-02,
        4.01652913e-02,  4.66367262e-02,  2.65494317e-01, -3.29123803e-02,
        2.46454171e-04])
2025-07-16 00:21:37 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([ 0.09926512, -0.02928971,  0.11534556, -0.0022515 , -0.00744015,
        0.00076166,  0.09026031, -0.01286668,  0.0015391 ])
2025-07-16 00:21:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:37 - shap - INFO - phi = array([-0.09926512,  0.02928971, -0.11534556,  0.0022515 ,  0.00744015,
       -0.00076166, -0.09026031,  0.01286668, -0.0015391 ])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([ 0.07615007,  0.0687415 ,  0.08592784, -0.00902202, -0.01074833,
        0.06342187,  0.05268071,  0.01695551,  0.01198657])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.07615007, -0.0687415 , -0.08592784,  0.00902202,  0.01074833,
       -0.06342187, -0.05268071, -0.01695551, -0.01198657])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.05037463, -0.12013238, -0.04256845, -0.02179037, -0.02370845,
       -0.0534618 , -0.17552759, -0.01623589,  0.00851711])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([ 0.05037463,  0.12013238,  0.04256845,  0.02179037,  0.02370845,
        0.0534618 ,  0.17552759,  0.01623589, -0.00851711])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([ 0.01006241, -0.01518492,  0.10255138, -0.01887682, -0.02837832,
        0.08733956,  0.0532695 , -0.00247874,  0.00287581])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.01006241,  0.01518492, -0.10255138,  0.01887682,  0.02837832,
       -0.08733956, -0.0532695 ,  0.00247874, -0.00287581])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([ 0.02435831,  0.03178069,  0.09624669, -0.00933797, -0.01625941,
        0.06264903,  0.09983728,  0.01672282,  0.01219335])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.02435831, -0.03178069, -0.09624669,  0.00933797,  0.01625941,
       -0.06264903, -0.09983728, -0.01672282, -0.01219335])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([ 0.07602418,  0.09696939,  0.07156143, -0.00589387, -0.0023717 ,
        0.0703468 ,  0.02417291,  0.03117981, -0.00238406])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.07602418, -0.09696939, -0.07156143,  0.00589387,  0.0023717 ,
       -0.0703468 , -0.02417291, -0.03117981,  0.00238406])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([ 0.07806114,  0.09202597,  0.06476064,  0.0254409 ,  0.02351231,
       -0.01873198,  0.10371342, -0.00403284, -0.00345432])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.07806114, -0.09202597, -0.06476064, -0.0254409 , -0.02351231,
        0.01873198, -0.10371342,  0.00403284,  0.00345432])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.06303071, -0.12887487, -0.09093125, -0.0095433 , -0.01615365,
       -0.02423003, -0.14859802, -0.00508465, -0.02094734])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([0.06303071, 0.12887487, 0.09093125, 0.0095433 , 0.01615365,
       0.02423003, 0.14859802, 0.00508465, 0.02094734])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([ 0.07044437,  0.07574671,  0.05961746, -0.00588446, -0.00161282,
        0.08163879,  0.02304543,  0.03373092,  0.02456884])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.07044437, -0.07574671, -0.05961746,  0.00588446,  0.00161282,
       -0.08163879, -0.02304543, -0.03373092, -0.02456884])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.13379759,  0.09614133, -0.23320294,  0.02106398,  0.0181257 ,
       -0.01244576,  0.05029162, -0.02344501, -0.02469172])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([ 0.13379759, -0.09614133,  0.23320294, -0.02106398, -0.0181257 ,
        0.01244576, -0.05029162,  0.02344501,  0.02469172])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([ 0.08245888, -0.17765732,  0.10481395,  0.02976032,  0.04124145,
       -0.02594779,  0.07072818,  0.00739056,  0.06851385])
2025-07-16 00:21:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:38 - shap - INFO - phi = array([-0.08245888,  0.17765732, -0.10481395, -0.02976032, -0.04124145,
        0.02594779, -0.07072818, -0.00739056, -0.06851385])
2025-07-16 00:21:38 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.00264157, -0.0144865 ,  0.02886197, -0.03566312, -0.01821278,
       -0.07406873, -0.27638315, -0.01605223, -0.01490708])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.00264157,  0.0144865 , -0.02886197,  0.03566312,  0.01821278,
        0.07406873,  0.27638315,  0.01605223,  0.01490708])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.10241084, -0.19336052, -0.16333209,  0.02552323,  0.03546631,
        0.01191869,  0.08257129,  0.00585923, -0.01763903])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.10241084,  0.19336052,  0.16333209, -0.02552323, -0.03546631,
       -0.01191869, -0.08257129, -0.00585923,  0.01763903])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.06283977,  0.13312032,  0.05177435, -0.00690317, -0.00205826,
        0.00437777,  0.1172791 , -0.01240668, -0.01006999])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.06283977, -0.13312032, -0.05177435,  0.00690317,  0.00205826,
       -0.00437777, -0.1172791 ,  0.01240668,  0.01006999])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.06796528, -0.14022439, -0.07604428, -0.02104099, -0.02109497,
       -0.02038736, -0.1400243 , -0.01444538, -0.00427031])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([0.06796528, 0.14022439, 0.07604428, 0.02104099, 0.02109497,
       0.02038736, 0.1400243 , 0.01444538, 0.00427031])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.05803672, -0.10263124, -0.01501478, -0.02049615, -0.00560291,
       -0.0288559 , -0.22069257, -0.01458681,  0.00363732])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.05803672,  0.10263124,  0.01501478,  0.02049615,  0.00560291,
        0.0288559 ,  0.22069257,  0.01458681, -0.00363732])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.03701511,  0.16690452,  0.00789615, -0.01332692, -0.01452274,
        0.1101861 ,  0.01304199,  0.00725127, -0.00266903])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.03701511, -0.16690452, -0.00789615,  0.01332692,  0.01452274,
       -0.1101861 , -0.01304199, -0.00725127,  0.00266903])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.04099163,  0.16775126, -0.08680346,  0.03083079,  0.02071082,
       -0.00039599,  0.18236389,  0.01940385,  0.01691806])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.04099163, -0.16775126,  0.08680346, -0.03083079, -0.02071082,
        0.00039599, -0.18236389, -0.01940385, -0.01691806])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.04129749, -0.0018103 ,  0.01748723,  0.00105024,  0.01476006,
       -0.03057159,  0.1287013 , -0.00716176, -0.00125979])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.04129749,  0.0018103 , -0.01748723, -0.00105024, -0.01476006,
        0.03057159, -0.1287013 ,  0.00716176,  0.00125979])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.17648354,  0.11868199, -0.15563798,  0.01899922,  0.01738051,
       -0.00297227,  0.09499861,  0.02998661,  0.03606236])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.17648354, -0.11868199,  0.15563798, -0.01899922, -0.01738051,
        0.00297227, -0.09499861, -0.02998661, -0.03606236])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.07668583,  0.07111649,  0.09774837, -0.00926726, -0.01398214,
       -0.02224089,  0.11164019, -0.01738636, -0.00579246])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.07668583, -0.07111649, -0.09774837,  0.00926726,  0.01398214,
        0.02224089, -0.11164019,  0.01738636,  0.00579246])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([-0.05046202, -0.08830686,  0.02874267, -0.02780175, -0.02258181,
       -0.04689506, -0.25051504, -0.01508021, -0.00326108])
2025-07-16 00:21:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:39 - shap - INFO - phi = array([ 0.05046202,  0.08830686, -0.02874267,  0.02780175,  0.02258181,
        0.04689506,  0.25051504,  0.01508021,  0.00326108])
2025-07-16 00:21:39 - shap - INFO - num_full_subsets = 4
2025-07-16 00:21:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:40 - shap - INFO - phi = array([ 0.0849088 ,  0.0888979 ,  0.07861703, -0.0053566 , -0.00517272,
        0.02119305,  0.09747057, -0.00119412,  0.00193132])
2025-07-16 00:21:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 00:21:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 00:21:40 - shap - INFO - phi = array([-0.0849088 , -0.0888979 , -0.07861703,  0.0053566 ,  0.00517272,
       -0.02119305, -0.09747057,  0.00119412, -0.00193132])
2025-07-16 00:21:45 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-16 00:21:45 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 00:21:45 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 00:21:45 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 00:21:45 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 00:21:50 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 00:21:51 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-16 00:21:51 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
