2025-07-15 20:06:11 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:06:12 - __main__ - INFO - 🚀 集成学习功能完整使用示例
2025-07-15 20:06:12 - __main__ - INFO - 
开始执行: 基础集成学习
2025-07-15 20:06:12 - __main__ - INFO - ============================================================
2025-07-15 20:06:12 - __main__ - INFO - 示例1: 基础集成学习
2025-07-15 20:06:12 - __main__ - INFO - ============================================================
2025-07-15 20:06:12 - __main__ - INFO - 创建示例数据...
2025-07-15 20:06:12 - __main__ - INFO - 示例数据已保存到: sample_data.csv
2025-07-15 20:06:12 - __main__ - INFO - 数据形状: (1000, 21)
2025-07-15 20:06:12 - __main__ - INFO - 类别分布: [500 500]
2025-07-15 20:06:12 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:12 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:06:12 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:12 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM']
2025-07-15 20:06:12 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:06:12 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:06:12 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:06:12 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:06:12 - model_training - INFO - 准确率: 0.9700
2025-07-15 20:06:12 - model_training - INFO - AUC: 0.9908
2025-07-15 20:06:12 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:12 - model_training - INFO - 
[[97  3]
 [ 3 97]]
2025-07-15 20:06:12 - model_training - INFO - 
分类报告:
2025-07-15 20:06:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.97      0.97       100
           1       0.97      0.97      0.97       100

    accuracy                           0.97       200
   macro avg       0.97      0.97      0.97       200
weighted avg       0.97      0.97      0.97       200

2025-07-15 20:06:12 - model_training - INFO - 训练时间: 0.23 秒
2025-07-15 20:06:12 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:06:12 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:06:12 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:06:12 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:06:12 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:06:12 - model_training - INFO - 准确率: 0.9750
2025-07-15 20:06:12 - model_training - INFO - AUC: 0.9879
2025-07-15 20:06:12 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:12 - model_training - INFO - 
[[96  4]
 [ 1 99]]
2025-07-15 20:06:12 - model_training - INFO - 
分类报告:
2025-07-15 20:06:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.99      0.96      0.97       100
           1       0.96      0.99      0.98       100

    accuracy                           0.97       200
   macro avg       0.98      0.97      0.97       200
weighted avg       0.98      0.97      0.97       200

2025-07-15 20:06:12 - model_training - INFO - 训练时间: 0.14 秒
2025-07-15 20:06:12 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:06:12 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:06:12 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:06:12 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:06:12 - model_training - INFO - 模型名称: LightGBM
2025-07-15 20:06:12 - model_training - INFO - 准确率: 0.9700
2025-07-15 20:06:12 - model_training - INFO - AUC: 0.9894
2025-07-15 20:06:12 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:12 - model_training - INFO - 
[[96  4]
 [ 2 98]]
2025-07-15 20:06:12 - model_training - INFO - 
分类报告:
2025-07-15 20:06:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.98      0.96      0.97       100
           1       0.96      0.98      0.97       100

    accuracy                           0.97       200
   macro avg       0.97      0.97      0.97       200
weighted avg       0.97      0.97      0.97       200

2025-07-15 20:06:12 - model_training - INFO - 训练时间: 0.13 秒
2025-07-15 20:06:12 - model_training - INFO - 模型 LightGBM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 20:06:12 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 20:06:12 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:06:12 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-15 20:06:12 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:06:12 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:06:12 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:13 - model_ensemble - INFO -     voting_soft - 准确率: 0.9800, F1: 0.9800
2025-07-15 20:06:13 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:06:13 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:13 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:06:13 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:06:13 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:06:15 - model_ensemble - INFO -   stacking - 准确率: 0.9800, F1: 0.9800
2025-07-15 20:06:15 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:15 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:06:15 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:15 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 20:06:15 - model_ensemble - INFO - 最佳F1分数: 0.9800
2025-07-15 20:06:15 - model_ensemble - INFO - 最佳准确率: 0.9800
2025-07-15 20:06:15 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:06:15 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9800, 精确率: 0.9800, 召回率: 0.9800, F1: 0.9800, AUC: 0.9922
2025-07-15 20:06:15 - model_ensemble - INFO -   stacking        - 准确率: 0.9800, 精确率: 0.9800, 召回率: 0.9800, F1: 0.9800, AUC: 0.9923
2025-07-15 20:06:15 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:06:15 - model_ensemble - INFO - 集成学习结果已保存到: output\ensemble_example\ensemble_results_20250715_200615.joblib
2025-07-15 20:06:15 - __main__ - ERROR - ❌ 基础集成学习 执行出错: ' font-family'
2025-07-15 20:06:15 - __main__ - INFO - 
开始执行: 自定义集成分类器
2025-07-15 20:06:15 - __main__ - INFO - ============================================================
2025-07-15 20:06:15 - __main__ - INFO - 示例2: 自定义集成分类器
2025-07-15 20:06:15 - __main__ - INFO - ============================================================
2025-07-15 20:06:15 - __main__ - INFO - 创建示例数据...
2025-07-15 20:06:15 - __main__ - INFO - 示例数据已保存到: sample_data.csv
2025-07-15 20:06:15 - __main__ - INFO - 数据形状: (1000, 21)
2025-07-15 20:06:15 - __main__ - INFO - 类别分布: [500 500]
2025-07-15 20:06:15 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:06:15 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:06:15 - model_training - INFO - 准确率: 0.9700
2025-07-15 20:06:15 - model_training - INFO - AUC: 0.9908
2025-07-15 20:06:15 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:15 - model_training - INFO - 
[[97  3]
 [ 3 97]]
2025-07-15 20:06:15 - model_training - INFO - 
分类报告:
2025-07-15 20:06:15 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.97      0.97       100
           1       0.97      0.97      0.97       100

    accuracy                           0.97       200
   macro avg       0.97      0.97      0.97       200
weighted avg       0.97      0.97      0.97       200

2025-07-15 20:06:15 - model_training - INFO - 训练时间: 0.24 秒
2025-07-15 20:06:15 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:06:15 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:06:15 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:06:15 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:06:15 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:06:15 - model_training - INFO - 准确率: 0.9750
2025-07-15 20:06:15 - model_training - INFO - AUC: 0.9879
2025-07-15 20:06:15 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:15 - model_training - INFO - 
[[96  4]
 [ 1 99]]
2025-07-15 20:06:15 - model_training - INFO - 
分类报告:
2025-07-15 20:06:15 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.99      0.96      0.97       100
           1       0.96      0.99      0.98       100

    accuracy                           0.97       200
   macro avg       0.98      0.97      0.97       200
weighted avg       0.98      0.97      0.97       200

2025-07-15 20:06:15 - model_training - INFO - 训练时间: 0.07 秒
2025-07-15 20:06:15 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:06:15 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:06:15 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:06:15 - __main__ - INFO - 测试集成方法: voting
2025-07-15 20:06:15 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:16 - __main__ - INFO -   voting 准确率: 0.9700
2025-07-15 20:06:16 - __main__ - INFO - 测试集成方法: bagging
2025-07-15 20:06:16 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-07-15 20:06:16 - __main__ - ERROR -   bagging 失败: BaggingClassifier.__init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 20:06:16 - __main__ - INFO - 测试集成方法: stacking
2025-07-15 20:06:16 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:06:17 - __main__ - INFO -   stacking 准确率: 0.9700
2025-07-15 20:06:17 - __main__ - INFO - ✅ 自定义集成分类器 执行成功
2025-07-15 20:06:17 - __main__ - INFO - 
开始执行: 完整分析管道
2025-07-15 20:06:17 - __main__ - INFO - ============================================================
2025-07-15 20:06:17 - __main__ - INFO - 示例3: 完整的二分类分析管道
2025-07-15 20:06:17 - __main__ - INFO - ============================================================
2025-07-15 20:06:17 - __main__ - INFO - 创建示例数据...
2025-07-15 20:06:17 - __main__ - INFO - 示例数据已保存到: sample_data.csv
2025-07-15 20:06:17 - __main__ - INFO - 数据形状: (1000, 21)
2025-07-15 20:06:17 - __main__ - INFO - 类别分布: [500 500]
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 初始化二分类分析流程
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 数据文件: sample_data.csv
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 输出目录: output\complete_pipeline
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 超参数调优: 禁用
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - SHAP分析: 禁用
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 🚀 开始完整的二分类分析流程
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 步骤1: 数据预处理
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 数据预处理完成
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 训练集: 800 样本, 20 特征
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 测试集: 200 样本
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 类别分布 - 训练集: 400:400
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 类别分布 - 测试集: 100:100
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 步骤2: 模型训练
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 训练模型: RandomForest
2025-07-15 20:06:18 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:06:18 - model_training - INFO - 准确率: 0.9700
2025-07-15 20:06:18 - model_training - INFO - AUC: 0.9908
2025-07-15 20:06:18 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:18 - model_training - INFO - 
[[97  3]
 [ 3 97]]
2025-07-15 20:06:18 - model_training - INFO - 
分类报告:
2025-07-15 20:06:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.97      0.97       100
           1       0.97      0.97      0.97       100

    accuracy                           0.97       200
   macro avg       0.97      0.97      0.97       200
weighted avg       0.97      0.97      0.97       200

2025-07-15 20:06:18 - model_training - INFO - 训练时间: 0.24 秒
2025-07-15 20:06:18 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:06:18 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:06:18 - binary_classification_pipeline - INFO -   RandomForest 训练完成
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 训练模型: XGBoost
2025-07-15 20:06:18 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:06:18 - model_training - INFO - 准确率: 0.9750
2025-07-15 20:06:18 - model_training - INFO - AUC: 0.9879
2025-07-15 20:06:18 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:18 - model_training - INFO - 
[[96  4]
 [ 1 99]]
2025-07-15 20:06:18 - model_training - INFO - 
分类报告:
2025-07-15 20:06:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.99      0.96      0.97       100
           1       0.96      0.99      0.98       100

    accuracy                           0.97       200
   macro avg       0.98      0.97      0.97       200
weighted avg       0.98      0.97      0.97       200

2025-07-15 20:06:18 - model_training - INFO - 训练时间: 0.07 秒
2025-07-15 20:06:18 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:06:18 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:06:18 - binary_classification_pipeline - INFO -   XGBoost 训练完成
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 训练模型: LightGBM
2025-07-15 20:06:18 - model_training - INFO - 模型名称: LightGBM
2025-07-15 20:06:18 - model_training - INFO - 准确率: 0.9700
2025-07-15 20:06:18 - model_training - INFO - AUC: 0.9894
2025-07-15 20:06:18 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:18 - model_training - INFO - 
[[96  4]
 [ 2 98]]
2025-07-15 20:06:18 - model_training - INFO - 
分类报告:
2025-07-15 20:06:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.98      0.96      0.97       100
           1       0.96      0.98      0.97       100

    accuracy                           0.97       200
   macro avg       0.97      0.97      0.97       200
weighted avg       0.97      0.97      0.97       200

2025-07-15 20:06:18 - model_training - INFO - 训练时间: 0.09 秒
2025-07-15 20:06:18 - model_training - INFO - 模型 LightGBM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 20:06:18 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 20:06:18 - binary_classification_pipeline - INFO -   LightGBM 训练完成
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 模型训练完成，成功训练 3 个模型
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 步骤3: 最佳模型选择
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:18 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-07-15 20:06:18 - best_model_selector - INFO - 加载模型训练结果...
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 计算综合性能指标...
2025-07-15 20:06:18 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-07-15 20:06:18 - best_model_selector - INFO - 使用 balanced 策略计算综合得分...
2025-07-15 20:06:18 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: XGBoost (得分: 0.9735)
2025-07-15 20:06:18 - best_model_selector - INFO - 模型选择结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-07-15 20:06:18 - best_model_selector - INFO - ============================================================
2025-07-15 20:06:18 - best_model_selector - INFO - 最佳模型推荐结果
2025-07-15 20:06:18 - best_model_selector - INFO - ============================================================
2025-07-15 20:06:18 - best_model_selector - INFO - 推荐策略: balanced
2025-07-15 20:06:18 - best_model_selector - INFO - 最佳模型: XGBoost
2025-07-15 20:06:18 - best_model_selector - INFO - 综合得分: 0.9735
2025-07-15 20:06:18 - best_model_selector - INFO - 
前三名模型:
2025-07-15 20:06:18 - best_model_selector - INFO -   1. XGBoost: 0.9735
2025-07-15 20:06:18 - best_model_selector - INFO -   2. Random Forest: 0.9692
2025-07-15 20:06:18 - best_model_selector - INFO -   3. LightGBM: 0.9690
2025-07-15 20:06:18 - best_model_selector - INFO -   4. KNN: 0.8641
2025-07-15 20:06:18 - best_model_selector - INFO -   5. Naive Bayes: 0.8464
2025-07-15 20:06:18 - best_model_selector - INFO - 
推荐 XGBoost 作为最佳模型的理由：

1. 在平衡策略下，该模型获得了最高的综合得分 0.9735
2. 在多个关键指标上表现均衡：
   - AUC-ROC: 0.9879
   - F1分数: 0.9754
   - MCC: 0.9504

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-07-15 20:06:18 - best_model_selector - INFO - ============================================================
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 最佳模型选择完成
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 推荐模型: XGBoost
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 综合得分: 0.9735
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 步骤4: 集成学习
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 使用 3 个模型进行集成学习
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 模型列表: ['RandomForest', 'XGBoost', 'LightGBM']
2025-07-15 20:06:18 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:18 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:06:18 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:18 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM']
2025-07-15 20:06:18 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:06:18 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:06:18 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:06:18 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:06:18 - model_training - INFO - 准确率: 0.9700
2025-07-15 20:06:18 - model_training - INFO - AUC: 0.9908
2025-07-15 20:06:18 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:18 - model_training - INFO - 
[[97  3]
 [ 3 97]]
2025-07-15 20:06:18 - model_training - INFO - 
分类报告:
2025-07-15 20:06:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.97      0.97       100
           1       0.97      0.97      0.97       100

    accuracy                           0.97       200
   macro avg       0.97      0.97      0.97       200
weighted avg       0.97      0.97      0.97       200

2025-07-15 20:06:18 - model_training - INFO - 训练时间: 0.23 秒
2025-07-15 20:06:18 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:06:18 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:06:18 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:06:18 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:06:18 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:06:18 - model_training - INFO - 准确率: 0.9750
2025-07-15 20:06:18 - model_training - INFO - AUC: 0.9879
2025-07-15 20:06:18 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:18 - model_training - INFO - 
[[96  4]
 [ 1 99]]
2025-07-15 20:06:18 - model_training - INFO - 
分类报告:
2025-07-15 20:06:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.99      0.96      0.97       100
           1       0.96      0.99      0.98       100

    accuracy                           0.97       200
   macro avg       0.98      0.97      0.97       200
weighted avg       0.98      0.97      0.97       200

2025-07-15 20:06:18 - model_training - INFO - 训练时间: 0.07 秒
2025-07-15 20:06:18 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:06:18 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:06:18 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:06:18 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:06:18 - model_training - INFO - 模型名称: LightGBM
2025-07-15 20:06:18 - model_training - INFO - 准确率: 0.9700
2025-07-15 20:06:18 - model_training - INFO - AUC: 0.9894
2025-07-15 20:06:18 - model_training - INFO - 混淆矩阵:
2025-07-15 20:06:18 - model_training - INFO - 
[[96  4]
 [ 2 98]]
2025-07-15 20:06:18 - model_training - INFO - 
分类报告:
2025-07-15 20:06:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.98      0.96      0.97       100
           1       0.96      0.98      0.97       100

    accuracy                           0.97       200
   macro avg       0.97      0.97      0.97       200
weighted avg       0.97      0.97      0.97       200

2025-07-15 20:06:18 - model_training - INFO - 训练时间: 0.14 秒
2025-07-15 20:06:18 - model_training - INFO - 模型 LightGBM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 20:06:18 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 20:06:18 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:06:18 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-15 20:06:18 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:06:18 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:06:18 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:19 - model_ensemble - INFO -     voting_soft - 准确率: 0.9800, F1: 0.9800
2025-07-15 20:06:19 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:06:19 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:19 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:06:19 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:06:19 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:06:21 - model_ensemble - INFO -   stacking - 准确率: 0.9800, F1: 0.9800
2025-07-15 20:06:21 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:21 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:06:21 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:21 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 20:06:21 - model_ensemble - INFO - 最佳F1分数: 0.9800
2025-07-15 20:06:21 - model_ensemble - INFO - 最佳准确率: 0.9800
2025-07-15 20:06:21 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:06:21 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9800, 精确率: 0.9800, 召回率: 0.9800, F1: 0.9800, AUC: 0.9922
2025-07-15 20:06:21 - model_ensemble - INFO -   stacking        - 准确率: 0.9800, 精确率: 0.9800, 召回率: 0.9800, F1: 0.9800, AUC: 0.9923
2025-07-15 20:06:21 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:06:21 - model_ensemble - INFO - 集成学习结果已保存到: output\complete_pipeline\ensemble\ensemble_results_20250715_200621.joblib
2025-07-15 20:06:21 - binary_classification_pipeline - ERROR - 集成学习过程出错: ' font-family'
2025-07-15 20:06:21 - binary_classification_pipeline - ERROR - 集成学习失败，分析终止
2025-07-15 20:06:21 - __main__ - ERROR - 完整分析管道执行失败
2025-07-15 20:06:21 - __main__ - ERROR - ❌ 完整分析管道 执行失败
2025-07-15 20:06:21 - __main__ - INFO - 
开始执行: 命令行使用方式
2025-07-15 20:06:21 - __main__ - INFO - ============================================================
2025-07-15 20:06:21 - __main__ - INFO - 示例4: 命令行使用方式
2025-07-15 20:06:21 - __main__ - INFO - ============================================================
2025-07-15 20:06:21 - __main__ - INFO - 创建示例数据...
2025-07-15 20:06:21 - __main__ - INFO - 示例数据已保存到: sample_data.csv
2025-07-15 20:06:21 - __main__ - INFO - 数据形状: (1000, 21)
2025-07-15 20:06:21 - __main__ - INFO - 类别分布: [500 500]
2025-07-15 20:06:21 - __main__ - INFO - 命令行使用示例:
2025-07-15 20:06:21 - __main__ - INFO - 1. 单数据源集成学习:
2025-07-15 20:06:21 - __main__ - INFO -    python code/main.py --mode ensemble --model "RandomForest,XGBoost,LightGBM" --data sample_data.csv
2025-07-15 20:06:21 - __main__ - INFO - 
2. 多数据源集成学习:
2025-07-15 20:06:21 - __main__ - INFO -    python code/main.py --mode multi_data_ensemble --model_data_config config.json
2025-07-15 20:06:21 - __main__ - INFO - 
3. 完整二分类分析（包含集成学习）:
2025-07-15 20:06:21 - __main__ - INFO -    python code/binary_classification_pipeline.py --data sample_data.csv --models "RandomForest,XGBoost" --enable_ensemble
2025-07-15 20:06:21 - __main__ - INFO - 
4. GUI界面使用:
2025-07-15 20:06:21 - __main__ - INFO -    python gui_main.py
2025-07-15 20:06:21 - __main__ - INFO -    然后在"集成学习"选项卡中进行操作
2025-07-15 20:06:21 - __main__ - INFO - ✅ 命令行使用方式 执行成功
2025-07-15 20:06:21 - __main__ - INFO - ============================================================
2025-07-15 20:06:21 - __main__ - INFO - 示例执行结果总结
2025-07-15 20:06:21 - __main__ - INFO - ============================================================
2025-07-15 20:06:21 - __main__ - INFO - 基础集成学习: ❌ 失败
2025-07-15 20:06:21 - __main__ - INFO - 自定义集成分类器: ✅ 成功
2025-07-15 20:06:21 - __main__ - INFO - 完整分析管道: ❌ 失败
2025-07-15 20:06:21 - __main__ - INFO - 命令行使用方式: ✅ 成功
2025-07-15 20:06:21 - __main__ - INFO - 
总体结果: 2/4 个示例成功
2025-07-15 20:06:21 - __main__ - WARNING - ⚠️ 部分示例执行失败，请检查相关功能
2025-07-15 20:06:21 - __main__ - INFO - 临时文件已清理
