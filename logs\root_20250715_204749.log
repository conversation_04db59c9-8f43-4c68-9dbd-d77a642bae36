2025-07-15 20:47:51 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:47:51 - GUI - INFO - GUI界面初始化完成
2025-07-15 20:48:27 - model_ensemble - INFO - ============================================================
2025-07-15 20:48:27 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:48:27 - model_ensemble - INFO - ============================================================
2025-07-15 20:48:27 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'SVM', 'KNN']
2025-07-15 20:48:27 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:48:27 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:48:27 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:48:27 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:48:27 - model_training - INFO - 准确率: 0.9000
2025-07-15 20:48:27 - model_training - INFO - AUC: 0.9386
2025-07-15 20:48:27 - model_training - INFO - 混淆矩阵:
2025-07-15 20:48:27 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-15 20:48:27 - model_training - INFO - 
分类报告:
2025-07-15 20:48:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-15 20:48:27 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 20:48:27 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:48:27 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:48:27 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:48:27 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:48:27 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:48:27 - model_training - INFO - 准确率: 0.8750
2025-07-15 20:48:27 - model_training - INFO - AUC: 0.9668
2025-07-15 20:48:27 - model_training - INFO - 混淆矩阵:
2025-07-15 20:48:27 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 20:48:27 - model_training - INFO - 
分类报告:
2025-07-15 20:48:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 20:48:27 - model_training - INFO - 训练时间: 0.06 秒
2025-07-15 20:48:27 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:48:27 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:48:27 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:48:27 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 20:48:27 - model_training - INFO - 模型名称: SVM
2025-07-15 20:48:27 - model_training - INFO - 准确率: 0.8000
2025-07-15 20:48:27 - model_training - INFO - AUC: 0.9207
2025-07-15 20:48:27 - model_training - INFO - 混淆矩阵:
2025-07-15 20:48:27 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-07-15 20:48:27 - model_training - INFO - 
分类报告:
2025-07-15 20:48:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-07-15 20:48:27 - model_training - INFO - 训练时间: 0.03 秒
2025-07-15 20:48:27 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-15 20:48:27 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-15 20:48:27 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 20:48:27 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-15 20:48:27 - model_training - INFO - 模型名称: KNN
2025-07-15 20:48:27 - model_training - INFO - 准确率: 0.8750
2025-07-15 20:48:27 - model_training - INFO - AUC: 0.9322
2025-07-15 20:48:27 - model_training - INFO - 混淆矩阵:
2025-07-15 20:48:27 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 20:48:27 - model_training - INFO - 
分类报告:
2025-07-15 20:48:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 20:48:28 - model_training - INFO - 训练时间: 0.02 秒
2025-07-15 20:48:28 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-15 20:48:28 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-15 20:48:28 - model_ensemble - INFO -   KNN 训练完成
2025-07-15 20:48:28 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:48:28 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:48:28 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:48:28 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:48:28 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:48:28 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:48:28 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:48:28 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:48:28 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:48:28 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:48:29 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:48:29 - model_ensemble - INFO - ============================================================
2025-07-15 20:48:29 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:48:29 - model_ensemble - INFO - ============================================================
2025-07-15 20:48:29 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 20:48:29 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 20:48:29 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 20:48:29 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:48:29 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9540
2025-07-15 20:48:29 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9565
2025-07-15 20:48:29 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:48:29 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_204829.joblib
