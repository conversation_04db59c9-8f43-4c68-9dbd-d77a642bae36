2025-07-15 22:06:46 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:06:46 - GUI - INFO - GUI界面初始化完成
2025-07-15 22:07:24 - model_ensemble - INFO - ============================================================
2025-07-15 22:07:24 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:07:24 - model_ensemble - INFO - ============================================================
2025-07-15 22:07:24 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic', 'SVM']
2025-07-15 22:07:24 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 22:07:24 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:07:24 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:07:24 - model_training - INFO - 模型名称: Random Forest
2025-07-15 22:07:24 - model_training - INFO - 准确率: 0.6667
2025-07-15 22:07:24 - model_training - INFO - AUC: 0.7438
2025-07-15 22:07:24 - model_training - INFO - 混淆矩阵:
2025-07-15 22:07:24 - model_training - INFO - 
[[5 3]
 [3 7]]
2025-07-15 22:07:24 - model_training - INFO - 
分类报告:
2025-07-15 22:07:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.62      0.62      0.62         8
           1       0.70      0.70      0.70        10

    accuracy                           0.67        18
   macro avg       0.66      0.66      0.66        18
weighted avg       0.67      0.67      0.67        18

2025-07-15 22:07:24 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 22:07:24 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 22:07:24 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 22:07:24 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:07:24 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:07:24 - model_training - INFO - 模型名称: XGBoost
2025-07-15 22:07:24 - model_training - INFO - 准确率: 0.5556
2025-07-15 22:07:24 - model_training - INFO - AUC: 0.5500
2025-07-15 22:07:24 - model_training - INFO - 混淆矩阵:
2025-07-15 22:07:24 - model_training - INFO - 
[[5 3]
 [5 5]]
2025-07-15 22:07:24 - model_training - INFO - 
分类报告:
2025-07-15 22:07:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.50      0.62      0.56         8
           1       0.62      0.50      0.56        10

    accuracy                           0.56        18
   macro avg       0.56      0.56      0.56        18
weighted avg       0.57      0.56      0.56        18

2025-07-15 22:07:24 - model_training - INFO - 训练时间: 0.09 秒
2025-07-15 22:07:24 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 22:07:24 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 22:07:24 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:07:24 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 22:07:26 - model_training - INFO - 模型名称: LightGBM
2025-07-15 22:07:26 - model_training - INFO - 准确率: 0.5556
2025-07-15 22:07:26 - model_training - INFO - AUC: 0.6188
2025-07-15 22:07:26 - model_training - INFO - 混淆矩阵:
2025-07-15 22:07:26 - model_training - INFO - 
[[4 4]
 [4 6]]
2025-07-15 22:07:26 - model_training - INFO - 
分类报告:
2025-07-15 22:07:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.50      0.50      0.50         8
           1       0.60      0.60      0.60        10

    accuracy                           0.56        18
   macro avg       0.55      0.55      0.55        18
weighted avg       0.56      0.56      0.56        18

2025-07-15 22:07:26 - model_training - INFO - 训练时间: 1.61 秒
2025-07-15 22:07:26 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 22:07:26 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 22:07:26 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 22:07:26 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 22:07:26 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 22:07:26 - model_training - INFO - 准确率: 0.7222
2025-07-15 22:07:26 - model_training - INFO - AUC: 0.8000
2025-07-15 22:07:26 - model_training - INFO - 混淆矩阵:
2025-07-15 22:07:26 - model_training - INFO - 
[[7 1]
 [4 6]]
2025-07-15 22:07:26 - model_training - INFO - 
分类报告:
2025-07-15 22:07:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.64      0.88      0.74         8
           1       0.86      0.60      0.71        10

    accuracy                           0.72        18
   macro avg       0.75      0.74      0.72        18
weighted avg       0.76      0.72      0.72        18

2025-07-15 22:07:26 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:07:26 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 22:07:26 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 22:07:26 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 22:07:26 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:07:26 - model_training - INFO - 模型名称: SVM
2025-07-15 22:07:26 - model_training - INFO - 准确率: 0.6667
2025-07-15 22:07:26 - model_training - INFO - AUC: 0.7125
2025-07-15 22:07:26 - model_training - INFO - 混淆矩阵:
2025-07-15 22:07:26 - model_training - INFO - 
[[7 1]
 [5 5]]
2025-07-15 22:07:26 - model_training - INFO - 
分类报告:
2025-07-15 22:07:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.58      0.88      0.70         8
           1       0.83      0.50      0.62        10

    accuracy                           0.67        18
   macro avg       0.71      0.69      0.66        18
weighted avg       0.72      0.67      0.66        18

2025-07-15 22:07:26 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:07:26 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-15 22:07:26 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-15 22:07:26 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:07:26 - model_ensemble - INFO - 成功训练了 5 个基础模型
2025-07-15 22:07:26 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:07:26 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:07:26 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:07:26 - model_ensemble - INFO -     voting_soft - 准确率: 0.6667, F1: 0.6667
2025-07-15 22:07:26 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:07:26 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:07:27 - model_ensemble - INFO -     voting_hard - 准确率: 0.7222, F1: 0.7196
2025-07-15 22:07:27 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:07:27 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:07:29 - model_ensemble - INFO -   stacking - 准确率: 0.5000, F1: 0.4749
2025-07-15 22:07:29 - model_ensemble - INFO - ============================================================
2025-07-15 22:07:29 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:07:29 - model_ensemble - INFO - ============================================================
2025-07-15 22:07:29 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 22:07:29 - model_ensemble - INFO - 最佳F1分数: 0.7196
2025-07-15 22:07:29 - model_ensemble - INFO - 最佳准确率: 0.7222
2025-07-15 22:07:29 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:07:29 - model_ensemble - INFO -   voting_soft     - 准确率: 0.6667, 精确率: 0.6833, 召回率: 0.6667, F1: 0.6667, AUC: 0.6500
2025-07-15 22:07:29 - model_ensemble - INFO -   voting_hard     - 准确率: 0.7222, 精确率: 0.7590, 召回率: 0.7222, F1: 0.7196, AUC: 0.0000
2025-07-15 22:07:29 - model_ensemble - INFO -   stacking        - 准确率: 0.5000, 精确率: 0.4769, 召回率: 0.5000, F1: 0.4749, AUC: 0.6625
2025-07-15 22:07:29 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:07:29 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_220729.joblib
2025-07-15 22:07:29 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:07:29 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-15 22:07:29 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:07:29 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:07:29 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:07:29 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.10455456,  0.10448747,  0.03769268])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.10455456, -0.10448747, -0.03769268])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.09920864, -0.01326027, -0.04277323])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.09920864,  0.01326027,  0.04277323])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.0701483 ,  0.09725644, -0.00275367])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.0701483 , -0.09725644,  0.00275366])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.08611156,  0.06912065, -0.11383825])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.08611156, -0.06912065,  0.11383825])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.06785474,  0.10046479, -0.04534192])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.06785474, -0.10046479,  0.04534192])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.00120082,  0.12742595, -0.03030025])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.00120081, -0.12742595,  0.03030025])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([0.09561293, 0.10783175, 0.03601099])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.09561293, -0.10783175, -0.03601099])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.119692  , -0.09410642,  0.0749941 ])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.119692  ,  0.09410642, -0.0749941 ])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.11194326, -0.01690852, -0.03255911])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.11194326,  0.01690852,  0.03255912])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.02987677, -0.14750983, -0.006314  ])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.02987677,  0.14750983,  0.006314  ])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.04380133, -0.18630781, -0.00269305])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([0.04380133, 0.18630781, 0.00269305])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.1168103 , -0.03202708,  0.10011913])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.1168103 ,  0.03202708, -0.10011913])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.08320978, -0.01216847, -0.02439597])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([0.08320978, 0.01216847, 0.02439597])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.05547063, -0.17328926,  0.03908848])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.05547063,  0.17328927, -0.03908848])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.11015146, -0.16457791,  0.00195042])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.11015146,  0.16457791, -0.00195042])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.03889319,  0.10318668, -0.00385242])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.03889318, -0.10318668,  0.00385242])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.14319146,  0.05956208, -0.06268317])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.14319146, -0.05956208,  0.06268317])
2025-07-15 22:07:29 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([-0.02583232,  0.07081975,  0.07764926])
2025-07-15 22:07:29 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:29 - shap - INFO - phi = array([ 0.02583232, -0.07081976, -0.07764926])
2025-07-15 22:07:32 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_soft
2025-07-15 22:07:32 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:07:32 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:07:32 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:07:32 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -     dependence: 3 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:07:32 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:07:32 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([ 0.24074074, -0.34259259, -0.28703704])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([-0.34259259, -0.06481481,  0.01851852])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([ 0.02777778, -0.36111111, -0.05555556])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([-0.24074074, -0.26851852,  0.12037037])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([-0.15740741, -0.21296296, -0.01851852])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([-0.18518519, -0.21296296,  0.00925926])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([-0.10185185, -0.21296296, -0.07407407])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([ 0.2962963 ,  0.37962963, -0.06481481])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([-0.36111111,  0.        , -0.02777778])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:32 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:32 - shap - INFO - phi = array([0.06481481, 0.34259259, 0.2037037 ])
2025-07-15 22:07:32 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:33 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:33 - shap - INFO - phi = array([0.10185185, 0.32407407, 0.18518519])
2025-07-15 22:07:33 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:33 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:33 - shap - INFO - phi = array([-0.34259259,  0.0462963 , -0.09259259])
2025-07-15 22:07:33 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:33 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:33 - shap - INFO - phi = array([0.2962963 , 0.26851852, 0.0462963 ])
2025-07-15 22:07:33 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:33 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:33 - shap - INFO - phi = array([-0.03703704,  0.60185185,  0.0462963 ])
2025-07-15 22:07:33 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:33 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:33 - shap - INFO - phi = array([0.19444444, 0.30555556, 0.11111111])
2025-07-15 22:07:33 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:33 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:33 - shap - INFO - phi = array([-0.08333333, -0.30555556,  0.        ])
2025-07-15 22:07:33 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:33 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:33 - shap - INFO - phi = array([ 0.5462963 , -0.06481481,  0.12962963])
2025-07-15 22:07:33 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:33 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:33 - shap - INFO - phi = array([ 0.08333333, -0.22222222, -0.25      ])
2025-07-15 22:07:36 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_hard
2025-07-15 22:07:36 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:07:36 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:07:36 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:07:36 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -     dependence: 3 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:07:36 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:07:36 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 22:07:36 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.04932368,  0.05936656,  0.02059408])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.04932368, -0.05936656, -0.02059408])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.06628557, -0.01115107, -0.01869866])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.06628557,  0.01115107,  0.01869866])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.06548771,  0.05234389, -0.00033908])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.06548771, -0.05234389,  0.00033908])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.05190742,  0.03016245, -0.09413059])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.05190742, -0.03016245,  0.09413059])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.05477112,  0.05762619, -0.01618455])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.05477112, -0.05762619,  0.01618455])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.029745  ,  0.07014913, -0.02679047])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.029745  , -0.07014913,  0.02679047])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([0.06406733, 0.05726015, 0.01631058])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.06406733, -0.05726015, -0.01631058])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.0794484 , -0.04028912,  0.03885675])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.0794484 ,  0.04028912, -0.03885675])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.07023959, -0.01739054, -0.0263955 ])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.07023959,  0.01739054,  0.0263955 ])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.04234278, -0.08596141,  0.01248188])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.04234278,  0.08596141, -0.01248188])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.0336816 , -0.11507142,  0.01167951])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.0336816 ,  0.11507142, -0.01167951])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.07136561, -0.01428032,  0.05771255])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.07136561,  0.01428032, -0.05771255])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.05576533, -0.01233001, -0.02378234])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([0.05576533, 0.01233001, 0.02378234])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.04951627, -0.0860138 ,  0.03285808])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.04951627,  0.0860138 , -0.03285808])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.07142271, -0.07992118,  0.01567491])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.07142271,  0.07992118, -0.01567491])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.01434622,  0.05446264, -0.00481003])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.01434622, -0.05446264,  0.00481003])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.08299967,  0.02955648, -0.04208499])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.08299967, -0.02955648,  0.04208499])
2025-07-15 22:07:36 - shap - INFO - num_full_subsets = 1
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([-0.01696783,  0.05148139,  0.0470479 ])
2025-07-15 22:07:36 - shap - INFO - np.sum(w_aug) = 3.0
2025-07-15 22:07:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:07:36 - shap - INFO - phi = array([ 0.01696783, -0.05148139, -0.0470479 ])
2025-07-15 22:07:39 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-15 22:07:39 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:07:39 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:07:39 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:07:39 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -     dependence: 3 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:07:50 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:07:50 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-15 22:07:50 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
2025-07-15 22:12:57 - model_ensemble - INFO - ============================================================
2025-07-15 22:12:57 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:12:57 - model_ensemble - INFO - ============================================================
2025-07-15 22:12:57 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic', 'SVM']
2025-07-15 22:12:57 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 22:12:57 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:12:57 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:12:58 - model_training - INFO - 模型名称: Random Forest
2025-07-15 22:12:58 - model_training - INFO - 准确率: 0.9000
2025-07-15 22:12:58 - model_training - INFO - AUC: 0.9386
2025-07-15 22:12:58 - model_training - INFO - 混淆矩阵:
2025-07-15 22:12:58 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-15 22:12:58 - model_training - INFO - 
分类报告:
2025-07-15 22:12:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-15 22:12:58 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 22:12:58 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 22:12:58 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 22:12:58 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:12:58 - model_training - INFO - 模型名称: XGBoost
2025-07-15 22:12:58 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:12:58 - model_training - INFO - AUC: 0.9668
2025-07-15 22:12:58 - model_training - INFO - 混淆矩阵:
2025-07-15 22:12:58 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:12:58 - model_training - INFO - 
分类报告:
2025-07-15 22:12:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:12:58 - model_training - INFO - 训练时间: 0.03 秒
2025-07-15 22:12:58 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 22:12:58 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 22:12:58 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 22:12:58 - model_training - INFO - 模型名称: LightGBM
2025-07-15 22:12:58 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:12:58 - model_training - INFO - AUC: 0.9463
2025-07-15 22:12:58 - model_training - INFO - 混淆矩阵:
2025-07-15 22:12:58 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:12:58 - model_training - INFO - 
分类报告:
2025-07-15 22:12:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:12:58 - model_training - INFO - 训练时间: 0.21 秒
2025-07-15 22:12:58 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 22:12:58 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 22:12:58 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 22:12:58 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 22:12:58 - model_training - INFO - 准确率: 0.8500
2025-07-15 22:12:58 - model_training - INFO - AUC: 0.9284
2025-07-15 22:12:58 - model_training - INFO - 混淆矩阵:
2025-07-15 22:12:58 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-07-15 22:12:58 - model_training - INFO - 
分类报告:
2025-07-15 22:12:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-07-15 22:12:58 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:12:58 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 22:12:58 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 22:12:58 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:12:58 - model_training - INFO - 模型名称: SVM
2025-07-15 22:12:58 - model_training - INFO - 准确率: 0.8000
2025-07-15 22:12:58 - model_training - INFO - AUC: 0.9207
2025-07-15 22:12:58 - model_training - INFO - 混淆矩阵:
2025-07-15 22:12:58 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-07-15 22:12:58 - model_training - INFO - 
分类报告:
2025-07-15 22:12:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-07-15 22:12:58 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:12:58 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-15 22:12:58 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-15 22:12:58 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 成功训练了 5 个基础模型
2025-07-15 22:12:58 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:12:58 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:12:58 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:12:58 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:12:58 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:12:58 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:12:58 - model_ensemble - INFO -     voting_hard - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:12:58 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:12:58 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:13:00 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:13:00 - model_ensemble - INFO - ============================================================
2025-07-15 22:13:00 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:13:00 - model_ensemble - INFO - ============================================================
2025-07-15 22:13:00 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 22:13:00 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 22:13:00 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 22:13:00 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:13:00 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9616
2025-07-15 22:13:00 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.0000
2025-07-15 22:13:00 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9540
2025-07-15 22:13:00 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:13:00 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_221300.joblib
2025-07-15 22:13:00 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-15 22:13:00 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:13:00 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:13:00 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:13:00 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:13:00 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.1143805 ,  0.11895613, -0.01381257, -0.02084557,  0.00161984,
        0.11554663,  0.02441236, -0.00922988])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.1143805 , -0.11895613,  0.01381257,  0.02084557, -0.00161984,
       -0.11554663, -0.02441236,  0.00922988])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.12370736, -0.09945416, -0.02911763, -0.07308076,  0.01841787,
        0.0222512 , -0.03575113, -0.01131847])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.12370736,  0.09945416,  0.02911763,  0.07308076, -0.01841787,
       -0.0222512 ,  0.03575113,  0.01131847])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.10864255, -0.09548812, -0.01902005, -0.02678691, -0.03448488,
       -0.29980272,  0.00204704, -0.01481918])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.10864255,  0.09548812,  0.01902005,  0.02678691,  0.03448488,
        0.29980272, -0.00204704,  0.01481918])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.15397921, -0.02191182,  0.01289062, -0.05408358, -0.04077611,
       -0.30229769,  0.00890657, -0.02016108])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.15397921,  0.02191182, -0.01289062,  0.05408358,  0.04077611,
        0.30229769, -0.00890657,  0.02016108])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.12236092,  0.13103394, -0.01623863, -0.03192724, -0.04208037,
       -0.03113944,  0.1210479 , -0.00199441])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.12236092, -0.13103394,  0.01623863,  0.03192724,  0.04208038,
        0.03113944, -0.1210479 ,  0.00199441])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.08006056,  0.134343  ,  0.00767424,  0.10212519,  0.05297406,
       -0.00510197, -0.0214801 ,  0.03091261])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.08006056, -0.134343  , -0.00767424, -0.10212519, -0.05297406,
        0.00510197,  0.0214801 , -0.03091261])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.17155585, -0.06742606, -0.00855645, -0.04378548,  0.00048808,
        0.24146825, -0.01775584, -0.00935407])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.17155585,  0.06742606,  0.00855645,  0.04378548, -0.00048808,
       -0.24146825,  0.01775584,  0.00935407])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.15781595, -0.08253486, -0.01125253, -0.05204048, -0.04174166,
        0.08020619,  0.06891846, -0.01928196])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.15781595,  0.08253486,  0.01125253,  0.05204048,  0.04174166,
       -0.08020619, -0.06891846,  0.01928196])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.20416238, -0.21790985,  0.0073286 , -0.07254315, -0.03629796,
       -0.00786195,  0.00971996, -0.02688374])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.20416238,  0.21790985, -0.0073286 ,  0.07254315,  0.03629796,
        0.00786195, -0.00971996,  0.02688374])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.06851135,  0.18634895,  0.00128227, -0.02554785,  0.02982823,
       -0.38705983, -0.02707456, -0.0021894 ])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.06851135, -0.18634895, -0.00128227,  0.02554785, -0.02982823,
        0.38705983,  0.02707456,  0.0021894 ])
2025-07-15 22:13:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([ 0.06178581,  0.18316672,  0.00710044, -0.04268686, -0.00165846,
        0.17924757,  0.00527068, -0.01223438])
2025-07-15 22:13:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:01 - shap - INFO - phi = array([-0.06178581, -0.18316672, -0.00710045,  0.04268686,  0.00165846,
       -0.17924757, -0.00527068,  0.01223438])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.0998264 ,  0.14556141, -0.00030447, -0.04085447, -0.00314398,
        0.20698218, -0.00924366, -0.0096917 ])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.0998264 , -0.14556141,  0.00030447,  0.04085447,  0.00314398,
       -0.20698218,  0.00924366,  0.0096917 ])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.15529784, -0.25922229, -0.03718992, -0.04969837, -0.04781518,
        0.00641286,  0.05008497, -0.03383468])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.15529784,  0.25922229,  0.03718992,  0.04969837,  0.04781518,
       -0.00641286, -0.05008497,  0.03383468])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.10441159, -0.14855519, -0.02014903, -0.03352991, -0.04215351,
       -0.25593974,  0.02460603, -0.01778813])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.10441159,  0.14855519,  0.02014903,  0.03352991,  0.04215351,
        0.25593974, -0.02460603,  0.01778813])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.18861927, -0.19789035,  0.00086515, -0.05159961, -0.02161372,
        0.07213019,  0.01590982, -0.02182802])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.18861927,  0.19789035, -0.00086515,  0.05159961,  0.02161372,
       -0.07213019, -0.01590982,  0.02182802])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.14240873, -0.05949354,  0.01523769,  0.10364004,  0.06361731,
        0.01669343, -0.02203504,  0.02336232])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.14240873,  0.05949354, -0.01523769, -0.10364004, -0.06361731,
       -0.01669343,  0.02203504, -0.02336232])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.11310423,  0.1570913 , -0.0094681 , -0.02704606, -0.0176967 ,
        0.14530337,  0.01946193, -0.00820661])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.11310423, -0.1570913 ,  0.0094681 ,  0.02704606,  0.0176967 ,
       -0.14530337, -0.01946193,  0.00820661])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.11080086,  0.19305384, -0.01441563, -0.00438158,  0.02029358,
        0.0857798 ,  0.00549146, -0.00916822])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.11080086, -0.19305384,  0.01441563,  0.00438158, -0.02029358,
       -0.0857798 , -0.00549146,  0.00916822])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.16107236, -0.08945997,  0.01355777,  0.05397133,  0.06037134,
        0.03897891, -0.02234338, -0.01598142])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.16107236,  0.08945997, -0.01355777, -0.05397133, -0.06037134,
       -0.03897891,  0.02234338,  0.01598142])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.09806595, -0.17329439,  0.02612006,  0.07126118, -0.023925  ,
       -0.00153142, -0.03691116,  0.05086432])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.09806595,  0.17329439, -0.02612006, -0.07126118,  0.023925  ,
        0.00153142,  0.03691116, -0.05086432])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.1215665 , -0.11957507,  0.02190376,  0.15349475,  0.03169163,
       -0.15818892, -0.02858837,  0.02274952])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.1215665 ,  0.11957507, -0.02190376, -0.15349475, -0.03169163,
        0.15818892,  0.02858837, -0.02274952])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([-0.04907861,  0.1121615 ,  0.02564069,  0.16480126,  0.06428793,
       -0.0342051 , -0.0270767 ,  0.02751141])
2025-07-15 22:13:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:02 - shap - INFO - phi = array([ 0.04907861, -0.11216149, -0.02564069, -0.16480126, -0.06428793,
        0.0342051 ,  0.0270767 , -0.02751141])
2025-07-15 22:13:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 0.09169154,  0.1611879 ,  0.00635291,  0.09552057,  0.05590124,
        0.00541818, -0.02536223, -0.00701793])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-0.09169154, -0.1611879 , -0.00635291, -0.09552057, -0.05590124,
       -0.00541818,  0.02536223,  0.00701793])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 1.06863295e-01,  1.84638398e-01, -5.92505130e-03, -3.14427690e-02,
       -9.97183815e-05,  1.16220387e-01,  1.81419436e-02, -7.25077082e-03])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-1.06863295e-01, -1.84638397e-01,  5.92505144e-03,  3.14427693e-02,
        9.97183289e-05, -1.16220387e-01, -1.81419427e-02,  7.25077180e-03])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-0.1268189 , -0.10718396, -0.01622175, -0.0376351 , -0.04240907,
       -0.27691749,  0.02313398, -0.01547626])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 0.1268189 ,  0.10718396,  0.01622175,  0.0376351 ,  0.04240907,
        0.27691749, -0.02313398,  0.01547626])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 0.08350222,  0.12898793,  0.00674058,  0.08615096,  0.05647502,
        0.02083359, -0.02082605,  0.02357856])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-0.08350222, -0.12898793, -0.00674058, -0.08615096, -0.05647502,
       -0.02083359,  0.02082605, -0.02357856])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-0.16876368, -0.14601811,  0.01477286, -0.06725891, -0.04313052,
       -0.15366578,  0.0045039 , -0.01502612])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 0.16876368,  0.14601811, -0.01477286,  0.06725891,  0.04313052,
        0.15366578, -0.0045039 ,  0.01502612])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 0.12855955,  0.12509471, -0.01913504, -0.00395017,  0.02568844,
        0.10763499,  0.00154308, -0.00623703])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-0.12855955, -0.12509471,  0.01913504,  0.00395017, -0.02568844,
       -0.10763499, -0.00154309,  0.00623703])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 0.04625146, -0.1042944 ,  0.00154528,  0.00166772, -0.04331102,
       -0.4386064 ,  0.00796805, -0.0144879 ])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-0.04625146,  0.1042944 , -0.00154528, -0.00166772,  0.04331102,
        0.4386064 , -0.00796805,  0.0144879 ])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-0.24813422, -0.22482681,  0.0087587 , -0.07375857, -0.02995882,
        0.1620411 ,  0.01076294, -0.02997934])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 0.24813422,  0.22482681, -0.0087587 ,  0.07375857,  0.02995882,
       -0.1620411 , -0.01076294,  0.02997934])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 0.10310392,  0.1579337 , -0.0064888 , -0.03337214,  0.00056329,
        0.1841741 , -0.00875847, -0.0032649 ])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-0.10310392, -0.1579337 ,  0.0064888 ,  0.03337214, -0.00056329,
       -0.1841741 ,  0.00875847,  0.0032649 ])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([-0.16070998, -0.26542446, -0.02771952, -0.06609642, -0.04233356,
        0.11686129,  0.00915301, -0.02822114])
2025-07-15 22:13:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:03 - shap - INFO - phi = array([ 0.16070998,  0.26542446,  0.02771952,  0.06609642,  0.04233356,
       -0.11686129, -0.00915301,  0.02822114])
2025-07-15 22:13:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([-0.06727086, -0.07036563,  0.03781777,  0.20936515,  0.03807104,
       -0.04772136, -0.02946887,  0.09733768])
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([ 0.06727086,  0.07036563, -0.03781777, -0.20936515, -0.03807104,
        0.04772136,  0.02946887, -0.09733768])
2025-07-15 22:13:04 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([-0.05990708,  0.23861232,  0.01822261,  0.00034317,  0.03819424,
        0.06289189, -0.01999393, -0.0130599 ])
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([ 0.05990708, -0.23861232, -0.01822261, -0.00034317, -0.03819424,
       -0.06289189,  0.01999393,  0.0130599 ])
2025-07-15 22:13:04 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([-0.21792915, -0.14064101,  0.00681367, -0.06150298, -0.02926696,
        0.2299729 , -0.01991917,  0.03031445])
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([ 0.21792915,  0.14064101, -0.00681367,  0.06150298,  0.02926696,
       -0.2299729 ,  0.01991917, -0.03031445])
2025-07-15 22:13:04 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([ 0.07995804,  0.08919108,  0.00675303, -0.04787328, -0.00423187,
        0.25368633, -0.01651555,  0.01088746])
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([-0.07995804, -0.08919108, -0.00675303,  0.04787328,  0.00423187,
       -0.25368633,  0.01651555, -0.01088746])
2025-07-15 22:13:04 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([-0.21869757,  0.17787128,  0.00877123, -0.04505818, -0.00337912,
        0.15714387, -0.01044623,  0.03352712])
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([ 0.21869757, -0.17787128, -0.00877123,  0.04505818,  0.00337912,
       -0.15714387,  0.01044623, -0.03352712])
2025-07-15 22:13:04 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([ 0.09127332,  0.07529282,  0.0082734 ,  0.10247515,  0.06210352,
        0.02394546, -0.02529612,  0.03512804])
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([-0.09127332, -0.07529282, -0.0082734 , -0.10247515, -0.06210352,
       -0.02394546,  0.02529612, -0.03512805])
2025-07-15 22:13:04 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([ 0.06632821, -0.11187348,  0.00711673, -0.00685514, -0.04379781,
       -0.42249586,  0.01520049, -0.00977547])
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([-0.06632821,  0.11187348, -0.00711673,  0.00685514,  0.04379781,
        0.42249586, -0.01520049,  0.00977547])
2025-07-15 22:13:04 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([ 0.12713292,  0.1023166 , -0.0165249 , -0.01957492,  0.01471935,
        0.170711  , -0.021438  ,  0.0075886 ])
2025-07-15 22:13:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:04 - shap - INFO - phi = array([-0.12713292, -0.1023166 ,  0.0165249 ,  0.01957492, -0.01471935,
       -0.170711  ,  0.021438  , -0.0075886 ])
2025-07-15 22:13:08 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_soft
2025-07-15 22:13:08 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:13:08 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:13:08 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:13:08 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:13:08 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:13:08 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:13:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:08 - shap - INFO - phi = array([-0.15002976, -0.12431548,  0.02407738,  0.02991071,  0.00294643,
       -0.1641369 , -0.02330357,  0.00485119])
2025-07-15 22:13:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:08 - shap - INFO - phi = array([-0.12785714,  0.14636905,  0.08339286,  0.19339286,  0.02440476,
        0.03821429,  0.12761905,  0.11446429])
2025-07-15 22:13:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:08 - shap - INFO - phi = array([ 0.12458333,  0.09494048,  0.01220238,  0.03107143,  0.02767857,
        0.30869048, -0.00958333,  0.01041667])
2025-07-15 22:13:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:08 - shap - INFO - phi = array([ 0.16324405,  0.03574405, -0.00949405,  0.04360119,  0.02342262,
        0.3433631 , -0.01580357,  0.01592262])
2025-07-15 22:13:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:08 - shap - INFO - phi = array([-0.19806548, -0.16014881,  0.0216369 ,  0.04258929,  0.02705357,
       -0.0196131 , -0.08324405, -0.03020833])
2025-07-15 22:13:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:09 - shap - INFO - phi = array([-0.08791667, -0.13089286, -0.00797619, -0.08678571, -0.04619048,
       -0.02339286,  0.01928571, -0.03613095])
2025-07-15 22:13:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:09 - shap - INFO - phi = array([-0.29714286,  0.04470238,  0.00285714,  0.0397619 ,  0.01166667,
       -0.20916667,  0.00702381,  0.00029762])
2025-07-15 22:13:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:09 - shap - INFO - phi = array([-0.27883929,  0.01848214,  0.00598214,  0.04044643,  0.02366071,
       -0.18330357, -0.02717262,  0.00074405])
2025-07-15 22:13:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:09 - shap - INFO - phi = array([ 0.30625   ,  0.28392857, -0.01767857,  0.0347619 ,  0.01916667,
       -0.03619048, -0.01238095,  0.02214286])
2025-07-15 22:13:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:09 - shap - INFO - phi = array([-0.01767857, -0.17660714,  0.07244048,  0.03255952, -0.03119048,
        0.51875   ,  0.11761905,  0.08410714])
2025-07-15 22:13:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:09 - shap - INFO - phi = array([-0.07610119, -0.15782738, -0.00258929,  0.02860119, -0.00401786,
       -0.18449405, -0.0103869 ,  0.00681548])
2025-07-15 22:13:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:09 - shap - INFO - phi = array([-0.12666667, -0.13994048,  0.00065476,  0.02755952,  0.01035714,
       -0.18244048,  0.00488095,  0.00559524])
2025-07-15 22:13:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:09 - shap - INFO - phi = array([ 0.2447619 ,  0.29886905,  0.02708333,  0.05077381,  0.02511905,
       -0.04416667, -0.03059524,  0.02815476])
2025-07-15 22:13:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:10 - shap - INFO - phi = array([ 0.12297619,  0.11440476,  0.0160119 ,  0.03077381,  0.02690476,
        0.29571429, -0.01833333,  0.01154762])
2025-07-15 22:13:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:10 - shap - INFO - phi = array([ 0.35232143,  0.26916667, -0.00166667,  0.03720238,  0.01422619,
       -0.07696429, -0.01196429,  0.01767857])
2025-07-15 22:13:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:10 - shap - INFO - phi = array([-0.22166667,  0.03267857, -0.02267857, -0.03904762, -0.04345238,
       -0.09422619,  0.00125   , -0.01285714])
2025-07-15 22:13:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:10 - shap - INFO - phi = array([-0.12690476, -0.13059524,  0.00446429,  0.02059524,  0.01083333,
       -0.16607143, -0.01529762,  0.00297619])
2025-07-15 22:13:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:10 - shap - INFO - phi = array([-0.13074405, -0.14627976,  0.01294643,  0.01282738, -0.0066369 ,
       -0.12991071, -0.01473214,  0.00252976])
2025-07-15 22:13:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:10 - shap - INFO - phi = array([-0.29160714,  0.04607143, -0.01434524, -0.00934524, -0.03321429,
       -0.12779762,  0.00839286,  0.02184524])
2025-07-15 22:13:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:10 - shap - INFO - phi = array([ 0.2377381 ,  0.22160714, -0.0285119 , -0.00130952,  0.0735119 ,
        0.02464286,  0.09208333, -0.0197619 ])
2025-07-15 22:13:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:10 - shap - INFO - phi = array([ 0.08970238,  0.07297619, -0.12952381, -0.28178571, -0.12059524,
        0.12113095, -0.06559524, -0.08630952])
2025-07-15 22:13:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:11 - shap - INFO - phi = array([ 0.0389881 , -0.14011905, -0.02738095, -0.16190476, -0.07434524,
       -0.01672619,  0.01565476, -0.03416667])
2025-07-15 22:13:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:11 - shap - INFO - phi = array([-0.10642857, -0.14732143, -0.00690476, -0.08041667, -0.05172619,
       -0.02857143,  0.01755952,  0.00380952])
2025-07-15 22:13:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:11 - shap - INFO - phi = array([-0.12848214, -0.13574405,  0.00443452,  0.02419643,  0.00752976,
       -0.15806548, -0.01651786,  0.00264881])
2025-07-15 22:13:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:11 - shap - INFO - phi = array([ 0.13696429,  0.10005952,  0.00910714,  0.03267857,  0.02065476,
        0.30869048, -0.02011905,  0.01196429])
2025-07-15 22:13:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:11 - shap - INFO - phi = array([-0.10502976, -0.1078869 , -0.0066369 , -0.06348214, -0.03919643,
       -0.06235119,  0.01306548, -0.02848214])
2025-07-15 22:13:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:11 - shap - INFO - phi = array([ 0.208125  ,  0.15770833, -0.00854167,  0.04818452,  0.01842262,
        0.17973214, -0.01485119,  0.01122024])
2025-07-15 22:13:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:11 - shap - INFO - phi = array([-0.16241071, -0.10895833,  0.02122024,  0.01544643, -0.0028869 ,
       -0.15639881, -0.01020833,  0.00419643])
2025-07-15 22:13:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:11 - shap - INFO - phi = array([-0.05958333,  0.05660714, -0.00327381, -0.00107143,  0.04208333,
        0.56928571, -0.01696429,  0.01291667])
2025-07-15 22:13:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:11 - shap - INFO - phi = array([ 0.368125  ,  0.26455357, -0.00318452,  0.04491071,  0.01848214,
       -0.10145833, -0.01348214,  0.02205357])
2025-07-15 22:13:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:12 - shap - INFO - phi = array([-0.12577381, -0.13541667,  0.00113095,  0.02416667,  0.00904762,
       -0.17095238, -0.00291667,  0.00071429])
2025-07-15 22:13:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:12 - shap - INFO - phi = array([ 0.29193452,  0.2871131 ,  0.01669643,  0.05883929,  0.03467262,
       -0.09193452, -0.01502976,  0.01770833])
2025-07-15 22:13:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:12 - shap - INFO - phi = array([ 0.04285714,  0.05857143, -0.03244048, -0.33053571, -0.0502381 ,
        0.01702381,  0.00744048, -0.11267857])
2025-07-15 22:13:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:12 - shap - INFO - phi = array([ 0.05973214, -0.3421131 , -0.01342262,  0.01943452, -0.01776786,
       -0.11693452,  0.01020833,  0.0008631 ])
2025-07-15 22:13:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:12 - shap - INFO - phi = array([ 0.350625  ,  0.25324405,  0.0121131 ,  0.07866071,  0.0403869 ,
       -0.13401786,  0.0066369 , -0.00764881])
2025-07-15 22:13:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:12 - shap - INFO - phi = array([-0.08699405, -0.11258929, -0.016875  ,  0.0303869 ,  0.00925595,
       -0.21776786, -0.00122024, -0.00419643])
2025-07-15 22:13:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:12 - shap - INFO - phi = array([ 0.10604167, -0.3203869 , -0.00116071,  0.02508929,  0.00502976,
       -0.18139881,  0.000625  , -0.03383929])
2025-07-15 22:13:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:12 - shap - INFO - phi = array([-0.11264881, -0.0796131 , -0.00550595, -0.07264881, -0.04544643,
       -0.06431548,  0.01520833, -0.03502976])
2025-07-15 22:13:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:12 - shap - INFO - phi = array([-0.08160714,  0.05386905, -0.00690476,  0.01630952,  0.04244048,
        0.59595238, -0.02928571,  0.00922619])
2025-07-15 22:13:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:13 - shap - INFO - phi = array([-0.14479167, -0.11491071,  0.01824405,  0.01360119, -0.00205357,
       -0.17842262,  0.01443452, -0.00610119])
2025-07-15 22:13:16 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_hard
2025-07-15 22:13:16 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:13:16 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:13:16 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:13:16 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:13:16 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:13:16 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 22:13:16 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 22:13:16 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.10998164,  0.12875243, -0.0149635 , -0.02230531,  0.00117502,
        0.12237652,  0.01736153, -0.00752067])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.10998164, -0.12875243,  0.0149635 ,  0.02230531, -0.00117502,
       -0.12237652, -0.01736153,  0.00752067])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.13270994, -0.07809219, -0.03551162, -0.07687963,  0.02621018,
        0.02647343, -0.04050136, -0.0171601 ])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.13270994,  0.07809219,  0.03551162,  0.07687963, -0.02621018,
       -0.02647343,  0.04050136,  0.0171601 ])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.10007966, -0.08733912, -0.01477835, -0.02564507, -0.03481951,
       -0.29856752,  0.00226059, -0.01542232])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.10007966,  0.08733912,  0.01477835,  0.02564507,  0.03481951,
        0.29856752, -0.00226059,  0.01542232])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.15052328, -0.02036347,  0.01300277, -0.04948934, -0.03923334,
       -0.30792955,  0.01008487, -0.02014912])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.15052328,  0.02036347, -0.01300277,  0.04948934,  0.03923334,
        0.30792955, -0.01008487,  0.02014912])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.12762212,  0.14802456, -0.01695065, -0.03081481, -0.03901628,
       -0.00379639,  0.11022511,  0.00929087])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.12762212, -0.14802456,  0.01695065,  0.03081481,  0.03901628,
        0.00379639, -0.11022511, -0.00929087])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.07168306,  0.12025811,  0.00693201,  0.08220402,  0.04812401,
        0.00884098, -0.0153733 ,  0.02679029])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.07168306, -0.12025811, -0.00693201, -0.08220402, -0.04812401,
       -0.00884098,  0.0153733 , -0.02679029])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 1.80029261e-01, -4.10914361e-02, -7.81356898e-03, -4.26598820e-02,
       -1.92303888e-04,  2.47178659e-01, -1.51364063e-02, -1.04517068e-02])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-1.80029261e-01,  4.10914361e-02,  7.81356898e-03,  4.26598820e-02,
        1.92303888e-04, -2.47178659e-01,  1.51364063e-02,  1.04517068e-02])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.17622468, -0.05450274, -0.01179324, -0.05096254, -0.04425981,
        0.09825689,  0.07214551, -0.01242389])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.17622468,  0.05450274,  0.01179324,  0.05096254,  0.04425981,
       -0.09825689, -0.07214551,  0.01242389])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.20389692, -0.23705715,  0.00810981, -0.06401978, -0.03904038,
       -0.00756246,  0.0097069 , -0.02479244])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.20389692,  0.23705715, -0.00810981,  0.06401978,  0.03904038,
        0.00756246, -0.0097069 ,  0.02479244])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.07033261,  0.19358242,  0.00129804, -0.02040758,  0.03993225,
       -0.38396384, -0.03370915, -0.00780498])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.07033261, -0.19358242, -0.00129804,  0.02040758, -0.03993225,
        0.38396384,  0.03370915,  0.00780498])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.05944652,  0.16362145,  0.0052288 , -0.03611505, -0.00258135,
        0.16599719,  0.00357075, -0.01041914])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.05944652, -0.16362145, -0.0052288 ,  0.03611505,  0.00258135,
       -0.16599719, -0.00357075,  0.01041914])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([ 0.08834998,  0.12936188, -0.00071937, -0.03380861, -0.00304609,
        0.18804063, -0.00860182, -0.008496  ])
2025-07-15 22:13:17 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:17 - shap - INFO - phi = array([-0.08834998, -0.12936188,  0.00071937,  0.03380861,  0.00304609,
       -0.18804063,  0.00860182,  0.008496  ])
2025-07-15 22:13:17 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.15542895, -0.27954576, -0.03212944, -0.0503718 , -0.04844859,
        0.00277978,  0.04621499, -0.03244121])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.15542895,  0.27954576,  0.03212944,  0.0503718 ,  0.04844859,
       -0.00277978, -0.04621499,  0.03244121])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.0946115 , -0.14369022, -0.01632636, -0.03136959, -0.04247605,
       -0.25374524,  0.02401731, -0.01612182])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.0946115 ,  0.14369022,  0.01632636,  0.03136959,  0.04247605,
        0.25374524, -0.02401731,  0.01612182])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.18871005, -0.22510085, -0.00038344, -0.0522198 , -0.02573527,
        0.07081941,  0.01399675, -0.02561407])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.18871005,  0.22510085,  0.00038344,  0.0522198 ,  0.02573527,
       -0.07081941, -0.01399675,  0.02561407])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.14697587, -0.03773327,  0.01416778,  0.09349763,  0.0682949 ,
        0.03215167, -0.01891821,  0.0200806 ])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.14697587,  0.03773327, -0.01416778, -0.09349763, -0.0682949 ,
       -0.03215167,  0.01891821, -0.0200806 ])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.10066269,  0.14764486, -0.00954336, -0.02463557, -0.01435823,
        0.14009632,  0.01341877, -0.00585797])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.10066269, -0.14764486,  0.00954336,  0.02463557,  0.01435823,
       -0.14009632, -0.01341877,  0.00585797])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.09728971,  0.17542477, -0.01360169, -0.00722136,  0.01798676,
        0.08582081,  0.00363677, -0.00862412])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.09728971, -0.17542477,  0.01360169,  0.00722136, -0.01798676,
       -0.08582081, -0.00363677,  0.00862412])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.17733505, -0.06277127,  0.01237058,  0.05562084,  0.07184748,
        0.05392747, -0.02018217, -0.01652841])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.17733505,  0.06277127, -0.01237058, -0.05562084, -0.07184748,
       -0.05392747,  0.02018217,  0.01652841])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.11010249, -0.1824344 ,  0.02597475,  0.06993181, -0.03187891,
       -0.01772455, -0.0395009 ,  0.05278123])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.11010249,  0.1824344 , -0.02597475, -0.06993181,  0.03187891,
        0.01772455,  0.0395009 , -0.05278123])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.12073231, -0.11788   ,  0.02387285,  0.16282718,  0.04066715,
       -0.17598736, -0.03216096,  0.02385615])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.12073231,  0.11788   , -0.02387285, -0.16282718, -0.04066715,
        0.17598736,  0.03216096, -0.02385615])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.03478037,  0.1179149 ,  0.02151082,  0.15864593,  0.07072041,
       -0.01519919, -0.02458766,  0.02382396])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.03478037, -0.1179149 , -0.02151082, -0.15864593, -0.07072041,
        0.01519919,  0.02458766, -0.02382396])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([ 0.08224176,  0.14131505,  0.00572918,  0.07544465,  0.05241953,
        0.0158622 , -0.01571403, -0.00738149])
2025-07-15 22:13:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:18 - shap - INFO - phi = array([-0.08224176, -0.14131505, -0.00572918, -0.07544465, -0.05241953,
       -0.0158622 ,  0.01571403,  0.00738149])
2025-07-15 22:13:18 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 9.47103869e-02,  1.68059787e-01, -6.64485454e-03, -2.78805153e-02,
        7.46773552e-05,  1.13761174e-01,  1.25411778e-02, -4.98417847e-03])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-9.47103869e-02, -1.68059787e-01,  6.64485454e-03,  2.78805153e-02,
       -7.46773552e-05, -1.13761174e-01, -1.25411778e-02,  4.98417847e-03])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.11544738, -0.10244342, -0.01291054, -0.03480708, -0.04181526,
       -0.27638499,  0.02266101, -0.01374469])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.11544738,  0.10244342,  0.01291054,  0.03480708,  0.04181526,
        0.27638499, -0.02266101,  0.01374469])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.07515655,  0.1131201 ,  0.00618426,  0.06860702,  0.05046928,
        0.03077559, -0.01405475,  0.02019777])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.07515655, -0.1131201 , -0.00618426, -0.06860702, -0.05046928,
       -0.03077559,  0.01405475, -0.02019777])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.16616333, -0.14789709,  0.01465207, -0.05926151, -0.04434248,
       -0.15381792,  0.0053877 , -0.01480147])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.16616333,  0.14789709, -0.01465207,  0.05926151,  0.04434248,
        0.15381792, -0.0053877 ,  0.01480147])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.11856412,  0.1252713 , -0.01708697, -0.00841427,  0.02230959,
        0.11129911, -0.00016823, -0.00813551])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.11856412, -0.1252713 ,  0.01708697,  0.00841427, -0.02230959,
       -0.11129911,  0.00016823,  0.00813551])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.04717823, -0.0884962 ,  0.00175473,  0.00149033, -0.04557132,
       -0.4651584 ,  0.00666342, -0.01516228])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.04717823,  0.0884962 , -0.00175473, -0.00149033,  0.04557132,
        0.4651584 , -0.00666342,  0.01516228])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.27525582, -0.24242192,  0.0083667 , -0.07145146, -0.0309469 ,
        0.15448499,  0.00873939, -0.03324425])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.27525582,  0.24242192, -0.0083667 ,  0.07145146,  0.0309469 ,
       -0.15448499, -0.00873939,  0.03324425])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.09032301,  0.14051739, -0.00630443, -0.02880635,  0.00065224,
        0.16882209, -0.00823058, -0.00467978])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.09032301, -0.14051739,  0.00630443,  0.02880635, -0.00065224,
       -0.16882209,  0.00823058,  0.00467978])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.1720318 , -0.2870041 , -0.02582475, -0.06796629, -0.04659926,
        0.10289866,  0.00767485, -0.02957338])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.1720318 ,  0.2870041 ,  0.02582475,  0.06796629,  0.04659926,
       -0.10289866, -0.00767485,  0.02957338])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.0510987 , -0.05531771,  0.03694611,  0.23418219,  0.0488762 ,
       -0.03758275, -0.02531562,  0.10495363])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.0510987 ,  0.05531771, -0.03694611, -0.23418219, -0.0488762 ,
        0.03758275,  0.02531562, -0.10495363])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([-0.04040597,  0.25677897,  0.01210554, -0.00154545,  0.03689357,
        0.07575237, -0.01593352, -0.01275565])
2025-07-15 22:13:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:19 - shap - INFO - phi = array([ 0.04040597, -0.25677897, -0.01210554,  0.00154545, -0.03689357,
       -0.07575237,  0.01593352,  0.01275565])
2025-07-15 22:13:19 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([-0.2334131 , -0.14116148,  0.00914013, -0.06172566, -0.03488744,
        0.24425286, -0.02198177,  0.03408491])
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([ 0.2334131 ,  0.14116148, -0.00914013,  0.06172566,  0.03488744,
       -0.24425286,  0.02198177, -0.03408491])
2025-07-15 22:13:20 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([ 0.06946227,  0.0843273 ,  0.00853793, -0.04027242, -0.00451657,
        0.23163062, -0.01175395,  0.00874378])
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([-0.06946227, -0.0843273 , -0.00853793,  0.04027242,  0.00451657,
       -0.23163062,  0.01175395, -0.00874378])
2025-07-15 22:13:20 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([-0.16287637,  0.2034615 ,  0.01037297, -0.03542373, -0.00270323,
        0.16178316, -0.00701794,  0.03521066])
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([ 0.16287637, -0.2034615 , -0.01037297,  0.03542373,  0.00270323,
       -0.16178316,  0.00701794, -0.03521066])
2025-07-15 22:13:20 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([ 0.0826377 ,  0.0734088 ,  0.00693552,  0.08161343,  0.05518903,
        0.0352663 , -0.016687  ,  0.02958939])
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([-0.0826377 , -0.0734088 , -0.00693552, -0.08161343, -0.05518903,
       -0.0352663 ,  0.016687  , -0.02958939])
2025-07-15 22:13:20 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([ 0.06203912, -0.09926032,  0.00491752, -0.00818768, -0.04797365,
       -0.45379642,  0.01181324, -0.01094879])
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([-0.06203912,  0.09926032, -0.00491752,  0.00818768,  0.04797365,
        0.45379642, -0.01181324,  0.01094879])
2025-07-15 22:13:20 - shap - INFO - num_full_subsets = 4
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([ 0.1146017 ,  0.10075854, -0.01482474, -0.01939689,  0.01259996,
        0.16186767, -0.01659135,  0.00583619])
2025-07-15 22:13:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:13:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:13:20 - shap - INFO - phi = array([-0.1146017 , -0.10075854,  0.01482474,  0.01939689, -0.01259996,
       -0.16186767,  0.01659135, -0.00583619])
2025-07-15 22:13:24 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-15 22:13:24 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:13:24 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:13:24 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:13:24 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:14:17 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:14:18 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-15 22:14:18 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
