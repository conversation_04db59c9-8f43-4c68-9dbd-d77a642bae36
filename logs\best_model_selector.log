2025-07-15 20:06:18 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-07-15 20:06:18 - best_model_selector - INFO - 加载模型训练结果...
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 计算综合性能指标...
2025-07-15 20:06:18 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-07-15 20:06:18 - best_model_selector - INFO - 使用 balanced 策略计算综合得分...
2025-07-15 20:06:18 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: XGBoost (得分: 0.9735)
2025-07-15 20:06:18 - best_model_selector - INFO - 模型选择结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-07-15 20:06:18 - best_model_selector - INFO - ============================================================
2025-07-15 20:06:18 - best_model_selector - INFO - 最佳模型推荐结果
2025-07-15 20:06:18 - best_model_selector - INFO - ============================================================
2025-07-15 20:06:18 - best_model_selector - INFO - 推荐策略: balanced
2025-07-15 20:06:18 - best_model_selector - INFO - 最佳模型: XGBoost
2025-07-15 20:06:18 - best_model_selector - INFO - 综合得分: 0.9735
2025-07-15 20:06:18 - best_model_selector - INFO - 
前三名模型:
2025-07-15 20:06:18 - best_model_selector - INFO -   1. XGBoost: 0.9735
2025-07-15 20:06:18 - best_model_selector - INFO -   2. Random Forest: 0.9692
2025-07-15 20:06:18 - best_model_selector - INFO -   3. LightGBM: 0.9690
2025-07-15 20:06:18 - best_model_selector - INFO -   4. KNN: 0.8641
2025-07-15 20:06:18 - best_model_selector - INFO -   5. Naive Bayes: 0.8464
2025-07-15 20:06:18 - best_model_selector - INFO - 
推荐 XGBoost 作为最佳模型的理由：

1. 在平衡策略下，该模型获得了最高的综合得分 0.9735
2. 在多个关键指标上表现均衡：
   - AUC-ROC: 0.9879
   - F1分数: 0.9754
   - MCC: 0.9504

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-07-15 20:06:18 - best_model_selector - INFO - ============================================================
