2025-07-15 20:53:54 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:53:55 - GUI - INFO - GUI界面初始化完成
2025-07-15 20:54:22 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-07-15 20:54:22 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 20:54:22 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 235, 'max_depth': 15, 'min_samples_split': 2, 'min_samples_leaf': 17, 'max_features': 'log2'}
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9834
2025-07-15 20:54:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250715_205435.html
2025-07-15 20:54:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250715_205435.html
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.15 秒
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 20
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:37 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:37 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:37 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:37 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:38 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:38 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:38 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:38 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 135, 'max_depth': 9, 'learning_rate': 0.1704482693220943, 'subsample': 0.8433573654439638, 'colsample_bytree': 0.5610514446454544}
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9869
2025-07-15 20:54:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250715_205439.html
2025-07-15 20:54:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250715_205439.html
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.33 秒
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 20
2025-07-15 20:54:42 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 187, 'max_depth': 4, 'learning_rate': 0.12091061097011385, 'feature_fraction': 0.7806902520482117, 'bagging_fraction': 0.9230958055354184}
2025-07-15 20:54:42 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9853
2025-07-15 20:54:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:42 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250715_205442.html
2025-07-15 20:54:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250715_205443.html
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.49 秒
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 20
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 5.587456935185252, 'solver': 'liblinear'}
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-07-15 20:54:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250715_205443.html
2025-07-15 20:54:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250715_205443.html
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.66 秒
2025-07-15 20:56:00 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-07-15 20:56:00 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 20:56:00 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:56:00 - model_training - INFO - 准确率: 0.9000
2025-07-15 20:56:00 - model_training - INFO - AUC: 0.9386
2025-07-15 20:56:00 - model_training - INFO - 混淆矩阵:
2025-07-15 20:56:00 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-15 20:56:00 - model_training - INFO - 
分类报告:
2025-07-15 20:56:00 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-15 20:56:00 - model_training - INFO - 训练时间: 0.07 秒
2025-07-15 20:56:00 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:56:00 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:56:00 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:56:00 - model_training - INFO - 准确率: 0.8750
2025-07-15 20:56:00 - model_training - INFO - AUC: 0.9668
2025-07-15 20:56:00 - model_training - INFO - 混淆矩阵:
2025-07-15 20:56:00 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 20:56:00 - model_training - INFO - 
分类报告:
2025-07-15 20:56:00 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 20:56:00 - model_training - INFO - 训练时间: 0.04 秒
2025-07-15 20:56:00 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:56:00 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:56:01 - model_training - INFO - 模型名称: LightGBM
2025-07-15 20:56:01 - model_training - INFO - 准确率: 0.8750
2025-07-15 20:56:01 - model_training - INFO - AUC: 0.9463
2025-07-15 20:56:01 - model_training - INFO - 混淆矩阵:
2025-07-15 20:56:01 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 20:56:01 - model_training - INFO - 
分类报告:
2025-07-15 20:56:01 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 20:56:01 - model_training - INFO - 训练时间: 0.22 秒
2025-07-15 20:56:01 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 20:56:01 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 20:56:01 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 20:56:01 - model_training - INFO - 准确率: 0.8500
2025-07-15 20:56:01 - model_training - INFO - AUC: 0.9284
2025-07-15 20:56:01 - model_training - INFO - 混淆矩阵:
2025-07-15 20:56:01 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-07-15 20:56:01 - model_training - INFO - 
分类报告:
2025-07-15 20:56:01 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-07-15 20:56:01 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 20:56:01 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 20:56:01 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 20:56:29 - model_ensemble - INFO - ============================================================
2025-07-15 20:56:29 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:56:29 - model_ensemble - INFO - ============================================================
2025-07-15 20:56:29 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 20:56:29 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:56:29 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:56:29 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:56:29 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:56:29 - model_training - INFO - 准确率: 0.9000
2025-07-15 20:56:29 - model_training - INFO - AUC: 0.9386
2025-07-15 20:56:29 - model_training - INFO - 混淆矩阵:
2025-07-15 20:56:29 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-15 20:56:29 - model_training - INFO - 
分类报告:
2025-07-15 20:56:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-15 20:56:29 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 20:56:29 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:56:29 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:56:29 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:56:29 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:56:29 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:56:29 - model_training - INFO - 准确率: 0.8750
2025-07-15 20:56:29 - model_training - INFO - AUC: 0.9668
2025-07-15 20:56:29 - model_training - INFO - 混淆矩阵:
2025-07-15 20:56:29 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 20:56:29 - model_training - INFO - 
分类报告:
2025-07-15 20:56:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 20:56:29 - model_training - INFO - 训练时间: 0.05 秒
2025-07-15 20:56:29 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:56:29 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:56:29 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:56:29 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:56:29 - model_training - INFO - 模型名称: LightGBM
2025-07-15 20:56:29 - model_training - INFO - 准确率: 0.8750
2025-07-15 20:56:29 - model_training - INFO - AUC: 0.9463
2025-07-15 20:56:29 - model_training - INFO - 混淆矩阵:
2025-07-15 20:56:29 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 20:56:29 - model_training - INFO - 
分类报告:
2025-07-15 20:56:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 20:56:29 - model_training - INFO - 训练时间: 0.22 秒
2025-07-15 20:56:29 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 20:56:29 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 20:56:29 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:56:29 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 20:56:29 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 20:56:29 - model_training - INFO - 准确率: 0.8500
2025-07-15 20:56:29 - model_training - INFO - AUC: 0.9284
2025-07-15 20:56:29 - model_training - INFO - 混淆矩阵:
2025-07-15 20:56:29 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-07-15 20:56:29 - model_training - INFO - 
分类报告:
2025-07-15 20:56:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-07-15 20:56:29 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 20:56:29 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 20:56:29 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 20:56:29 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 20:56:29 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:56:29 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:56:29 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:56:29 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:56:30 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:56:30 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:56:30 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:56:30 - model_ensemble - INFO -     voting_hard - 准确率: 0.9000, F1: 0.8990
2025-07-15 20:56:30 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:56:30 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:56:32 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:56:32 - model_ensemble - INFO - ============================================================
2025-07-15 20:56:32 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:56:32 - model_ensemble - INFO - ============================================================
2025-07-15 20:56:32 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 20:56:32 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-15 20:56:32 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-15 20:56:32 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:56:32 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 20:56:32 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9000, 精确率: 0.9027, 召回率: 0.9000, F1: 0.8990, AUC: 0.0000
2025-07-15 20:56:32 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 20:56:32 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:56:32 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_205632.joblib
2025-07-15 20:58:09 - model_ensemble - INFO - ============================================================
2025-07-15 20:58:09 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:58:09 - model_ensemble - INFO - ============================================================
2025-07-15 20:58:09 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 20:58:09 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:58:09 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:58:09 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:58:10 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:58:10 - model_training - INFO - 准确率: 0.9000
2025-07-15 20:58:10 - model_training - INFO - AUC: 0.9386
2025-07-15 20:58:10 - model_training - INFO - 混淆矩阵:
2025-07-15 20:58:10 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-15 20:58:10 - model_training - INFO - 
分类报告:
2025-07-15 20:58:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-15 20:58:10 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 20:58:10 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:58:10 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:58:10 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:58:10 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:58:10 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:58:10 - model_training - INFO - 准确率: 0.8750
2025-07-15 20:58:10 - model_training - INFO - AUC: 0.9668
2025-07-15 20:58:10 - model_training - INFO - 混淆矩阵:
2025-07-15 20:58:10 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 20:58:10 - model_training - INFO - 
分类报告:
2025-07-15 20:58:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 20:58:10 - model_training - INFO - 训练时间: 0.04 秒
2025-07-15 20:58:10 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:58:10 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:58:10 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:58:10 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:58:10 - model_training - INFO - 模型名称: LightGBM
2025-07-15 20:58:10 - model_training - INFO - 准确率: 0.8750
2025-07-15 20:58:10 - model_training - INFO - AUC: 0.9463
2025-07-15 20:58:10 - model_training - INFO - 混淆矩阵:
2025-07-15 20:58:10 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 20:58:10 - model_training - INFO - 
分类报告:
2025-07-15 20:58:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 20:58:10 - model_training - INFO - 训练时间: 0.21 秒
2025-07-15 20:58:10 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 20:58:10 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 20:58:10 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:58:10 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 20:58:10 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 20:58:10 - model_training - INFO - 准确率: 0.8500
2025-07-15 20:58:10 - model_training - INFO - AUC: 0.9284
2025-07-15 20:58:10 - model_training - INFO - 混淆矩阵:
2025-07-15 20:58:10 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-07-15 20:58:10 - model_training - INFO - 
分类报告:
2025-07-15 20:58:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-07-15 20:58:10 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 20:58:10 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 20:58:10 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 20:58:10 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 20:58:10 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:58:10 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:58:10 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:58:10 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:58:10 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:58:10 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:58:10 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:58:11 - model_ensemble - INFO -     voting_hard - 准确率: 0.9000, F1: 0.8990
2025-07-15 20:58:11 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:58:11 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:58:12 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:58:12 - model_ensemble - INFO - ============================================================
2025-07-15 20:58:12 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:58:12 - model_ensemble - INFO - ============================================================
2025-07-15 20:58:12 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 20:58:12 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-15 20:58:12 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-15 20:58:12 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:58:12 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 20:58:12 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9000, 精确率: 0.9027, 召回率: 0.9000, F1: 0.8990, AUC: 0.0000
2025-07-15 20:58:12 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 20:58:12 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:58:12 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_205812.joblib
