#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型性能自动比较功能
提供详细的性能指标计算和综合比较报告
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端，避免GUI警告
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from joblib import load
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, 
    roc_auc_score, confusion_matrix, classification_report,
    average_precision_score, matthews_corrcoef, cohen_kappa_score
)
from datetime import datetime
import json
from config import OUTPUT_PATH, CACHE_PATH, PROJECT_ROOT

# 配置字典
CONFIG = {
    'output_path': OUTPUT_PATH,
    'cache_path': CACHE_PATH,
    'report_path': PROJECT_ROOT / 'reports',
    'dpi': 150,
    'figsize': (12, 8)
}

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set_theme(style='whitegrid')

# 设置英文字体和样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
sns.set_theme(style='whitegrid')

# 中文术语到英文的映射
TERM_MAPPING = {
    '模型性能指标热力图': 'Model Performance Metrics Heatmap',
    '模型名称': 'Model Name',
    '性能指标': 'Performance Metrics',
    '性能指标值': 'Performance Metric Value',
    '模型性能雷达图': 'Model Performance Radar Chart',
    '模型综合性能排名': 'Model Performance Ranking',
    '模型名称': 'Model Name',
    '综合性能得分': 'Composite Performance Score',
    '准确率': 'Accuracy',
    '精确率': 'Precision',
    '召回率': 'Recall',
    'F1分数': 'F1 Score',
    '特异性': 'Specificity',
    '敏感性': 'Sensitivity',
    '阴性预测值': 'NPV',
    '阳性预测值': 'PPV'
}

def translate_term(term):
    """将中文术语翻译为英文"""
    return TERM_MAPPING.get(term, term)

# 模型列表
MODEL_NAMES = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
               'Logistic', 'SVM', 'NeuralNet', 'NaiveBayes', 'KNN']

def calculate_comprehensive_metrics(y_true, y_pred, y_pred_proba=None):
    """
    计算综合性能指标
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        y_pred_proba: 预测概率
    
    Returns:
        dict: 包含所有性能指标的字典
    """
    metrics = {}
    
    # 基础分类指标
    metrics['accuracy'] = accuracy_score(y_true, y_pred)
    metrics['precision'] = precision_score(y_true, y_pred, zero_division=0)
    metrics['recall'] = recall_score(y_true, y_pred, zero_division=0)
    metrics['f1_score'] = f1_score(y_true, y_pred, zero_division=0)
    
    # 混淆矩阵相关指标
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0
    metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0
    metrics['npv'] = tn / (tn + fn) if (tn + fn) > 0 else 0  # 阴性预测值
    metrics['ppv'] = tp / (tp + fp) if (tp + fp) > 0 else 0  # 阳性预测值
    
    # 概率相关指标
    if y_pred_proba is not None and len(y_pred_proba) == len(y_true):
        metrics['auc_roc'] = roc_auc_score(y_true, y_pred_proba)
        metrics['auc_pr'] = average_precision_score(y_true, y_pred_proba)
    else:
        metrics['auc_roc'] = 0.5
        metrics['auc_pr'] = 0.5
    
    # 其他高级指标
    metrics['mcc'] = matthews_corrcoef(y_true, y_pred)  # 马修斯相关系数
    metrics['kappa'] = cohen_kappa_score(y_true, y_pred)  # Cohen's Kappa
    
    # 平衡准确率
    metrics['balanced_accuracy'] = (metrics['sensitivity'] + metrics['specificity']) / 2
    
    return metrics

def load_model_results(selected_models=None):
    """
    加载所有模型的缓存结果
    
    Args:
        selected_models: 可选，要加载的模型列表。如果为None，则加载所有模型。
    
    Returns:
        dict: 包含所有模型结果的字典
    """
    results = {}
    
    # 确定要加载的模型列表
    models_to_load = selected_models if selected_models is not None else MODEL_NAMES
    
    for model_name in models_to_load:
        if model_name not in MODEL_NAMES:
            print(f"Warning: Model {model_name} is not in the predefined MODEL_NAMES list")
            continue
            
        cache_file = CONFIG['cache_path'] / f"{model_name}_results.joblib"
        if cache_file.exists():
            try:
                results[model_name] = load(cache_file)
                print(f"Successfully loaded cached results for model {model_name}")
            except Exception as e:
                print(f"Failed to load model {model_name}: {e}")
        else:
            print(f"Cache file for model {model_name} does not exist: {cache_file}")
    
    return results

def generate_performance_dataframe(results):
    """
    生成性能指标数据框
    
    Args:
        results: 模型结果字典
    
    Returns:
        pd.DataFrame: 包含所有模型性能指标的数据框
    """
    performance_data = []
    
    for model_name, data in results.items():
        y_true = data['y_true']
        y_pred = data['y_pred']
        y_pred_proba = data.get('y_pred_proba', None)
        
        # 计算综合指标
        metrics = calculate_comprehensive_metrics(y_true, y_pred, y_pred_proba)
        metrics['model_name'] = model_name
        performance_data.append(metrics)
    
    df = pd.DataFrame(performance_data)
    df = df.set_index('model_name')
    
    return df

def create_performance_heatmap(df, save_path):
    """
    创建性能指标热力图
    
    Args:
        df: 性能指标数据框
        save_path: 保存路径
    """
    plt.figure(figsize=(14, 10))
    
    # 选择主要指标进行可视化
    main_metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'specificity', 
                   'auc_roc', 'auc_pr', 'mcc', 'kappa', 'balanced_accuracy']
    
    # 确保所有指标都存在
    available_metrics = [metric for metric in main_metrics if metric in df.columns]
    
    # 创建热力图
    sns.heatmap(df[available_metrics].T, annot=True, cmap='RdYlBu_r', 
                center=0.5, fmt='.3f', cbar_kws={'label': translate_term('性能指标值')})
    
    plt.title(translate_term('模型性能指标热力图'), fontsize=16, fontweight='bold', pad=20)
    plt.xlabel(translate_term('模型名称'), fontsize=12)
    plt.ylabel(translate_term('性能指标'), fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    
    # 保存图表
    save_path.mkdir(parents=True, exist_ok=True)
    plt.savefig(save_path / 'performance_heatmap.png', dpi=CONFIG['dpi'], bbox_inches='tight')
    plt.close()
    print(f"Performance heatmap saved to: {save_path / 'performance_heatmap.png'}")

def create_radar_comparison(df, save_path):
    """
    创建多模型雷达图比较
    
    Args:
        df: 性能指标数据框
        save_path: 保存路径
    """
    # 选择关键指标
    key_metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'specificity', 'auc_roc']
    available_metrics = [metric for metric in key_metrics if metric in df.columns]
    
    if len(available_metrics) < 3:
        print("Insufficient metrics available, skipping radar chart generation")
        return
    
    # 设置雷达图参数
    N = len(available_metrics)
    angles = np.linspace(0, 2 * np.pi, N, endpoint=False).tolist()
    angles += angles[:1]
    
    # 创建子图 - 使用3x4布局以容纳所有10个模型
    rows = 3
    cols = 4
    fig, axes = plt.subplots(rows, cols, figsize=(24, 18), subplot_kw=dict(projection='polar'))
    axes = axes.flatten()
    
    colors = sns.color_palette('tab10', len(df))
    
    for idx, (model_name, row) in enumerate(df.iterrows()):
        if idx >= rows * cols:  # 最多显示12个模型
            print(f"Warning: Only showing first {rows * cols} models in radar chart")
            break
            
        ax = axes[idx]
        values = [row[metric] for metric in available_metrics]
        values += values[:1]  # 闭合雷达图
        
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=colors[idx])
        ax.fill(angles, values, alpha=0.25, color=colors[idx])
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(available_metrics)
        ax.set_ylim(0, 1)
        ax.set_title(f'{model_name} Performance Radar', size=12, fontweight='bold')
        ax.grid(True)
    
    # 隐藏多余的子图
    for idx in range(len(df), rows * cols):
        axes[idx].set_visible(False)
    
    plt.tight_layout()
    save_path.mkdir(parents=True, exist_ok=True)
    plt.savefig(save_path / 'radar_comparison.png', dpi=CONFIG['dpi'], bbox_inches='tight')
    plt.close()
    print(f"Radar comparison chart saved to: {save_path / 'radar_comparison.png'}")

def create_ranking_chart(df, save_path):
    """
    创建模型排名图表
    
    Args:
        df: 性能指标数据框
        save_path: 保存路径
    """
    # 计算综合得分（多个指标的加权平均）
    weights = {
        'accuracy': 0.15,
        'precision': 0.15,
        'recall': 0.15,
        'f1_score': 0.20,
        'auc_roc': 0.20,
        'mcc': 0.15
    }
    
    # 计算加权得分
    df['composite_score'] = 0
    for metric, weight in weights.items():
        if metric in df.columns:
            df['composite_score'] += df[metric] * weight
    
    # 按综合得分排序
    df_sorted = df.sort_values('composite_score', ascending=False)
    
    # 创建排名图表
    plt.figure(figsize=(12, 8))
    
    # 条形图
    bars = plt.bar(range(len(df_sorted)), df_sorted['composite_score'], 
                   color=sns.color_palette('viridis', len(df_sorted)))
    
    # 添加数值标签
    for i, (idx, row) in enumerate(df_sorted.iterrows()):
        plt.text(i, row['composite_score'] + 0.01, f'{row["composite_score"]:.3f}', 
                ha='center', va='bottom', fontweight='bold')
    
    plt.xlabel(translate_term('模型名称'), fontsize=12)
    plt.ylabel(translate_term('综合性能得分'), fontsize=12)
    plt.title(translate_term('模型综合性能排名'), fontsize=16, fontweight='bold', pad=20)
    plt.xticks(range(len(df_sorted)), df_sorted.index, rotation=45)
    plt.ylim(0, 1)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    
    save_path.mkdir(parents=True, exist_ok=True)
    plt.savefig(save_path / 'model_ranking.png', dpi=CONFIG['dpi'], bbox_inches='tight')
    plt.close()
    print(f"Model ranking chart saved to: {save_path / 'model_ranking.png'}")
    
    return df_sorted

def generate_html_report(df, df_sorted, save_path):
    """
    生成HTML格式的详细报告
    
    Args:
        df: 性能指标数据框
        df_sorted: 按综合得分排序的数据框
        save_path: 保存路径
    """
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>模型性能比较报告</title>
        <style>
            body {{
                font-family: 'Arial', sans-serif;
                margin: 40px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            h1 {{
                color: #2c3e50;
                text-align: center;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
            }}
            h2 {{
                color: #34495e;
                margin-top: 30px;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }}
            th {{
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }}
            tr:nth-child(even) {{
                background-color: #f2f2f2;
            }}
            .best-score {{
                background-color: #2ecc71 !important;
                color: white;
                font-weight: bold;
            }}
            .summary {{
                background-color: #ecf0f1;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
            }}
            .metric-description {{
                font-size: 0.9em;
                color: #7f8c8d;
                margin-top: 10px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>模型性能比较报告</h1>
            <p style="text-align: center; color: #7f8c8d;">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <div class="summary">
                <h2>📊 执行摘要</h2>
                <p><strong>最佳模型:</strong> {df_sorted.index[0]} (综合得分: {df_sorted.iloc[0]['composite_score']:.3f})</p>
                <p><strong>比较模型数量:</strong> {len(df)}</p>
                <p><strong>评估指标数量:</strong> {len(df.columns) - 1}</p>
            </div>
            
            <h2>🏆 模型排名</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>综合得分</th>
                    <th>准确率</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                </tr>
    """
    
    # 添加排名表格
    for rank, (model_name, row) in enumerate(df_sorted.iterrows(), 1):
        html_content += f"""
                <tr>
                    <td>{rank}</td>
                    <td><strong>{model_name}</strong></td>
                    <td class="{'best-score' if rank == 1 else ''}">{row['composite_score']:.3f}</td>
                    <td>{row.get('accuracy', 0):.3f}</td>
                    <td>{row.get('precision', 0):.3f}</td>
                    <td>{row.get('recall', 0):.3f}</td>
                    <td>{row.get('f1_score', 0):.3f}</td>
                    <td>{row.get('auc_roc', 0):.3f}</td>
                </tr>
        """
    
    html_content += """
            </table>
            
            <h2>📈 详细性能指标</h2>
            <table>
                <tr>
                    <th>模型</th>
    """
    
    # 英文列名到中文的映射
    column_mapping = {
        'accuracy': '准确率',
        'precision': '精确率', 
        'recall': '召回率',
        'f1_score': 'F1分数',
        'specificity': '特异性',
        'sensitivity': '敏感性',
        'npv': '阴性预测值',
        'ppv': '阳性预测值',
        'auc_roc': 'AUC-ROC',
        'auc_pr': 'AUC-PR',
        'mcc': 'MCC',
        'kappa': 'Kappa',
        'balanced_accuracy': '平衡准确率'
    }
    
    # 添加指标列标题
    for col in df.columns:
        if col != 'composite_score':
            chinese_name = column_mapping.get(col, col)
            html_content += f"<th>{chinese_name}</th>"
    
    html_content += "</tr>"
    
    # 添加详细指标数据
    for model_name, row in df.iterrows():
        html_content += f"<tr><td><strong>{model_name}</strong></td>"
        for col in df.columns:
            if col != 'composite_score':
                value = row[col]
                # 高亮最佳值
                is_best = value == df[col].max()
                cell_class = 'best-score' if is_best else ''
                html_content += f'<td class="{cell_class}">{value:.3f}</td>'
        html_content += "</tr>"
    
    html_content += f"""
            </table>
            
            <div class="metric-description">
                <h2>📝 指标说明</h2>
                <ul>
                    <li><strong>准确率(Accuracy):</strong> 正确预测实例的比例</li>
                    <li><strong>精确率(Precision):</strong> 预测为正例中实际为正例的比例</li>
                    <li><strong>召回率(Recall):</strong> 实际正例中被正确预测的比例</li>
                    <li><strong>F1分数(F1 Score):</strong> 精确率和召回率的调和平均数</li>
                    <li><strong>特异性(Specificity):</strong> 实际负例中被正确预测的比例</li>
                    <li><strong>AUC-ROC:</strong> ROC曲线下面积，衡量分类器性能</li>
                    <li><strong>AUC-PR:</strong> 精确率-召回率曲线下面积</li>
                    <li><strong>MCC:</strong> 马修斯相关系数，平衡的分类性能度量</li>
                    <li><strong>Kappa:</strong> 科恩卡帕系数，考虑随机一致性</li>
                    <li><strong>平衡准确率(Balanced Accuracy):</strong> 敏感性和特异性的平均值</li>
                </ul>
            </div>
            
            <div class="summary">
                <h2>💡 推荐建议</h2>
                <p>基于综合性能评估，我们推荐使用 <strong>{df_sorted.index[0]}</strong> 模型进行后续任务。</p>
                <p>如果您对特定指标有具体要求，请参考详细性能指标表格选择最合适的模型。</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # 保存HTML报告
    save_path.mkdir(parents=True, exist_ok=True)
    with open(save_path / 'performance_report.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML report saved to: {save_path / 'performance_report.html'}")

def save_performance_json(df, df_sorted, save_path):
    """
    保存性能数据为JSON格式
    
    Args:
        df: 性能指标数据框
        df_sorted: 按综合得分排序的数据框
        save_path: 保存路径
    """
    performance_data = {
        'generation_time': datetime.now().isoformat(),
        'best_model': df_sorted.index[0],
        'best_score': float(df_sorted.iloc[0]['composite_score']),
        'model_count': len(df),
        'detailed_metrics': df.to_dict('index'),
        'ranking': [(model, float(score)) for model, score in 
                   zip(df_sorted.index, df_sorted['composite_score'])]
    }
    
    save_path.mkdir(parents=True, exist_ok=True)
    with open(save_path / 'performance_data.json', 'w', encoding='utf-8') as f:
        json.dump(performance_data, f, ensure_ascii=False, indent=2)
    
    print(f"Performance data JSON saved to: {save_path / 'performance_data.json'}")

def generate_comprehensive_report(selected_models=None):
    """
    生成综合性能比较报告
    主函数，协调所有报告生成功能
    
    Args:
        selected_models: 可选，要包含在报告中的模型列表。如果为None，则包含所有可用模型。
    """
    print("Starting model performance comparison report generation...")
    
    # 创建报告目录
    report_path = CONFIG['report_path']
    report_path.mkdir(parents=True, exist_ok=True)
    
    # 加载模型结果
    results = load_model_results(selected_models)
    
    if not results:
        print("No model results available, cannot generate report!")
        return
    
    print(f"Successfully loaded results for {len(results)} models")
    
    # 生成性能数据框
    df = generate_performance_dataframe(results)
    print("Performance metrics calculation completed")
    
    # 创建可视化图表
    create_performance_heatmap(df, report_path)
    create_radar_comparison(df, report_path)
    df_sorted = create_ranking_chart(df, report_path)
    
    # 生成报告文件
    generate_html_report(df, df_sorted, report_path)
    save_performance_json(df, df_sorted, report_path)
    
    # 保存CSV格式的详细数据
    df.to_csv(report_path / 'performance_metrics.csv', encoding='utf-8-sig')
    print(f"Performance metrics CSV saved to: {report_path / 'performance_metrics.csv'}")
    
    print(f"\n📊 Model performance comparison report generation completed!")
    print(f"📁 Report files location: {report_path}")
    print(f"🏆 Best model: {df_sorted.index[0]} (Score: {df_sorted.iloc[0]['composite_score']:.3f})")
    print(f"📄 View detailed report: {report_path / 'performance_report.html'}")

if __name__ == "__main__":
    generate_comprehensive_report()