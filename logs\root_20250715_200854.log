2025-07-15 20:08:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:08:56 - GUI - INFO - GUI界面初始化完成
2025-07-15 20:10:08 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 9)
2025-07-15 20:10:08 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 20:10:08 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:10:08 - model_training - INFO - 准确率: 0.8250
2025-07-15 20:10:08 - model_training - INFO - AUC: 0.9015
2025-07-15 20:10:08 - model_training - INFO - 混淆矩阵:
2025-07-15 20:10:08 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-15 20:10:08 - model_training - INFO - 
分类报告:
2025-07-15 20:10:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-15 20:10:08 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 20:10:08 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:10:08 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:10:08 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:10:08 - model_training - INFO - 准确率: 0.7750
2025-07-15 20:10:08 - model_training - INFO - AUC: 0.8568
2025-07-15 20:10:08 - model_training - INFO - 混淆矩阵:
2025-07-15 20:10:08 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-15 20:10:08 - model_training - INFO - 
分类报告:
2025-07-15 20:10:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-15 20:10:08 - model_training - INFO - 训练时间: 0.10 秒
2025-07-15 20:10:08 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:10:08 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:10:10 - model_training - INFO - 模型名称: LightGBM
2025-07-15 20:10:10 - model_training - INFO - 准确率: 0.7500
2025-07-15 20:10:10 - model_training - INFO - AUC: 0.8568
2025-07-15 20:10:10 - model_training - INFO - 混淆矩阵:
2025-07-15 20:10:10 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-07-15 20:10:10 - model_training - INFO - 
分类报告:
2025-07-15 20:10:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-07-15 20:10:10 - model_training - INFO - 训练时间: 1.49 秒
2025-07-15 20:10:10 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 20:10:10 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 20:10:10 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 20:10:10 - model_training - INFO - 准确率: 0.8250
2025-07-15 20:10:10 - model_training - INFO - AUC: 0.9028
2025-07-15 20:10:10 - model_training - INFO - 混淆矩阵:
2025-07-15 20:10:10 - model_training - INFO - 
[[19  4]
 [ 3 14]]
2025-07-15 20:10:10 - model_training - INFO - 
分类报告:
2025-07-15 20:10:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.83      0.84        23
           1       0.78      0.82      0.80        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.83      0.82      0.83        40

2025-07-15 20:10:10 - model_training - INFO - 训练时间: 0.02 秒
2025-07-15 20:10:10 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 20:10:10 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 20:10:19 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 9)
2025-07-15 20:10:19 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 20:10:19 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 259, 'max_depth': 20, 'min_samples_split': 4, 'min_samples_leaf': 6, 'max_features': 'log2'}
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9709
2025-07-15 20:10:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250715_201030.html
2025-07-15 20:10:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250715_201030.html
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.26 秒
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 20
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 54, 'max_depth': 8, 'learning_rate': 0.06708292487614473, 'subsample': 0.7268981881756755, 'colsample_bytree': 0.6621441442264917}
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9725
2025-07-15 20:10:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250715_201033.html
2025-07-15 20:10:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250715_201033.html
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.18 秒
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 20
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 58, 'max_depth': 10, 'learning_rate': 0.12376958094247054, 'feature_fraction': 0.670670773570036, 'bagging_fraction': 0.7177785029988614}
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9676
2025-07-15 20:10:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250715_201035.html
2025-07-15 20:10:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250715_201035.html
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.64 秒
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 20
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 8.751786349278616, 'solver': 'liblinear'}
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9492
2025-07-15 20:10:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250715_201035.html
2025-07-15 20:10:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250715_201035.html
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.48 秒
