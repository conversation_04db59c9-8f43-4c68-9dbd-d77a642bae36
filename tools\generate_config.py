#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件生成工具
帮助用户快速生成模型数据映射配置文件
"""

import json
import argparse
from pathlib import Path

# 导入统一的模型配置
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'code'))

from config import MODEL_NAMES

# 支持的模型列表
SUPPORTED_MODELS = MODEL_NAMES

def generate_config_interactive():
    """
    交互式生成配置文件
    """
    print("=== 多数据源集成学习配置文件生成器 ===")
    print("支持的模型:", ', '.join(SUPPORTED_MODELS))
    print()
    
    model_data_mapping = {}
    
    while True:
        print(f"\n当前已配置 {len(model_data_mapping)} 个模型")
        if model_data_mapping:
            print("已配置的模型:")
            for model, path in model_data_mapping.items():
                print(f"  - {model}: {path}")
        
        print("\n请选择操作:")
        print("1. 添加模型数据映射")
        print("2. 删除模型数据映射")
        print("3. 保存配置文件")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == '1':
            add_model_mapping(model_data_mapping)
        elif choice == '2':
            remove_model_mapping(model_data_mapping)
        elif choice == '3':
            save_config_file(model_data_mapping)
        elif choice == '4':
            break
        else:
            print("无效选择，请重新输入")

def add_model_mapping(model_data_mapping):
    """
    添加模型数据映射
    """
    print("\n=== 添加模型数据映射 ===")
    
    # 显示可用模型
    available_models = [m for m in SUPPORTED_MODELS if m not in model_data_mapping]
    if not available_models:
        print("所有模型都已配置")
        return
    
    print("可用模型:")
    for i, model in enumerate(available_models, 1):
        print(f"  {i}. {model}")
    
    # 选择模型
    try:
        model_choice = int(input("请选择模型编号: ")) - 1
        if 0 <= model_choice < len(available_models):
            selected_model = available_models[model_choice]
        else:
            print("无效的模型编号")
            return
    except ValueError:
        print("请输入有效的数字")
        return
    
    # 输入数据路径
    data_path = input(f"请输入 {selected_model} 的数据文件路径: ").strip()
    
    # 验证路径
    if not data_path:
        print("数据路径不能为空")
        return
    
    # 检查文件是否存在（可选）
    if Path(data_path).exists():
        print(f"✓ 文件存在: {data_path}")
    else:
        confirm = input(f"⚠ 文件不存在: {data_path}，是否继续添加? (y/n): ")
        if confirm.lower() != 'y':
            return
    
    # 添加映射
    model_data_mapping[selected_model] = data_path
    print(f"✓ 成功添加: {selected_model} -> {data_path}")

def remove_model_mapping(model_data_mapping):
    """
    删除模型数据映射
    """
    if not model_data_mapping:
        print("没有已配置的模型")
        return
    
    print("\n=== 删除模型数据映射 ===")
    models = list(model_data_mapping.keys())
    
    for i, model in enumerate(models, 1):
        print(f"  {i}. {model}: {model_data_mapping[model]}")
    
    try:
        choice = int(input("请选择要删除的模型编号: ")) - 1
        if 0 <= choice < len(models):
            removed_model = models[choice]
            del model_data_mapping[removed_model]
            print(f"✓ 成功删除: {removed_model}")
        else:
            print("无效的模型编号")
    except ValueError:
        print("请输入有效的数字")

def save_config_file(model_data_mapping):
    """
    保存配置文件
    """
    if not model_data_mapping:
        print("没有配置任何模型，无法保存")
        return
    
    print("\n=== 保存配置文件 ===")
    
    # 输入保存路径
    default_path = "config/model_data_config.json"
    save_path = input(f"请输入保存路径 (默认: {default_path}): ").strip()
    
    if not save_path:
        save_path = default_path
    
    # 创建目录
    save_path = Path(save_path)
    save_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 生成完整配置
    config = {
        "description": "多数据源集成学习配置文件",
        "created_by": "配置文件生成工具",
        "model_count": len(model_data_mapping),
        "model_data_mapping": model_data_mapping,
        "usage_example": {
            "command": "python main.py --model All --mode multi_data_ensemble --model_data_config " + str(save_path),
            "description": "使用此配置文件运行多数据源集成学习"
        },
        "supported_ensemble_methods": ["voting", "stacking", "weighted"],
        "supported_data_strategies": ["unified", "combined", "original"]
    }
    
    # 保存文件
    try:
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✓ 配置文件已保存至: {save_path}")
        print(f"✓ 配置了 {len(model_data_mapping)} 个模型")
        
        # 显示使用示例
        print("\n使用示例:")
        print(f"python main.py --model All --mode multi_data_ensemble --model_data_config {save_path}")
        
    except Exception as e:
        print(f"保存失败: {e}")

def generate_config_from_args(args):
    """
    从命令行参数生成配置文件
    """
    if len(args.models) != len(args.data_paths):
        print("错误: 模型数量和数据路径数量不匹配")
        return
    
    # 验证模型名称
    for model in args.models:
        if model not in SUPPORTED_MODELS:
            print(f"错误: 不支持的模型 '{model}'")
            print(f"支持的模型: {', '.join(SUPPORTED_MODELS)}")
            return
    
    # 创建映射
    model_data_mapping = dict(zip(args.models, args.data_paths))
    
    # 生成配置
    config = {
        "description": "多数据源集成学习配置文件",
        "created_by": "命令行工具",
        "model_count": len(model_data_mapping),
        "model_data_mapping": model_data_mapping,
        "usage_example": {
            "command": f"python main.py --model All --mode multi_data_ensemble --model_data_config {args.output}",
            "description": "使用此配置文件运行多数据源集成学习"
        },
        "supported_ensemble_methods": ["voting", "stacking", "weighted"],
        "supported_data_strategies": ["unified", "combined", "original"]
    }
    
    # 保存文件
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✓ 配置文件已生成: {output_path}")
        print(f"✓ 配置了 {len(model_data_mapping)} 个模型:")
        for model, path in model_data_mapping.items():
            print(f"  - {model}: {path}")
    except Exception as e:
        print(f"生成失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='多数据源集成学习配置文件生成工具')
    parser.add_argument('--interactive', '-i', action='store_true',
                        help='交互式生成配置文件')
    parser.add_argument('--models', '-m', nargs='+',
                        help='模型列表')
    parser.add_argument('--data_paths', '-d', nargs='+',
                        help='数据路径列表')
    parser.add_argument('--output', '-o', default='config/model_data_config.json',
                        help='输出配置文件路径')
    parser.add_argument('--list_models', '-l', action='store_true',
                        help='列出支持的模型')
    
    args = parser.parse_args()
    
    if args.list_models:
        print("支持的模型:")
        for model in SUPPORTED_MODELS:
            print(f"  - {model}")
        return
    
    if args.interactive:
        generate_config_interactive()
    elif args.models and args.data_paths:
        generate_config_from_args(args)
    else:
        print("请选择交互式模式 (--interactive) 或提供模型和数据路径参数")
        print("使用 --help 查看详细帮助")
        print("\n快速开始:")
        print("  交互式: python generate_config.py --interactive")
        print("  命令行: python generate_config.py -m RandomForest XGBoost -d data1.csv data2.csv")

if __name__ == '__main__':
    main()