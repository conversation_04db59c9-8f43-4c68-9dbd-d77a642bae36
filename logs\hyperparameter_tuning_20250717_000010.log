2025-07-17 00:00:10 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-07-17 00:00:11 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 9, 'min_samples_split': 35, 'min_samples_leaf': 17, 'criterion': 'gini', 'class_weight': None, 'max_features': None}
2025-07-17 00:00:11 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.7820
2025-07-17 00:00:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:00:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250717_000011.html
2025-07-17 00:00:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:00:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250717_000011.html
2025-07-17 00:00:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.56 秒
2025-07-17 00:00:12 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-07-17 00:00:43 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 100, 'max_depth': 30, 'min_samples_split': 9, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-07-17 00:00:43 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8299
2025-07-17 00:00:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:00:43 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250717_000043.html
2025-07-17 00:00:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:00:44 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250717_000043.html
2025-07-17 00:00:44 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 32.01 秒
2025-07-17 00:00:44 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-07-17 00:00:44 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:44 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:44 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:44 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:44 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-17 00:00:54 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 106, 'max_depth': 9, 'learning_rate': 0.04701327276592608, 'subsample': 0.5551931672114783, 'colsample_bytree': 0.7787046544505262}
2025-07-17 00:00:54 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.8248
2025-07-17 00:00:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:00:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250717_000054.html
2025-07-17 00:00:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:00:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250717_000054.html
2025-07-17 00:00:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 10.43 秒
2025-07-17 00:00:54 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-07-17 00:01:00 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 89, 'max_depth': 2, 'learning_rate': 0.06458302624145651, 'feature_fraction': 0.8702784899540901, 'bagging_fraction': 0.8353036696654255}
2025-07-17 00:01:00 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.8113
2025-07-17 00:01:00 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:01:00 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250717_000100.html
2025-07-17 00:01:00 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:01:00 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250717_000100.html
2025-07-17 00:01:00 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 6.38 秒
2025-07-17 00:01:00 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-07-17 00:01:00 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:03 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:06 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:08 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:12 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:17 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:19 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:21 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:24 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:26 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:30 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:42 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:01:44 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:01 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:03 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:05 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:09 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:11 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:14 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:17 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:19 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:22 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:24 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:26 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:29 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:31 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:36 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:39 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:41 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:46 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:48 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:50 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:53 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:55 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:02:57 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:00 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:08 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:10 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:12 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:14 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:16 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:18 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:20 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:22 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:25 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:27 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:32 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:35 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:37 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-07-17 00:03:47 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 65, 'depth': 10, 'learning_rate': 0.012843895102950073, 'l2_leaf_reg': 5.520659604094637, 'bagging_temperature': 0.3847812079573546}
2025-07-17 00:03:47 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.8216
2025-07-17 00:03:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:03:47 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250717_000347.html
2025-07-17 00:03:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:03:47 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250717_000347.html
2025-07-17 00:03:47 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 166.42 秒
2025-07-17 00:03:47 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-07-17 00:03:48 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 0.10234184579111136, 'solver': 'lbfgs'}
2025-07-17 00:03:48 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.8278
2025-07-17 00:03:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:03:48 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250717_000348.html
2025-07-17 00:03:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:03:48 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250717_000348.html
2025-07-17 00:03:48 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.27 秒
2025-07-17 00:03:48 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-07-17 00:03:51 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 1.4558266153918618, 'kernel': 'linear'}
2025-07-17 00:03:51 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.8339
2025-07-17 00:03:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:03:51 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250717_000351.html
2025-07-17 00:03:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:03:51 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250717_000351.html
2025-07-17 00:03:51 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.00 秒
2025-07-17 00:03:51 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-07-17 00:03:52 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 8}
2025-07-17 00:03:52 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.8203
2025-07-17 00:03:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:03:52 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250717_000352.html
2025-07-17 00:03:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:03:53 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250717_000353.html
2025-07-17 00:03:53 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.50 秒
2025-07-17 00:03:53 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-07-17 00:03:53 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-07-17 00:03:53 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-07-17 00:06:40 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'hidden_layer_sizes': (100,), 'alpha': 0.003139546248943988}
2025-07-17 00:06:40 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.7368
2025-07-17 00:06:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:06:40 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250717_000640.html
2025-07-17 00:06:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-17 00:06:40 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250717_000640.html
2025-07-17 00:06:40 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 167.65 秒
