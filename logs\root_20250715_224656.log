2025-07-15 22:46:58 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:46:58 - GUI - INFO - GUI界面初始化完成
2025-07-15 22:47:38 - model_ensemble - INFO - ============================================================
2025-07-15 22:47:38 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:47:38 - model_ensemble - INFO - ============================================================
2025-07-15 22:47:38 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'SVM', 'KNN']
2025-07-15 22:47:38 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 22:47:38 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:47:38 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:47:38 - model_training - INFO - 模型名称: Random Forest
2025-07-15 22:47:38 - model_training - INFO - 准确率: 0.7250
2025-07-15 22:47:38 - model_training - INFO - AUC: 0.8184
2025-07-15 22:47:38 - model_training - INFO - 混淆矩阵:
2025-07-15 22:47:38 - model_training - INFO - 
[[19  4]
 [ 7 10]]
2025-07-15 22:47:38 - model_training - INFO - 
分类报告:
2025-07-15 22:47:38 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.73      0.83      0.78        23
           1       0.71      0.59      0.65        17

    accuracy                           0.72        40
   macro avg       0.72      0.71      0.71        40
weighted avg       0.72      0.72      0.72        40

2025-07-15 22:47:38 - model_training - INFO - 训练时间: 0.09 秒
2025-07-15 22:47:38 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 22:47:38 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 22:47:38 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:47:38 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:47:38 - model_training - INFO - 模型名称: XGBoost
2025-07-15 22:47:38 - model_training - INFO - 准确率: 0.8000
2025-07-15 22:47:38 - model_training - INFO - AUC: 0.8261
2025-07-15 22:47:38 - model_training - INFO - 混淆矩阵:
2025-07-15 22:47:38 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-07-15 22:47:38 - model_training - INFO - 
分类报告:
2025-07-15 22:47:38 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-07-15 22:47:38 - model_training - INFO - 训练时间: 0.10 秒
2025-07-15 22:47:38 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 22:47:38 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 22:47:38 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:47:38 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:47:38 - model_training - INFO - 模型名称: SVM
2025-07-15 22:47:38 - model_training - INFO - 准确率: 0.6500
2025-07-15 22:47:38 - model_training - INFO - AUC: 0.8133
2025-07-15 22:47:38 - model_training - INFO - 混淆矩阵:
2025-07-15 22:47:38 - model_training - INFO - 
[[22  1]
 [13  4]]
2025-07-15 22:47:38 - model_training - INFO - 
分类报告:
2025-07-15 22:47:38 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.63      0.96      0.76        23
           1       0.80      0.24      0.36        17

    accuracy                           0.65        40
   macro avg       0.71      0.60      0.56        40
weighted avg       0.70      0.65      0.59        40

2025-07-15 22:47:38 - model_training - INFO - 训练时间: 0.03 秒
2025-07-15 22:47:38 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-15 22:47:38 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-15 22:47:38 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:47:38 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-15 22:47:38 - model_training - INFO - 模型名称: KNN
2025-07-15 22:47:38 - model_training - INFO - 准确率: 0.6250
2025-07-15 22:47:38 - model_training - INFO - AUC: 0.7647
2025-07-15 22:47:38 - model_training - INFO - 混淆矩阵:
2025-07-15 22:47:38 - model_training - INFO - 
[[17  6]
 [ 9  8]]
2025-07-15 22:47:38 - model_training - INFO - 
分类报告:
2025-07-15 22:47:38 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.74      0.69        23
           1       0.57      0.47      0.52        17

    accuracy                           0.62        40
   macro avg       0.61      0.60      0.61        40
weighted avg       0.62      0.62      0.62        40

2025-07-15 22:47:38 - model_training - INFO - 训练时间: 0.02 秒
2025-07-15 22:47:38 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-15 22:47:38 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-15 22:47:38 - model_ensemble - INFO -   KNN 训练完成
2025-07-15 22:47:38 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 22:47:38 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:47:38 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:47:38 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:47:38 - model_ensemble - INFO -     voting_soft - 准确率: 0.7750, F1: 0.7710
2025-07-15 22:47:38 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:47:38 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:47:39 - model_ensemble - INFO -     voting_hard - 准确率: 0.6500, F1: 0.6221
2025-07-15 22:47:39 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:47:39 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:47:39 - model_ensemble - INFO -   stacking - 准确率: 0.8000, F1: 0.7979
2025-07-15 22:47:39 - model_ensemble - INFO - ============================================================
2025-07-15 22:47:39 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:47:39 - model_ensemble - INFO - ============================================================
2025-07-15 22:47:39 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 22:47:39 - model_ensemble - INFO - 最佳F1分数: 0.7979
2025-07-15 22:47:39 - model_ensemble - INFO - 最佳准确率: 0.8000
2025-07-15 22:47:39 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:47:39 - model_ensemble - INFO -   voting_soft     - 准确率: 0.7750, 精确率: 0.7762, 召回率: 0.7750, F1: 0.7710, AUC: 0.8389
2025-07-15 22:47:39 - model_ensemble - INFO -   voting_hard     - 准确率: 0.6500, 精确率: 0.6543, 召回率: 0.6500, F1: 0.6221, AUC: 0.0000
2025-07-15 22:47:39 - model_ensemble - INFO -   stacking        - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.7979, AUC: 0.8389
2025-07-15 22:47:39 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:47:39 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_224739.joblib
2025-07-15 22:47:39 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:47:39 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-15 22:47:39 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:47:39 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:47:39 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:47:39 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:47:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([ 0.00213176,  0.05030955,  0.03986173,  0.02852532, -0.00590589,
        0.05677564,  0.01851328,  0.0354003 ])
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([-0.00213176, -0.05030955, -0.03986173, -0.02852532,  0.00590589,
       -0.05677564, -0.01851328, -0.0354003 ])
2025-07-15 22:47:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([-0.10390483,  0.04898372,  0.11143058,  0.08151116, -0.0122717 ,
        0.00305509,  0.04190109, -0.00675103])
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([ 0.10390483, -0.04898372, -0.11143059, -0.08151116,  0.0122717 ,
       -0.00305509, -0.04190109,  0.00675102])
2025-07-15 22:47:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([-0.03241987, -0.05779328,  0.00475194, -0.05422751,  0.03189873,
        0.00533398, -0.08298787,  0.00681387])
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([ 0.03241987,  0.05779328, -0.00475193,  0.05422751, -0.03189873,
       -0.00533398,  0.08298787, -0.00681387])
2025-07-15 22:47:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([-0.02917907,  0.05830787, -0.00255215, -0.04587111, -0.02918547,
        0.01346993, -0.04490818,  0.03063083])
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([ 0.02917907, -0.05830787,  0.00255215,  0.04587111,  0.02918547,
       -0.01346993,  0.04490818, -0.03063083])
2025-07-15 22:47:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([-0.00701386,  0.08410636,  0.06286603,  0.03865485, -0.01503411,
        0.0190117 , -0.02855641,  0.03171529])
2025-07-15 22:47:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:40 - shap - INFO - phi = array([ 0.00701386, -0.08410636, -0.06286603, -0.03865485,  0.01503411,
       -0.0190117 ,  0.02855642, -0.03171529])
2025-07-15 22:47:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([ 0.01358008,  0.07656354, -0.00185394,  0.01737292, -0.02050329,
        0.10312174,  0.05119453,  0.0453699 ])
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([-0.01358008, -0.07656353,  0.00185394, -0.01737292,  0.02050329,
       -0.10312174, -0.05119453, -0.0453699 ])
2025-07-15 22:47:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([ 0.11731819,  0.0893872 , -0.00297207, -0.01559541, -0.0335771 ,
        0.01696067,  0.0366176 ,  0.01349676])
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([-0.11731819, -0.0893872 ,  0.00297207,  0.01559541,  0.0335771 ,
       -0.01696067, -0.0366176 , -0.01349676])
2025-07-15 22:47:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([-0.12312744,  0.03696558, -0.06941298, -0.02788696, -0.02693533,
       -0.10816232,  0.02357337, -0.05452676])
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([ 0.12312744, -0.03696558,  0.06941298,  0.02788696,  0.02693533,
        0.10816232, -0.02357337,  0.05452676])
2025-07-15 22:47:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([ 0.06393747, -0.32749003, -0.00181651,  0.00838521,  0.12853897,
       -0.13716276, -0.01394645, -0.02908527])
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([-0.06393747,  0.32749003,  0.00181651, -0.00838521, -0.12853897,
        0.13716276,  0.01394645,  0.02908528])
2025-07-15 22:47:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([ 0.00897411,  0.01828206,  0.0145201 ,  0.00581754, -0.02484202,
        0.12876746,  0.01944463,  0.02976145])
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([-0.00897411, -0.01828206, -0.0145201 , -0.00581754,  0.02484202,
       -0.12876746, -0.01944463, -0.02976145])
2025-07-15 22:47:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([ 0.02822409,  0.10774046, -0.00092577,  0.03156112, -0.00604147,
        0.04457254, -0.00995581,  0.01562523])
2025-07-15 22:47:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:41 - shap - INFO - phi = array([-0.02822409, -0.10774046,  0.00092577, -0.03156112,  0.00604147,
       -0.04457254,  0.00995581, -0.01562523])
2025-07-15 22:47:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([-0.06677769,  0.04622865,  0.10405534,  0.07463281, -0.00803991,
        0.07479325,  0.03148107,  0.00215473])
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([ 0.06677769, -0.04622865, -0.10405534, -0.07463281,  0.00803991,
       -0.07479326, -0.03148107, -0.00215473])
2025-07-15 22:47:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([-0.00276252, -0.05505303, -0.08833402, -0.06802561,  0.0250683 ,
       -0.04520487, -0.03732164,  0.0473731 ])
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([ 0.00276252,  0.05505303,  0.08833402,  0.06802562, -0.02506829,
        0.04520487,  0.03732164, -0.0473731 ])
2025-07-15 22:47:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([-0.02964502, -0.23567085, -0.04919549, -0.04171137, -0.01648892,
        0.1188723 , -0.04224724, -0.10957479])
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([ 0.02964502,  0.23567085,  0.04919549,  0.04171137,  0.01648892,
       -0.1188723 ,  0.04224724,  0.10957479])
2025-07-15 22:47:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([-0.01257659, -0.23760879,  0.03332401, -0.00547361, -0.01054586,
       -0.00888256, -0.11383406, -0.05909601])
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([ 0.01257659,  0.23760879, -0.03332401,  0.00547361,  0.01054586,
        0.00888256,  0.11383406,  0.05909601])
2025-07-15 22:47:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([ 0.06095101,  0.12043675,  0.00573863,  0.02574511, -0.01994053,
        0.08690599,  0.0220497 ,  0.02240652])
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([-0.06095101, -0.12043675, -0.00573863, -0.02574511,  0.01994053,
       -0.08690599, -0.0220497 , -0.02240652])
2025-07-15 22:47:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([-0.00042884, -0.02612001, -0.09715873, -0.10369666, -0.03377713,
        0.06827195,  0.01073316, -0.00245121])
2025-07-15 22:47:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:42 - shap - INFO - phi = array([ 0.00042884,  0.02612001,  0.09715873,  0.10369666,  0.03377713,
       -0.06827195, -0.01073316,  0.00245121])
2025-07-15 22:47:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([ 0.00413831,  0.10016152,  0.01017219,  0.00462041, -0.01350796,
        0.10396659,  0.03371187,  0.03272825])
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([-0.00413831, -0.10016152, -0.0101722 , -0.00462041,  0.01350796,
       -0.10396659, -0.03371187, -0.03272825])
2025-07-15 22:47:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([ 0.04279906, -0.26211816, -0.00464722,  0.01864183,  0.06393648,
       -0.29681101, -0.02773735,  0.01241781])
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([-0.04279906,  0.26211816,  0.00464722, -0.01864183, -0.06393648,
        0.29681101,  0.02773735, -0.01241781])
2025-07-15 22:47:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([ 0.01402696, -0.01168654, -0.08856726, -0.00038023, -0.05942994,
        0.07870594,  0.00063924, -0.03220866])
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([-0.01402696,  0.01168654,  0.08856726,  0.00038023,  0.05942994,
       -0.07870594, -0.00063924,  0.03220866])
2025-07-15 22:47:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([-0.04058144,  0.11816118,  0.05054588, -0.01893557, -0.0377666 ,
       -0.05307865,  0.01894915,  0.04014888])
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([ 0.04058144, -0.11816118, -0.05054589,  0.01893557,  0.0377666 ,
        0.05307865, -0.01894915, -0.04014888])
2025-07-15 22:47:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([-0.08418565,  0.06879025,  0.01497945,  0.04640877, -0.01418837,
        0.11112333,  0.04652053,  0.04874672])
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([ 0.08418565, -0.06879026, -0.01497945, -0.04640877,  0.01418837,
       -0.11112333, -0.04652053, -0.04874672])
2025-07-15 22:47:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([-0.00518663,  0.04284582,  0.00912442,  0.04245096, -0.02197397,
        0.10591018,  0.02334455,  0.03386555])
2025-07-15 22:47:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:43 - shap - INFO - phi = array([ 0.00518663, -0.04284582, -0.00912442, -0.04245096,  0.02197397,
       -0.10591018, -0.02334455, -0.03386555])
2025-07-15 22:47:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([-0.04828706, -0.05175344,  0.03424631,  0.03943177,  0.06681984,
        0.06022887,  0.02420029, -0.0349574 ])
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([ 0.04828706,  0.05175344, -0.03424631, -0.03943177, -0.06681984,
       -0.06022887, -0.02420029,  0.03495741])
2025-07-15 22:47:44 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([-0.01207638, -0.08112719, -0.08253743, -0.08426387, -0.02479541,
        0.1358145 ,  0.01356563, -0.05478152])
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([ 0.01207638,  0.08112719,  0.08253743,  0.08426387,  0.02479541,
       -0.1358145 , -0.01356563,  0.05478152])
2025-07-15 22:47:44 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([-0.10786921, -0.02349969, -0.07468691,  0.0477793 ,  0.03583675,
        0.08738233,  0.00426265, -0.04639629])
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([ 0.10786921,  0.02349969,  0.07468691, -0.0477793 , -0.03583675,
       -0.08738233, -0.00426265,  0.04639629])
2025-07-15 22:47:44 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([ 0.03250407, -0.18865298,  0.01250266,  0.01296291,  0.0358156 ,
       -0.16891105,  0.04851579, -0.05484469])
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([-0.03250407,  0.18865298, -0.01250266, -0.01296291, -0.0358156 ,
        0.16891105, -0.04851579,  0.05484469])
2025-07-15 22:47:44 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([ 0.024069  ,  0.10978649,  0.04646048,  0.03710016, -0.02358533,
        0.09288515,  0.00145619,  0.01279387])
2025-07-15 22:47:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:44 - shap - INFO - phi = array([-0.024069  , -0.10978649, -0.04646048, -0.03710016,  0.02358533,
       -0.09288515, -0.00145619, -0.01279387])
2025-07-15 22:47:44 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([-0.05308714,  0.03219765, -0.00561704, -0.0191612 , -0.05007256,
       -0.28448845,  0.03884599,  0.03678876])
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([ 0.05308714, -0.03219765,  0.00561704,  0.0191612 ,  0.05007256,
        0.28448845, -0.03884599, -0.03678876])
2025-07-15 22:47:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([-0.08841304, -0.02382412, -0.01062736, -0.01358013, -0.04714112,
       -0.01834618,  0.00278737,  0.00933211])
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([ 0.08841304,  0.02382412,  0.01062736,  0.01358013,  0.04714112,
        0.01834618, -0.00278737, -0.00933211])
2025-07-15 22:47:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([ 0.11180461,  0.1394776 , -0.01064878, -0.05057599,  0.00279946,
        0.0456544 ,  0.02317714, -0.00918274])
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([-0.11180461, -0.1394776 ,  0.01064878,  0.05057599, -0.00279946,
       -0.0456544 , -0.02317714,  0.00918274])
2025-07-15 22:47:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([ 0.06846226, -0.23959598, -0.01084335, -0.01934518,  0.028501  ,
       -0.22286173, -0.01824881,  0.00601867])
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([-0.06846226,  0.23959598,  0.01084335,  0.01934518, -0.028501  ,
        0.22286173,  0.01824881, -0.00601867])
2025-07-15 22:47:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([-0.08209912,  0.16004452, -0.03099736, -0.02611772,  0.01349316,
        0.07125111, -0.09281188, -0.06596179])
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([ 0.08209912, -0.16004452,  0.03099736,  0.02611772, -0.01349316,
       -0.07125111,  0.09281188,  0.06596178])
2025-07-15 22:47:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([-0.03056076, -0.14631147, -0.02095987,  0.04828497,  0.01523338,
        0.05530147, -0.00686913,  0.02118265])
2025-07-15 22:47:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:45 - shap - INFO - phi = array([ 0.03056076,  0.14631147,  0.02095988, -0.04828497, -0.01523338,
       -0.05530147,  0.00686913, -0.02118265])
2025-07-15 22:47:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([ 0.18100568, -0.10984119, -0.00794663, -0.06060494,  0.09549098,
       -0.23419452, -0.01044885,  0.02275328])
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([-0.18100568,  0.10984119,  0.00794663,  0.06060494, -0.09549098,
        0.23419452,  0.01044885, -0.02275328])
2025-07-15 22:47:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([ 0.01584874,  0.10771087,  0.06613503,  0.03089066, -0.0141795 ,
        0.09826978,  0.03814686, -0.02518589])
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([-0.01584874, -0.10771087, -0.06613503, -0.03089066,  0.0141795 ,
       -0.09826978, -0.03814686,  0.02518589])
2025-07-15 22:47:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([-0.07202316,  0.17578465,  0.04083328,  0.00315535,  0.09069875,
        0.00988154,  0.0014591 , -0.01451311])
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([ 0.07202316, -0.17578465, -0.04083328, -0.00315535, -0.09069875,
       -0.00988154, -0.0014591 ,  0.01451311])
2025-07-15 22:47:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([ 0.01542916,  0.14524891,  0.02778878,  0.0183104 , -0.02630608,
        0.07133746, -0.01785783,  0.02677135])
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([-0.01542916, -0.14524891, -0.02778878, -0.0183104 ,  0.02630608,
       -0.07133745,  0.01785784, -0.02677135])
2025-07-15 22:47:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([ 0.09200041,  0.01460948, -0.07347461, -0.05997638, -0.02517774,
       -0.07175966, -0.02368516,  0.04763806])
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([-0.09200041, -0.01460948,  0.07347461,  0.05997638,  0.02517774,
        0.07175966,  0.02368516, -0.04763806])
2025-07-15 22:47:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([ 0.13500034,  0.12601607,  0.04643864,  0.05318594, -0.01291812,
       -0.21776114, -0.00367409, -0.03241675])
2025-07-15 22:47:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:46 - shap - INFO - phi = array([-0.13500034, -0.12601607, -0.04643864, -0.05318594,  0.01291812,
        0.21776114,  0.00367409,  0.03241675])
2025-07-15 22:47:50 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_soft
2025-07-15 22:47:50 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:47:50 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:47:50 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:47:50 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:47:50 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:47:50 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:47:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:51 - shap - INFO - phi = array([ 0.02925595, -0.100625  , -0.03776786, -0.020625  ,  0.01735119,
       -0.07907738, -0.00377976, -0.02973214])
2025-07-15 22:47:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:51 - shap - INFO - phi = array([ 0.0635119 , -0.11113095, -0.08142857, -0.0960119 ,  0.01982143,
       -0.02089286, -0.01869048,  0.01982143])
2025-07-15 22:47:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:52 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:52 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:52 - shap - INFO - phi = array([ 0.01306548, -0.03175595, -0.10306548,  0.03770833, -0.11681548,
       -0.06806548,  0.07467262, -0.03074405])
2025-07-15 22:47:52 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:52 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:52 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:52 - shap - INFO - phi = array([ 0.05258929, -0.20818452, -0.013125  ,  0.0141369 ,  0.04931548,
       -0.07616071,  0.03306548, -0.0766369 ])
2025-07-15 22:47:52 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:52 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:52 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:52 - shap - INFO - phi = array([ 0.03089286, -0.15696429, -0.05636905, -0.02375   ,  0.03333333,
       -0.05827381,  0.02696429, -0.02083333])
2025-07-15 22:47:52 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:53 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:53 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:53 - shap - INFO - phi = array([ 0.03904762, -0.11422619, -0.00434524, -0.01029762,  0.02797619,
       -0.11529762, -0.01797619, -0.02988095])
2025-07-15 22:47:53 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:53 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:53 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:53 - shap - INFO - phi = array([-0.09386905, -0.11946429,  0.0002381 ,  0.01291667,  0.03952381,
       -0.04803571, -0.01136905, -0.00494048])
2025-07-15 22:47:53 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:53 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:53 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:53 - shap - INFO - phi = array([ 0.24199405, -0.0471131 ,  0.14110119,  0.06931548,  0.08139881,
        0.20752976, -0.0121131 ,  0.0928869 ])
2025-07-15 22:47:53 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:54 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:54 - shap - INFO - phi = array([-0.08636905,  0.7364881 ,  0.0114881 , -0.01083333, -0.15380952,
        0.18541667,  0.02267857,  0.06994048])
2025-07-15 22:47:54 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:54 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:54 - shap - INFO - phi = array([ 0.01032738, -0.05014881, -0.01473214, -0.00407738,  0.03098214,
       -0.15901786, -0.00360119, -0.03473214])
2025-07-15 22:47:54 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:54 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:54 - shap - INFO - phi = array([-0.013125  , -0.125625  , -0.04997024, -0.01604167,  0.01931548,
       -0.02110119, -0.00181548, -0.0166369 ])
2025-07-15 22:47:54 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:55 - shap - INFO - phi = array([ 0.0447619 , -0.10160714, -0.06130952, -0.06571429,  0.02369048,
       -0.06994048, -0.01404762,  0.01916667])
2025-07-15 22:47:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:55 - shap - INFO - phi = array([ 0.05547619,  0.11053571,  0.25315476,  0.2160119 ,  0.00035714,
        0.09839286,  0.12386905, -0.08279762])
2025-07-15 22:47:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:55 - shap - INFO - phi = array([ 0.07443452,  0.37550595,  0.06366071,  0.06604167,  0.0271131 ,
       -0.21014881,  0.02383929,  0.35455357])
2025-07-15 22:47:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:56 - shap - INFO - phi = array([ 0.0385119 ,  0.50791667, -0.05494048,  0.00684524,  0.00761905,
        0.02035714,  0.15202381,  0.09666667])
2025-07-15 22:47:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:56 - shap - INFO - phi = array([-0.02157738, -0.12931548, -0.0066369 , -0.00758929,  0.03044643,
       -0.07752976, -0.00050595, -0.01229167])
2025-07-15 22:47:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:56 - shap - INFO - phi = array([ 0.04104167,  0.08139881,  0.21931548,  0.265625  ,  0.14657738,
       -0.03044643,  0.0278869 ,  0.02360119])
2025-07-15 22:47:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:57 - shap - INFO - phi = array([ 0.02693452, -0.11425595, -0.00901786,  0.0046131 ,  0.03008929,
       -0.12407738, -0.01145833, -0.02782738])
2025-07-15 22:47:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:57 - shap - INFO - phi = array([-0.06467262,  0.46848214,  0.01151786, -0.01705357, -0.07598214,
        0.43116071,  0.04824405, -0.02669643])
2025-07-15 22:47:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:57 - shap - INFO - phi = array([-0.0885119 , -0.04583333,  0.06434524, -0.05113095,  0.06517857,
       -0.18130952, -0.0125    ,  0.0247619 ])
2025-07-15 22:47:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:58 - shap - INFO - phi = array([ 0.00434524, -0.19559524, -0.06517857,  0.00678571,  0.03779762,
        0.02940476, -0.00845238, -0.03410714])
2025-07-15 22:47:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:58 - shap - INFO - phi = array([ 0.10184524, -0.11375   , -0.0160119 , -0.0410119 ,  0.04005952,
       -0.13172619, -0.01744048, -0.04696429])
2025-07-15 22:47:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:58 - shap - INFO - phi = array([ 0.04044643, -0.09169643, -0.01092262, -0.04830357,  0.028125  ,
       -0.12306548, -0.00032738, -0.01925595])
2025-07-15 22:47:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:59 - shap - INFO - phi = array([ 0.04502976, -0.00520833, -0.11872024, -0.05300595, -0.044375  ,
       -0.06622024, -0.01300595,  0.03050595])
2025-07-15 22:47:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:59 - shap - INFO - phi = array([-0.09300595,  0.06485119,  0.07502976,  0.1016369 ,  0.04229167,
       -0.34723214, -0.05223214, -0.01633929])
2025-07-15 22:47:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:47:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:47:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:47:59 - shap - INFO - phi = array([ 0.13916667, -0.05452381,  0.08214286, -0.0972619 , -0.10654762,
       -0.21720238, -0.00440476,  0.03363095])
2025-07-15 22:47:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:00 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:00 - shap - INFO - phi = array([-0.02526786,  0.29401786, -0.175625  , -0.26056548, -0.13723214,
        0.31395833, -0.34747024,  0.11318452])
2025-07-15 22:48:00 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:00 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:00 - shap - INFO - phi = array([-0.01276786, -0.10675595, -0.02318452, -0.00800595,  0.02032738,
       -0.07705357, -0.00169643, -0.0158631 ])
2025-07-15 22:48:00 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:00 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:00 - shap - INFO - phi = array([ 0.1221131 , -0.00264881,  0.050625  ,  0.07354167,  0.10681548,
        0.49907738, -0.03258929, -0.04193452])
2025-07-15 22:48:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:01 - shap - INFO - phi = array([ 0.05044643, -0.02401786, -0.0383631 , -0.0858631 ,  0.04639881,
       -0.03270833, -0.06080357, -0.08008929])
2025-07-15 22:48:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:01 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:01 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:01 - shap - INFO - phi = array([-0.0502381 , -0.12154762, -0.00571429,  0.02452381, -0.02303571,
       -0.03113095, -0.00315476, -0.01470238])
2025-07-15 22:48:01 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:02 - shap - INFO - phi = array([-0.10080357,  0.38258929,  0.03360119,  0.05901786, -0.03229167,
        0.4116369 ,  0.02568452, -0.00443452])
2025-07-15 22:48:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:02 - shap - INFO - phi = array([ 0.0464881 , -0.25708333,  0.01315476,  0.01125   , -0.03678571,
       -0.07797619,  0.06      ,  0.01595238])
2025-07-15 22:48:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:02 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:02 - shap - INFO - phi = array([ 0.03907738,  0.04300595,  0.01008929, -0.10752976, -0.00895833,
       -0.1196131 , -0.00419643, -0.076875  ])
2025-07-15 22:48:02 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:03 - shap - INFO - phi = array([-0.38505952,  0.09172619, -0.03071429,  0.05803571, -0.24630952,
        0.34333333,  0.03940476, -0.09541667])
2025-07-15 22:48:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:03 - shap - INFO - phi = array([ 0.01416667, -0.1275    , -0.03744048, -0.02892857,  0.03315476,
       -0.07904762, -0.01136905,  0.01196429])
2025-07-15 22:48:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:03 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:03 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:03 - shap - INFO - phi = array([ 0.06127976, -0.1921131 , -0.03383929,  0.005625  , -0.07020833,
       -0.01455357,  0.00199405,  0.01681548])
2025-07-15 22:48:03 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:04 - shap - INFO - phi = array([ 0.01892857, -0.15452381, -0.0202381 , -0.00404762,  0.00505952,
       -0.04559524, -0.00202381, -0.02255952])
2025-07-15 22:48:04 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:04 - shap - INFO - phi = array([-0.24767857, -0.09214286,  0.06625   ,  0.05547619,  0.03392857,
        0.02636905,  0.01369048, -0.08089286])
2025-07-15 22:48:04 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:04 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:04 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:04 - shap - INFO - phi = array([-0.16223214, -0.16116071, -0.02705357, -0.03145833,  0.00830357,
        0.1358631 , -0.00699405,  0.01973214])
2025-07-15 22:48:08 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_hard
2025-07-15 22:48:08 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:48:08 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:48:08 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:48:08 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:48:08 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:48:08 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 22:48:08 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 22:48:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([ 0.01312068,  0.06992128,  0.06215197,  0.03521538, -0.01576479,
        0.05350269,  0.01132552,  0.03423892])
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([-0.01312068, -0.06992128, -0.06215197, -0.03521538,  0.01576479,
       -0.05350269, -0.01132552, -0.03423892])
2025-07-15 22:48:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([-0.09154864,  0.06696969,  0.13847918,  0.03916085, -0.01512463,
        0.00194028,  0.06146043,  0.01742965])
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([ 0.09154864, -0.06696969, -0.13847918, -0.03916085,  0.01512463,
       -0.00194028, -0.06146043, -0.01742965])
2025-07-15 22:48:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([-0.018888  , -0.02455332,  0.01683216, -0.069562  ,  0.06523176,
       -0.00572862, -0.08783596,  0.01619038])
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([ 0.018888  ,  0.02455332, -0.01683216,  0.069562  , -0.06523176,
        0.00572862,  0.08783596, -0.01619038])
2025-07-15 22:48:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([-0.0304703 ,  0.07467837,  0.01277726, -0.05432104, -0.04171423,
       -0.01593072, -0.07077463,  0.04058612])
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([ 0.0304703 , -0.07467837, -0.01277726,  0.05432104,  0.04171423,
        0.01593072,  0.07077463, -0.04058612])
2025-07-15 22:48:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([ 0.00813824,  0.0985105 ,  0.0939026 ,  0.0481338 , -0.02513206,
        0.00955678, -0.04345492,  0.03508592])
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([-0.00813824, -0.0985105 , -0.0939026 , -0.0481338 ,  0.02513206,
       -0.00955678,  0.04345492, -0.03508592])
2025-07-15 22:48:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([ 0.01483119,  0.08902284, -0.0016993 ,  0.02539853, -0.03643779,
        0.09873639,  0.04664258,  0.03992763])
2025-07-15 22:48:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:09 - shap - INFO - phi = array([-0.01483119, -0.08902284,  0.0016993 , -0.02539853,  0.03643779,
       -0.09873639, -0.04664258, -0.03992763])
2025-07-15 22:48:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([ 0.11066936,  0.12186571, -0.00258822, -0.01216751, -0.04370262,
        0.02979638,  0.04525778,  0.00269311])
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([-0.11066936, -0.12186571,  0.00258822,  0.01216751,  0.04370262,
       -0.02979638, -0.04525778, -0.00269311])
2025-07-15 22:48:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([-0.15322347,  0.03836398, -0.10160071, -0.0288565 , -0.0379448 ,
       -0.08770325,  0.03719292, -0.06040911])
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([ 0.15322347, -0.03836398,  0.10160071,  0.0288565 ,  0.0379448 ,
        0.08770325, -0.03719292,  0.06040911])
2025-07-15 22:48:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([ 0.07027727, -0.37253752, -0.00140139,  0.00245571,  0.12539398,
       -0.10906656, -0.03102603, -0.01622755])
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([-0.07027727,  0.37253752,  0.00140139, -0.00245571, -0.12539398,
        0.10906656,  0.03102603,  0.01622755])
2025-07-15 22:48:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([ 0.01840996,  0.04624201,  0.02906379,  0.01933732, -0.03457801,
        0.13642464,  0.02046606,  0.02928054])
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([-0.01840996, -0.04624201, -0.02906379, -0.01933732,  0.03457801,
       -0.13642464, -0.02046606, -0.02928054])
2025-07-15 22:48:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([ 0.03652099,  0.11345029,  0.04971191,  0.02840795, -0.01136771,
        0.04028881,  0.00243383,  0.017607  ])
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([-0.03652099, -0.11345029, -0.04971191, -0.02840795,  0.01136771,
       -0.04028881, -0.00243383, -0.017607  ])
2025-07-15 22:48:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([-0.07455706,  0.06469929,  0.12505364,  0.03907046, -0.0139593 ,
        0.06866292,  0.03669209,  0.0135162 ])
2025-07-15 22:48:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:10 - shap - INFO - phi = array([ 0.07455706, -0.06469929, -0.12505364, -0.03907046,  0.0139593 ,
       -0.06866292, -0.03669209, -0.0135162 ])
2025-07-15 22:48:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([ 0.00672237, -0.04259105, -0.1456877 , -0.08872056,  0.05405259,
       -0.07090603, -0.03896387,  0.05782759])
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([-0.00672237,  0.04259105,  0.1456877 ,  0.08872056, -0.05405259,
        0.07090603,  0.03896387, -0.05782759])
2025-07-15 22:48:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([-0.01572268, -0.263805  , -0.07087443, -0.04408531, -0.01589509,
        0.08740221, -0.03883981, -0.05604878])
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([ 0.01572268,  0.263805  ,  0.07087443,  0.04408531,  0.01589509,
       -0.08740221,  0.03883981,  0.05604878])
2025-07-15 22:48:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([-0.00737044, -0.30333715,  0.04696361, -0.00134275, -0.01106189,
       -0.01995483, -0.06708408, -0.06898021])
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([ 0.00737044,  0.30333715, -0.04696361,  0.00134275,  0.01106189,
        0.01995483,  0.06708408,  0.06898021])
2025-07-15 22:48:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([ 0.05312967,  0.12020035,  0.00996424,  0.02218905, -0.02648393,
        0.07521763,  0.02111917,  0.01564218])
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([-0.05312967, -0.12020035, -0.00996424, -0.02218905,  0.02648393,
       -0.07521763, -0.02111917, -0.01564218])
2025-07-15 22:48:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([ 0.00775784, -0.02077789, -0.15072422, -0.13606237, -0.04787054,
        0.07246104,  0.00946479, -0.00497439])
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([-0.00775784,  0.02077789,  0.15072422,  0.13606237,  0.04787054,
       -0.07246104, -0.00946479,  0.00497439])
2025-07-15 22:48:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([ 0.01092922,  0.111357  ,  0.01401945,  0.01063433, -0.02584231,
        0.09829299,  0.03272958,  0.0315983 ])
2025-07-15 22:48:11 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:11 - shap - INFO - phi = array([-0.01092922, -0.111357  , -0.01401945, -0.01063433,  0.02584231,
       -0.09829299, -0.03272958, -0.0315983 ])
2025-07-15 22:48:11 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([ 0.0311743 , -0.33454067,  0.00045422,  0.04131535,  0.08455768,
       -0.224162  , -0.03839451,  0.01594697])
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([-0.0311743 ,  0.33454067, -0.00045422, -0.04131535, -0.08455768,
        0.224162  ,  0.03839451, -0.01594697])
2025-07-15 22:48:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([ 0.03390391,  0.00060669, -0.13377871,  0.01739188, -0.07800427,
        0.08976183,  0.01957534, -0.02147671])
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([-0.03390391, -0.00060669,  0.13377871, -0.01739188,  0.07800427,
       -0.08976183, -0.01957534,  0.02147671])
2025-07-15 22:48:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([-0.01713012,  0.13811069,  0.08684193, -0.01726837, -0.04210699,
       -0.03197427,  0.03267484,  0.06565939])
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([ 0.01713012, -0.13811069, -0.08684193,  0.01726837,  0.04210699,
        0.03197427, -0.03267484, -0.06565939])
2025-07-15 22:48:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([-0.08845033,  0.07627804,  0.02522141,  0.0746795 , -0.03970265,
        0.09741518,  0.04025716,  0.05696684])
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([ 0.08845033, -0.07627804, -0.02522141, -0.0746795 ,  0.03970265,
       -0.09741518, -0.04025716, -0.05696684])
2025-07-15 22:48:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([ 0.00120649,  0.06270437,  0.01841799,  0.07193764, -0.02546826,
        0.09769554,  0.01554996,  0.02526165])
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([-0.00120649, -0.06270437, -0.01841799, -0.07193764,  0.02546826,
       -0.09769554, -0.01554996, -0.02526165])
2025-07-15 22:48:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([-0.06338884, -0.02672506,  0.07762961,  0.02518928,  0.11142059,
        0.06413227,  0.0341418 , -0.0236467 ])
2025-07-15 22:48:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:12 - shap - INFO - phi = array([ 0.06338884,  0.02672506, -0.07762961, -0.02518928, -0.11142059,
       -0.06413227, -0.0341418 ,  0.0236467 ])
2025-07-15 22:48:12 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([-0.01380933, -0.07866245, -0.13124505, -0.10836961, -0.0327526 ,
        0.12132895,  0.01181006, -0.10353424])
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([ 0.01380933,  0.07866245,  0.13124505,  0.10836961,  0.0327526 ,
       -0.12132895, -0.01181006,  0.10353424])
2025-07-15 22:48:13 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([-0.12142611,  0.01107056, -0.11479015,  0.1063353 ,  0.08241542,
        0.09204945,  0.01116426, -0.09559031])
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([ 0.12142611, -0.01107056,  0.11479015, -0.1063353 , -0.08241542,
       -0.09204945, -0.01116426,  0.09559031])
2025-07-15 22:48:13 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([ 0.02739314, -0.26073459, -0.00383124, -0.01757138,  0.0549831 ,
       -0.14996365,  0.00240606, -0.03534052])
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([-0.02739314,  0.26073459,  0.00383124,  0.01757138, -0.0549831 ,
        0.14996365, -0.00240606,  0.03534052])
2025-07-15 22:48:13 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([ 0.03069453,  0.10604206,  0.05103875,  0.02664639, -0.02254616,
        0.07730339,  0.01691456,  0.00930899])
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([-0.03069453, -0.10604206, -0.05103875, -0.02664639,  0.02254616,
       -0.07730339, -0.01691456, -0.00930899])
2025-07-15 22:48:13 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([-0.04252664,  0.03953848, -0.00485736, -0.02055505, -0.07676987,
       -0.28399166,  0.05101812,  0.04736671])
2025-07-15 22:48:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:13 - shap - INFO - phi = array([ 0.04252664, -0.03953848,  0.00485736,  0.02055505,  0.07676987,
        0.28399166, -0.05101812, -0.04736671])
2025-07-15 22:48:13 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([-0.10634534, -0.03072545, -0.01153483, -0.00867548, -0.08019367,
       -0.04811533, -0.00855656, -0.00152757])
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([0.10634534, 0.03072545, 0.01153483, 0.00867548, 0.08019367,
       0.04811533, 0.00855656, 0.00152757])
2025-07-15 22:48:14 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([ 0.08466765,  0.15141672,  0.        , -0.03642219, -0.01166698,
        0.04737741,  0.0308776 , -0.00649872])
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([-0.08466765, -0.15141672,  0.        ,  0.03642219,  0.01166698,
       -0.04737741, -0.0308776 ,  0.00649872])
2025-07-15 22:48:14 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([ 0.05180763, -0.2978755 , -0.00471572, -0.02965302,  0.05470707,
       -0.18737915, -0.01827513, -0.00247975])
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([-0.05180763,  0.2978755 ,  0.00471572,  0.02965302, -0.05470707,
        0.18737915,  0.01827513,  0.00247975])
2025-07-15 22:48:14 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([-0.1055152 ,  0.15786027, -0.05086493, -0.05105846, -0.0150036 ,
        0.08885153, -0.09428306, -0.10822067])
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([ 0.1055152 , -0.15786027,  0.05086493,  0.05105846,  0.0150036 ,
       -0.08885153,  0.09428306,  0.10822067])
2025-07-15 22:48:14 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([-0.0167265 , -0.11418431, -0.02030643,  0.11358903,  0.03897715,
        0.06699373,  0.00268887,  0.03250139])
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([ 0.0167265 ,  0.11418431,  0.02030643, -0.11358903, -0.03897715,
       -0.06699373, -0.00268887, -0.03250139])
2025-07-15 22:48:14 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([ 0.16314255, -0.12324038,  0.00054268, -0.06947993,  0.15630214,
       -0.21170825, -0.02401736,  0.03440242])
2025-07-15 22:48:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:14 - shap - INFO - phi = array([-0.16314255,  0.12324038, -0.00054268,  0.06947993, -0.15630214,
        0.21170825,  0.02401736, -0.03440242])
2025-07-15 22:48:14 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([ 0.01281648,  0.12111212,  0.06079664,  0.04947627, -0.02680891,
        0.0947619 ,  0.03466875, -0.05797467])
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([-0.01281648, -0.12111212, -0.06079664, -0.04947627,  0.02680891,
       -0.0947619 , -0.03466875,  0.05797467])
2025-07-15 22:48:15 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([-0.06312532,  0.14939174,  0.04998606,  0.0032667 ,  0.10735297,
        0.00758365,  0.00428993,  0.00092552])
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([ 0.06312532, -0.14939174, -0.04998606, -0.0032667 , -0.10735297,
       -0.00758365, -0.00428993, -0.00092552])
2025-07-15 22:48:15 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([ 0.02150331,  0.13306655,  0.03350284,  0.0206838 , -0.01487828,
        0.0607098 , -0.00543123,  0.02894644])
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([-0.02150331, -0.13306655, -0.03350284, -0.0206838 ,  0.01487828,
       -0.0607098 ,  0.00543123, -0.02894644])
2025-07-15 22:48:15 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([ 0.11018141,  0.00762689, -0.12044192, -0.08030925, -0.04370184,
       -0.09783949, -0.05272576,  0.05130862])
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([-0.11018141, -0.00762689,  0.12044192,  0.08030925,  0.04370184,
        0.09783949,  0.05272576, -0.05130862])
2025-07-15 22:48:15 - shap - INFO - num_full_subsets = 4
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([ 0.11122345,  0.12418115,  0.06760919,  0.05396359, -0.02291336,
       -0.23382625, -0.01316179, -0.05729125])
2025-07-15 22:48:15 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:48:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:48:15 - shap - INFO - phi = array([-0.11122345, -0.12418115, -0.06760919, -0.05396359,  0.02291336,
        0.23382625,  0.01316179,  0.05729125])
2025-07-15 22:48:19 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-15 22:48:19 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:48:19 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:48:19 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:48:19 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:48:29 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:48:30 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-15 22:48:30 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
