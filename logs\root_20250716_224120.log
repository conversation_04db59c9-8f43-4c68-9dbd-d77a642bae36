2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:41:20 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 22:41:20 - model_training - INFO - 准确率: 0.7733
2025-07-16 22:41:20 - model_training - INFO - AUC: 0.8017
2025-07-16 22:41:20 - model_training - INFO - 混淆矩阵:
2025-07-16 22:41:20 - model_training - INFO - 
[[120  31]
 [ 37 112]]
2025-07-16 22:41:20 - model_training - INFO - 
分类报告:
2025-07-16 22:41:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.79      0.78       151
           1       0.78      0.75      0.77       149

    accuracy                           0.77       300
   macro avg       0.77      0.77      0.77       300
weighted avg       0.77      0.77      0.77       300

2025-07-16 22:41:20 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 22:41:20 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7804
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:41:20 - model_training - INFO - 模型名称: Random Forest
2025-07-16 22:41:20 - model_training - INFO - 准确率: 0.8867
2025-07-16 22:41:20 - model_training - INFO - AUC: 0.9524
2025-07-16 22:41:20 - model_training - INFO - 混淆矩阵:
2025-07-16 22:41:20 - model_training - INFO - 
[[132  19]
 [ 15 134]]
2025-07-16 22:41:20 - model_training - INFO - 
分类报告:
2025-07-16 22:41:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.87      0.89       151
           1       0.88      0.90      0.89       149

    accuracy                           0.89       300
   macro avg       0.89      0.89      0.89       300
weighted avg       0.89      0.89      0.89       300

2025-07-16 22:41:20 - model_training - INFO - 训练时间: 0.22 秒
2025-07-16 22:41:20 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9031
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:41:20 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 22:41:20 - model_training - INFO - 准确率: 0.8100
2025-07-16 22:41:20 - model_training - INFO - AUC: 0.8935
2025-07-16 22:41:20 - model_training - INFO - 混淆矩阵:
2025-07-16 22:41:20 - model_training - INFO - 
[[122  29]
 [ 28 121]]
2025-07-16 22:41:20 - model_training - INFO - 
分类报告:
2025-07-16 22:41:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.81      0.81       151
           1       0.81      0.81      0.81       149

    accuracy                           0.81       300
   macro avg       0.81      0.81      0.81       300
weighted avg       0.81      0.81      0.81       300

2025-07-16 22:41:20 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 22:41:20 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8309
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:41:21 - model_training - INFO - 模型名称: SVM
2025-07-16 22:41:21 - model_training - INFO - 准确率: 0.9300
2025-07-16 22:41:21 - model_training - INFO - AUC: 0.9774
2025-07-16 22:41:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:41:21 - model_training - INFO - 
[[138  13]
 [  8 141]]
2025-07-16 22:41:21 - model_training - INFO - 
分类报告:
2025-07-16 22:41:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.91      0.93       151
           1       0.92      0.95      0.93       149

    accuracy                           0.93       300
   macro avg       0.93      0.93      0.93       300
weighted avg       0.93      0.93      0.93       300

2025-07-16 22:41:21 - model_training - INFO - 训练时间: 0.05 秒
2025-07-16 22:41:21 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9419
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 22:41:21 - model_training - INFO - 模型名称: XGBoost
2025-07-16 22:41:21 - model_training - INFO - 准确率: 0.9100
2025-07-16 22:41:21 - model_training - INFO - AUC: 0.9696
2025-07-16 22:41:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:41:21 - model_training - INFO - 
[[139  12]
 [ 15 134]]
2025-07-16 22:41:21 - model_training - INFO - 
分类报告:
2025-07-16 22:41:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.92      0.91       151
           1       0.92      0.90      0.91       149

    accuracy                           0.91       300
   macro avg       0.91      0.91      0.91       300
weighted avg       0.91      0.91      0.91       300

2025-07-16 22:41:21 - model_training - INFO - 训练时间: 0.15 秒
2025-07-16 22:41:21 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.9249
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 模型多样性矩阵计算完成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.588, 多样性=0.412
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.541, 多样性=0.459
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.569, 多样性=0.431
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.620, 多样性=0.380
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.713, 多样性=0.287
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.860, 多样性=0.140
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.848, 多样性=0.152
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.720, 多样性=0.280
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.707, 多样性=0.293
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.855, 多样性=0.145
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'SVM', 'XGBoost']
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.9233
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.4784
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.6559
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.6559
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 创建了 3 个数据变体用于增强模型多样性
