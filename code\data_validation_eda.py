#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据验证和探索性数据分析(EDA)模块

功能包括：
1. 数据完整性和有效性验证
2. 缺失值检测和处理
3. 异常值检测和处理
4. 数据分布分析
5. 特征相关性分析
6. 数据质量报告生成
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import normaltest, jarque_bera
import warnings
from pathlib import Path
import logging
from typing import Dict, List, Tuple, Optional, Any
import json
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataValidator:
    """
    数据验证器类
    用于检查数据的完整性、有效性和质量
    """
    
    def __init__(self, output_dir: str = "output/data_validation"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.validation_results = {}
        
    def validate_data_integrity(self, df: pd.DataFrame, target_col: str = None) -> Dict[str, Any]:
        """
        验证数据完整性
        
        Args:
            df: 数据框
            target_col: 目标列名
            
        Returns:
            dict: 验证结果
        """
        logger.info("开始数据完整性验证...")
        
        results = {
            'basic_info': {
                'shape': df.shape,
                'columns': list(df.columns),
                'dtypes': df.dtypes.to_dict(),
                'memory_usage': df.memory_usage(deep=True).sum() / 1024**2  # MB
            },
            'missing_values': self._check_missing_values(df),
            'duplicates': self._check_duplicates(df),
            'data_types': self._check_data_types(df),
            'target_validation': self._validate_target(df, target_col) if target_col else None
        }
        
        self.validation_results['integrity'] = results
        logger.info("数据完整性验证完成")
        return results
    
    def _check_missing_values(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检查缺失值"""
        missing_count = df.isnull().sum()
        missing_percent = (missing_count / len(df)) * 100
        
        missing_info = pd.DataFrame({
            '缺失数量': missing_count,
            '缺失百分比': missing_percent
        })
        missing_info = missing_info[missing_info['缺失数量'] > 0].sort_values('缺失百分比', ascending=False)
        
        return {
            'total_missing': missing_count.sum(),
            'columns_with_missing': len(missing_info),
            'missing_details': missing_info.to_dict('index'),
            'missing_pattern': df.isnull().sum().to_dict()
        }
    
    def _check_duplicates(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检查重复值"""
        duplicate_rows = df.duplicated().sum()
        duplicate_percent = (duplicate_rows / len(df)) * 100
        
        return {
            'duplicate_rows': int(duplicate_rows),
            'duplicate_percent': float(duplicate_percent),
            'unique_rows': len(df) - duplicate_rows
        }
    
    def _check_data_types(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检查数据类型"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
        datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
        
        return {
            'numeric_columns': numeric_cols,
            'categorical_columns': categorical_cols,
            'datetime_columns': datetime_cols,
            'column_types': df.dtypes.to_dict()
        }
    
    def _validate_target(self, df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """验证目标变量"""
        if target_col not in df.columns:
            return {'error': f'目标列 {target_col} 不存在'}
        
        target_series = df[target_col]
        unique_values = target_series.unique()
        value_counts = target_series.value_counts()
        
        return {
            'column_name': target_col,
            'unique_values': len(unique_values),
            'value_distribution': value_counts.to_dict(),
            'missing_in_target': target_series.isnull().sum(),
            'is_balanced': self._check_class_balance(value_counts)
        }
    
    def _check_class_balance(self, value_counts: pd.Series) -> Dict[str, Any]:
        """检查类别平衡性"""
        total = value_counts.sum()
        proportions = value_counts / total
        
        # 计算不平衡比率
        max_prop = proportions.max()
        min_prop = proportions.min()
        imbalance_ratio = max_prop / min_prop if min_prop > 0 else float('inf')
        
        return {
            'proportions': proportions.to_dict(),
            'imbalance_ratio': float(imbalance_ratio),
            'is_balanced': imbalance_ratio <= 3.0  # 认为比率小于3为相对平衡
        }

class OutlierDetector:
    """
    异常值检测器
    """
    
    def __init__(self):
        self.outlier_results = {}
    
    def detect_outliers(self, df: pd.DataFrame, methods: List[str] = None) -> Dict[str, Any]:
        """
        检测异常值
        
        Args:
            df: 数据框
            methods: 检测方法列表 ['iqr', 'zscore', 'isolation_forest']
            
        Returns:
            dict: 异常值检测结果
        """
        if methods is None:
            methods = ['iqr', 'zscore']
        
        logger.info("开始异常值检测...")
        
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        results = {}
        
        for col in numeric_cols:
            col_results = {}
            
            if 'iqr' in methods:
                col_results['iqr'] = self._detect_iqr_outliers(df[col])
            
            if 'zscore' in methods:
                col_results['zscore'] = self._detect_zscore_outliers(df[col])
            
            if 'isolation_forest' in methods:
                try:
                    from sklearn.ensemble import IsolationForest
                    col_results['isolation_forest'] = self._detect_isolation_forest_outliers(df[col])
                except ImportError:
                    logger.warning("sklearn未安装，跳过Isolation Forest方法")
            
            results[col] = col_results
        
        self.outlier_results = results
        logger.info("异常值检测完成")
        return results
    
    def _detect_iqr_outliers(self, series: pd.Series) -> Dict[str, Any]:
        """IQR方法检测异常值"""
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = series[(series < lower_bound) | (series > upper_bound)]
        
        return {
            'method': 'IQR',
            'lower_bound': float(lower_bound),
            'upper_bound': float(upper_bound),
            'outlier_count': len(outliers),
            'outlier_percent': (len(outliers) / len(series)) * 100,
            'outlier_indices': outliers.index.tolist()
        }
    
    def _detect_zscore_outliers(self, series: pd.Series, threshold: float = 3.0) -> Dict[str, Any]:
        """Z-score方法检测异常值"""
        z_scores = np.abs(stats.zscore(series.dropna()))
        outlier_mask = z_scores > threshold
        outliers = series.dropna()[outlier_mask]
        
        return {
            'method': 'Z-Score',
            'threshold': threshold,
            'outlier_count': len(outliers),
            'outlier_percent': (len(outliers) / len(series)) * 100,
            'outlier_indices': outliers.index.tolist()
        }
    
    def _detect_isolation_forest_outliers(self, series: pd.Series, contamination: float = 0.1) -> Dict[str, Any]:
        """Isolation Forest方法检测异常值"""
        from sklearn.ensemble import IsolationForest
        
        # 重塑数据
        data = series.dropna().values.reshape(-1, 1)
        
        # 训练模型
        iso_forest = IsolationForest(contamination=contamination, random_state=42)
        outlier_labels = iso_forest.fit_predict(data)
        
        # 获取异常值
        outlier_indices = series.dropna().index[outlier_labels == -1]
        
        return {
            'method': 'Isolation Forest',
            'contamination': contamination,
            'outlier_count': len(outlier_indices),
            'outlier_percent': (len(outlier_indices) / len(series)) * 100,
            'outlier_indices': outlier_indices.tolist()
        }

class EDAAnalyzer:
    """
    探索性数据分析器
    """
    
    def __init__(self, output_dir: str = "output/eda"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.eda_results = {}
    
    def perform_eda(self, df: pd.DataFrame, target_col: str = None) -> Dict[str, Any]:
        """
        执行完整的EDA分析
        
        Args:
            df: 数据框
            target_col: 目标列名
            
        Returns:
            dict: EDA分析结果
        """
        logger.info("开始探索性数据分析(EDA)...")
        
        results = {
            'descriptive_stats': self._descriptive_statistics(df),
            'distribution_analysis': self._analyze_distributions(df),
            'correlation_analysis': self._correlation_analysis(df),
            'feature_importance': self._feature_importance_analysis(df, target_col) if target_col else None
        }
        
        # 生成可视化图表
        self._generate_eda_plots(df, target_col)
        
        self.eda_results = results
        logger.info("EDA分析完成")
        return results
    
    def _descriptive_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """描述性统计分析"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        
        results = {
            'numeric_stats': df[numeric_cols].describe().to_dict() if len(numeric_cols) > 0 else {},
            'categorical_stats': {}
        }
        
        # 分类变量统计
        for col in categorical_cols:
            results['categorical_stats'][col] = {
                'unique_count': df[col].nunique(),
                'most_frequent': df[col].mode().iloc[0] if len(df[col].mode()) > 0 else None,
                'value_counts': df[col].value_counts().head(10).to_dict()
            }
        
        return results
    
    def _analyze_distributions(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分布分析"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        results = {}
        
        for col in numeric_cols:
            series = df[col].dropna()
            
            # 正态性检验
            try:
                normality_stat, normality_p = normaltest(series)
                is_normal = normality_p > 0.05
            except:
                normality_stat, normality_p, is_normal = None, None, False
            
            # 偏度和峰度
            skewness = float(series.skew())
            kurtosis = float(series.kurtosis())
            
            results[col] = {
                'normality_test': {
                    'statistic': float(normality_stat) if normality_stat else None,
                    'p_value': float(normality_p) if normality_p else None,
                    'is_normal': is_normal
                },
                'skewness': skewness,
                'kurtosis': kurtosis,
                'distribution_type': self._classify_distribution(skewness, kurtosis, is_normal)
            }
        
        return results
    
    def _classify_distribution(self, skewness: float, kurtosis: float, is_normal: bool) -> str:
        """分类分布类型"""
        if is_normal:
            return "正态分布"
        elif abs(skewness) < 0.5:
            return "近似对称分布"
        elif skewness > 0.5:
            return "右偏分布"
        elif skewness < -0.5:
            return "左偏分布"
        else:
            return "未知分布"
    
    def _correlation_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """相关性分析"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) < 2:
            return {'error': '数值列少于2个，无法进行相关性分析'}
        
        # 计算相关性矩阵
        corr_matrix = df[numeric_cols].corr()
        
        # 找出高相关性特征对
        high_corr_pairs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_val = corr_matrix.iloc[i, j]
                if abs(corr_val) > 0.7:  # 高相关性阈值
                    high_corr_pairs.append({
                        'feature1': corr_matrix.columns[i],
                        'feature2': corr_matrix.columns[j],
                        'correlation': float(corr_val)
                    })
        
        return {
            'correlation_matrix': corr_matrix.to_dict(),
            'high_correlation_pairs': high_corr_pairs,
            'max_correlation': float(corr_matrix.abs().max().max()),
            'mean_correlation': float(corr_matrix.abs().mean().mean())
        }
    
    def _feature_importance_analysis(self, df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """特征重要性分析"""
        if target_col not in df.columns:
            return {'error': f'目标列 {target_col} 不存在'}
        
        try:
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.feature_selection import mutual_info_classif
            
            # 准备数据
            X = df.select_dtypes(include=[np.number]).drop(columns=[target_col], errors='ignore')
            y = df[target_col]
            
            # 移除缺失值
            mask = ~(X.isnull().any(axis=1) | y.isnull())
            X_clean = X[mask]
            y_clean = y[mask]
            
            if len(X_clean) == 0:
                return {'error': '清理后无有效数据'}
            
            # 随机森林特征重要性
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf.fit(X_clean, y_clean)
            rf_importance = dict(zip(X_clean.columns, rf.feature_importances_))
            
            # 互信息
            mi_scores = mutual_info_classif(X_clean, y_clean, random_state=42)
            mi_importance = dict(zip(X_clean.columns, mi_scores))
            
            return {
                'random_forest_importance': rf_importance,
                'mutual_information': mi_importance,
                'top_features_rf': sorted(rf_importance.items(), key=lambda x: x[1], reverse=True)[:10],
                'top_features_mi': sorted(mi_importance.items(), key=lambda x: x[1], reverse=True)[:10]
            }
            
        except ImportError:
            logger.warning("sklearn未安装，跳过特征重要性分析")
            return {'error': 'sklearn未安装'}
        except Exception as e:
            logger.error(f"特征重要性分析出错: {e}")
            return {'error': str(e)}
    
    def _generate_eda_plots(self, df: pd.DataFrame, target_col: str = None):
        """生成EDA可视化图表"""
        logger.info("生成EDA可视化图表...")
        
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        
        # 1. 数据概览图
        self._plot_data_overview(df)
        
        # 2. 数值特征分布图
        if len(numeric_cols) > 0:
            self._plot_numeric_distributions(df[numeric_cols])
        
        # 3. 相关性热力图
        if len(numeric_cols) > 1:
            self._plot_correlation_heatmap(df[numeric_cols])
        
        # 4. 分类特征分布图
        if len(categorical_cols) > 0:
            self._plot_categorical_distributions(df[categorical_cols])
        
        # 5. 目标变量分析图
        if target_col and target_col in df.columns:
            self._plot_target_analysis(df, target_col)
    
    def _plot_data_overview(self, df: pd.DataFrame):
        """绘制数据概览图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('数据概览', fontsize=16, fontweight='bold')
        
        # 缺失值热力图
        sns.heatmap(df.isnull(), cbar=True, ax=axes[0, 0], cmap='viridis')
        axes[0, 0].set_title('缺失值分布')
        
        # 数据类型分布
        dtype_counts = df.dtypes.value_counts()
        axes[0, 1].pie(dtype_counts.values, labels=dtype_counts.index, autopct='%1.1f%%')
        axes[0, 1].set_title('数据类型分布')
        
        # 缺失值统计
        missing_counts = df.isnull().sum()
        missing_counts = missing_counts[missing_counts > 0]
        if len(missing_counts) > 0:
            missing_counts.plot(kind='bar', ax=axes[1, 0])
            axes[1, 0].set_title('各列缺失值数量')
            axes[1, 0].tick_params(axis='x', rotation=45)
        else:
            axes[1, 0].text(0.5, 0.5, '无缺失值', ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('缺失值统计')
        
        # 数据集基本信息
        info_text = f"""数据集形状: {df.shape}
        数值列数: {len(df.select_dtypes(include=[np.number]).columns)}
        分类列数: {len(df.select_dtypes(include=['object', 'category']).columns)}
        总缺失值: {df.isnull().sum().sum()}
        重复行数: {df.duplicated().sum()}
        内存使用: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB"""
        
        axes[1, 1].text(0.1, 0.5, info_text, transform=axes[1, 1].transAxes, 
                        fontsize=10, verticalalignment='center')
        axes[1, 1].set_title('数据集基本信息')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'data_overview.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_numeric_distributions(self, df_numeric: pd.DataFrame):
        """绘制数值特征分布图"""
        n_cols = min(4, len(df_numeric.columns))
        n_rows = (len(df_numeric.columns) + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))
        fig.suptitle('数值特征分布', fontsize=16, fontweight='bold')
        
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, col in enumerate(df_numeric.columns):
            row, col_idx = divmod(i, n_cols)
            ax = axes[row, col_idx]
            
            # 绘制直方图和密度曲线
            df_numeric[col].hist(bins=30, alpha=0.7, ax=ax, density=True)
            df_numeric[col].plot(kind='density', ax=ax, color='red')
            ax.set_title(f'{col} 分布')
            ax.set_xlabel(col)
            ax.set_ylabel('密度')
        
        # 隐藏多余的子图
        for i in range(len(df_numeric.columns), n_rows * n_cols):
            row, col_idx = divmod(i, n_cols)
            axes[row, col_idx].axis('off')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'numeric_distributions.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_correlation_heatmap(self, df_numeric: pd.DataFrame):
        """绘制相关性热力图"""
        plt.figure(figsize=(12, 10))
        
        # 计算相关性矩阵
        corr_matrix = df_numeric.corr()
        
        # 创建掩码以隐藏上三角
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        
        # 绘制热力图
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        
        plt.title('特征相关性热力图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_categorical_distributions(self, df_categorical: pd.DataFrame):
        """绘制分类特征分布图"""
        n_cols = min(3, len(df_categorical.columns))
        n_rows = (len(df_categorical.columns) + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(6*n_cols, 4*n_rows))
        fig.suptitle('分类特征分布', fontsize=16, fontweight='bold')
        
        if n_rows == 1 and n_cols == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, col in enumerate(df_categorical.columns):
            if n_rows == 1 and n_cols == 1:
                ax = axes[0]
            else:
                row, col_idx = divmod(i, n_cols)
                ax = axes[row, col_idx] if n_rows > 1 else axes[col_idx]
            
            # 获取前10个最频繁的值
            value_counts = df_categorical[col].value_counts().head(10)
            value_counts.plot(kind='bar', ax=ax)
            ax.set_title(f'{col} 分布')
            ax.set_xlabel(col)
            ax.set_ylabel('频次')
            ax.tick_params(axis='x', rotation=45)
        
        # 隐藏多余的子图
        if n_rows > 1 or n_cols > 1:
            for i in range(len(df_categorical.columns), n_rows * n_cols):
                row, col_idx = divmod(i, n_cols)
                if n_rows > 1:
                    axes[row, col_idx].axis('off')
                else:
                    axes[col_idx].axis('off')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'categorical_distributions.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_target_analysis(self, df: pd.DataFrame, target_col: str):
        """绘制目标变量分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'目标变量 {target_col} 分析', fontsize=16, fontweight='bold')
        
        # 目标变量分布
        df[target_col].value_counts().plot(kind='bar', ax=axes[0, 0])
        axes[0, 0].set_title('目标变量分布')
        axes[0, 0].set_xlabel(target_col)
        axes[0, 0].set_ylabel('频次')
        
        # 目标变量饼图
        df[target_col].value_counts().plot(kind='pie', ax=axes[0, 1], autopct='%1.1f%%')
        axes[0, 1].set_title('目标变量比例')
        
        # 数值特征与目标变量的关系
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        numeric_cols = [col for col in numeric_cols if col != target_col]
        
        if len(numeric_cols) > 0:
            # 选择第一个数值特征进行分析
            first_numeric = numeric_cols[0]
            for target_value in df[target_col].unique():
                subset = df[df[target_col] == target_value][first_numeric]
                axes[1, 0].hist(subset, alpha=0.7, label=f'{target_col}={target_value}', bins=20)
            axes[1, 0].set_title(f'{first_numeric} 按 {target_col} 分组分布')
            axes[1, 0].set_xlabel(first_numeric)
            axes[1, 0].set_ylabel('频次')
            axes[1, 0].legend()
        else:
            axes[1, 0].text(0.5, 0.5, '无数值特征可分析', ha='center', va='center', transform=axes[1, 0].transAxes)
        
        # 目标变量统计信息
        target_stats = df[target_col].value_counts()
        stats_text = f"""类别数量: {len(target_stats)}
        最多类别: {target_stats.index[0]} ({target_stats.iloc[0]})
        最少类别: {target_stats.index[-1]} ({target_stats.iloc[-1]})
        不平衡比率: {target_stats.iloc[0] / target_stats.iloc[-1]:.2f}
        缺失值: {df[target_col].isnull().sum()}"""
        
        axes[1, 1].text(0.1, 0.5, stats_text, transform=axes[1, 1].transAxes, 
                        fontsize=12, verticalalignment='center')
        axes[1, 1].set_title('目标变量统计信息')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'target_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

class DataQualityReporter:
    """
    数据质量报告生成器
    """
    
    def __init__(self, output_dir: str = "output/data_quality"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_report(self, validation_results: Dict, outlier_results: Dict, 
                       eda_results: Dict, df: pd.DataFrame) -> str:
        """
        生成综合数据质量报告
        
        Args:
            validation_results: 数据验证结果
            outlier_results: 异常值检测结果
            eda_results: EDA分析结果
            df: 原始数据框
            
        Returns:
            str: 报告文件路径
        """
        logger.info("生成数据质量报告...")
        
        report = {
            'report_info': {
                'generated_at': datetime.now().isoformat(),
                'dataset_shape': df.shape,
                'dataset_size_mb': df.memory_usage(deep=True).sum() / 1024**2
            },
            'data_validation': validation_results,
            'outlier_detection': outlier_results,
            'eda_analysis': eda_results,
            'recommendations': self._generate_recommendations(validation_results, outlier_results, eda_results)
        }
        
        # 保存JSON报告
        json_path = self.output_dir / 'data_quality_report.json'
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        # 生成HTML报告
        html_path = self._generate_html_report(report)
        
        logger.info(f"数据质量报告已生成: {html_path}")
        return str(html_path)
    
    def _generate_recommendations(self, validation_results: Dict, outlier_results: Dict, 
                                eda_results: Dict) -> List[str]:
        """生成数据质量改进建议"""
        recommendations = []
        
        # 基于验证结果的建议
        if validation_results.get('integrity', {}).get('missing_values', {}).get('total_missing', 0) > 0:
            recommendations.append("检测到缺失值，建议进行缺失值处理（删除、填充或插值）")
        
        if validation_results.get('integrity', {}).get('duplicates', {}).get('duplicate_rows', 0) > 0:
            recommendations.append("检测到重复行，建议删除重复数据")
        
        # 基于异常值检测的建议
        outlier_count = 0
        for col_results in outlier_results.values():
            for method_results in col_results.values():
                outlier_count += method_results.get('outlier_count', 0)
        
        if outlier_count > 0:
            recommendations.append(f"检测到 {outlier_count} 个异常值，建议进行异常值处理")
        
        # 基于EDA结果的建议
        if eda_results.get('correlation_analysis', {}).get('high_correlation_pairs'):
            recommendations.append("检测到高相关性特征，建议考虑特征选择或降维")
        
        # 基于分布分析的建议
        distribution_analysis = eda_results.get('distribution_analysis', {})
        for col, analysis in distribution_analysis.items():
            if not analysis.get('normality_test', {}).get('is_normal', True):
                recommendations.append(f"特征 {col} 不符合正态分布，建议考虑数据转换")
        
        if not recommendations:
            recommendations.append("数据质量良好，无明显问题")
        
        return recommendations
    
    def _generate_html_report(self, report: Dict) -> Path:
        """生成HTML格式的报告"""
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>数据质量报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #e9e9e9; border-radius: 3px; }}
                .recommendation {{ background-color: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>数据质量分析报告</h1>
                <p>生成时间: {report['report_info']['generated_at']}</p>
                <p>数据集大小: {report['report_info']['dataset_shape']}</p>
                <p>内存使用: {report['report_info']['dataset_size_mb']:.2f} MB</p>
            </div>
            
            <div class="section">
                <h2>数据完整性验证</h2>
                <div class="metric">总缺失值: {report['data_validation'].get('integrity', {}).get('missing_values', {}).get('total_missing', 0)}</div>
                <div class="metric">重复行数: {report['data_validation'].get('integrity', {}).get('duplicates', {}).get('duplicate_rows', 0)}</div>
                <div class="metric">数值列数: {len(report['data_validation'].get('integrity', {}).get('data_types', {}).get('numeric_columns', []))}</div>
                <div class="metric">分类列数: {len(report['data_validation'].get('integrity', {}).get('data_types', {}).get('categorical_columns', []))}</div>
            </div>
            
            <div class="section">
                <h2>异常值检测结果</h2>
                <p>检测到的异常值数量（按列统计）:</p>
                <ul>
        """
        
        # 添加异常值统计
        for col, methods in report['outlier_detection'].items():
            for method, results in methods.items():
                html_content += f"<li>{col} ({results['method']}): {results['outlier_count']} 个异常值</li>"
        
        html_content += """
                </ul>
            </div>
            
            <div class="section">
                <h2>改进建议</h2>
        """
        
        for rec in report['recommendations']:
            html_content += f'<div class="recommendation">{rec}</div>'
        
        html_content += """
            </div>
        </body>
        </html>
        """
        
        html_path = self.output_dir / 'data_quality_report.html'
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_path

def run_complete_data_analysis(file_path: str, target_col: str = None, 
                              output_dir: str = "output") -> Dict[str, Any]:
    """
    运行完整的数据验证和EDA分析
    
    Args:
        file_path: 数据文件路径
        target_col: 目标列名
        output_dir: 输出目录
        
    Returns:
        dict: 完整的分析结果
    """
    logger.info(f"开始完整数据分析: {file_path}")
    
    # 加载数据
    try:
        df = pd.read_csv(file_path)
        logger.info(f"成功加载数据，形状: {df.shape}")
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return {'error': f'加载数据失败: {e}'}
    
    # 自动检测目标列
    if target_col is None:
        if 'label' in df.columns:
            target_col = 'label'
        elif 'target' in df.columns:
            target_col = 'target'
        else:
            # 使用最后一列作为目标列
            target_col = df.columns[-1]
        logger.info(f"自动检测目标列: {target_col}")
    
    # 初始化分析器
    validator = DataValidator(f"{output_dir}/data_validation")
    outlier_detector = OutlierDetector()
    eda_analyzer = EDAAnalyzer(f"{output_dir}/eda")
    reporter = DataQualityReporter(f"{output_dir}/data_quality")
    
    # 执行分析
    validation_results = validator.validate_data_integrity(df, target_col)
    outlier_results = outlier_detector.detect_outliers(df)
    eda_results = eda_analyzer.perform_eda(df, target_col)
    
    # 生成报告
    report_path = reporter.generate_report(validation_results, outlier_results, eda_results, df)
    
    # 汇总结果
    complete_results = {
        'data_validation': validation_results,
        'outlier_detection': outlier_results,
        'eda_analysis': eda_results,
        'report_path': report_path,
        'summary': {
            'total_samples': len(df),
            'total_features': len(df.columns),
            'missing_values': df.isnull().sum().sum(),
            'duplicate_rows': df.duplicated().sum(),
            'data_quality_score': _calculate_data_quality_score(validation_results, outlier_results)
        }
    }
    
    logger.info("完整数据分析完成")
    return complete_results

def _calculate_data_quality_score(validation_results: Dict, outlier_results: Dict) -> float:
    """
    计算数据质量评分 (0-100)
    
    Args:
        validation_results: 验证结果
        outlier_results: 异常值检测结果
        
    Returns:
        float: 质量评分
    """
    score = 100.0
    
    # 缺失值扣分
    missing_percent = validation_results.get('integrity', {}).get('missing_values', {}).get('total_missing', 0)
    if missing_percent > 0:
        score -= min(missing_percent * 2, 30)  # 最多扣30分
    
    # 重复值扣分
    duplicate_percent = validation_results.get('integrity', {}).get('duplicates', {}).get('duplicate_percent', 0)
    score -= min(duplicate_percent, 20)  # 最多扣20分
    
    # 异常值扣分
    total_outliers = 0
    total_samples = 0
    for col_results in outlier_results.values():
        for method_results in col_results.values():
            total_outliers += method_results.get('outlier_count', 0)
            # 假设每列样本数相同，这里简化处理
            total_samples = max(total_samples, method_results.get('outlier_count', 0) + 100)
    
    if total_samples > 0:
        outlier_percent = (total_outliers / total_samples) * 100
        score -= min(outlier_percent, 20)  # 最多扣20分
    
    return max(score, 0.0)

if __name__ == "__main__":
    # 示例用法
    import sys
    
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        target_col = sys.argv[2] if len(sys.argv) > 2 else None
        
        results = run_complete_data_analysis(file_path, target_col)
        print(f"分析完成，报告路径: {results.get('report_path')}")
        print(f"数据质量评分: {results['summary']['data_quality_score']:.1f}/100")
    else:
        print("用法: python data_validation_eda.py <数据文件路径> [目标列名]")