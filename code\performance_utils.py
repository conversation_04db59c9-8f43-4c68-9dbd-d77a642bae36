#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化工具模块
提供并行处理、内存优化和大数据集处理功能
"""

import numpy as np
import pandas as pd
import os
import psutil
from joblib import Parallel, delayed, parallel_backend
from sklearn.utils import resample
from sklearn.model_selection import StratifiedKFold
import time
from functools import wraps
from contextlib import contextmanager
import gc

# 尝试导入日志模块
try:
    from logger import get_default_logger
    logger = get_default_logger("performance_utils")
except ImportError:
    import logging
    logger = logging.getLogger("performance_utils")
    logger.setLevel(logging.INFO)
    if not logger.handlers:
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logger.addHandler(handler)

def get_optimal_num_jobs():
    """
    根据系统CPU核心数和可用内存动态确定最佳并行作业数
    
    Returns:
        int: 最佳并行作业数
    """
    # 获取CPU核心数
    cpu_count = os.cpu_count()
    if cpu_count is None:
        cpu_count = 4  # 默认值
    
    # 获取系统内存情况
    mem = psutil.virtual_memory()
    available_memory_gb = mem.available / (1024 ** 3)
    
    # 基于可用内存调整并行作业数
    # 假设每个作业需要至少1GB内存
    memory_based_jobs = max(1, int(available_memory_gb / 1.5))
    
    # 取较小值以避免过度并行
    optimal_jobs = min(cpu_count, memory_based_jobs)
    
    logger.info(f"系统有{cpu_count}个CPU核心，{available_memory_gb:.2f}GB可用内存")
    logger.info(f"确定最佳并行作业数为: {optimal_jobs}")
    
    return optimal_jobs

def memory_usage_monitor(func):
    """
    装饰器，用于监控函数执行过程中的内存使用情况
    
    Args:
        func: 要监控的函数
        
    Returns:
        function: 包装后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 记录开始时内存使用情况
        process = psutil.Process(os.getpid())
        start_memory = process.memory_info().rss / (1024 ** 2)  # MB
        
        logger.info(f"函数 {func.__name__} 开始执行，当前内存使用: {start_memory:.2f} MB")
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 记录结束时内存使用情况和执行时间
        end_memory = process.memory_info().rss / (1024 ** 2)  # MB
        end_time = time.time()
        
        logger.info(f"函数 {func.__name__} 执行完成，当前内存使用: {end_memory:.2f} MB")
        logger.info(f"内存变化: {end_memory - start_memory:.2f} MB")
        logger.info(f"执行时间: {end_time - start_time:.2f} 秒")
        
        return result
    
    return wrapper

@contextmanager
def reduced_memory_context():
    """
    上下文管理器，用于临时减少内存占用
    在处理大数据集时非常有用
    """
    # 获取初始内存使用情况
    process = psutil.Process(os.getpid())
    start_memory = process.memory_info().rss / (1024 ** 2)  # MB
    
    logger.info(f"进入内存优化上下文，当前内存使用: {start_memory:.2f} MB")
    
    try:
        # 降低内存使用
        gc.collect()
        yield
    finally:
        # 再次收集垃圾
        gc.collect()
        
        # 记录结束时内存使用情况
        end_memory = process.memory_info().rss / (1024 ** 2)  # MB
        logger.info(f"退出内存优化上下文，当前内存使用: {end_memory:.2f} MB")
        logger.info(f"内存变化: {end_memory - start_memory:.2f} MB")

def reduce_dataframe_memory(df):
    """
    减少DataFrame的内存使用
    通过将列转换为最佳数据类型来优化内存使用
    
    Args:
        df: pandas DataFrame
        
    Returns:
        pandas.DataFrame: 优化后的DataFrame
    """
    start_memory = df.memory_usage().sum() / (1024 ** 2)  # MB
    logger.info(f"DataFrame初始内存使用: {start_memory:.2f} MB")
    
    for col in df.columns:
        col_type = df[col].dtype
        
        if col_type != object:  # 数值类型
            c_min = df[col].min()
            c_max = df[col].max()
            
            # 整数类型优化
            if str(col_type)[:3] == 'int':
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    df[col] = df[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    df[col] = df[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    df[col] = df[col].astype(np.int32)
                elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:
                    df[col] = df[col].astype(np.int64)
            
            # 浮点类型优化
            else:
                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                    df[col] = df[col].astype(np.float16)
                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    df[col] = df[col].astype(np.float32)
                else:
                    df[col] = df[col].astype(np.float64)
        
        # 对象类型(字符串)转换为类别类型
        else:
            # 如果唯一值少于数据量的50%，考虑使用类别类型
            if df[col].nunique() / len(df) < 0.5:
                df[col] = df[col].astype('category')
    
    end_memory = df.memory_usage().sum() / (1024 ** 2)  # MB
    logger.info(f"DataFrame优化后内存使用: {end_memory:.2f} MB")
    logger.info(f"内存减少: {start_memory - end_memory:.2f} MB ({(1 - end_memory/start_memory) * 100:.1f}%)")
    
    return df

def batch_process_dataframe(df, batch_size, processing_func, *args, **kwargs):
    """
    批处理大型DataFrame
    
    Args:
        df: 要处理的DataFrame
        batch_size: 批处理大小
        processing_func: 处理函数，接受DataFrame批次作为第一个参数
        args: 传递给处理函数的位置参数
        kwargs: 传递给处理函数的关键字参数
        
    Returns:
        list: 每个批次处理的结果列表
    """
    num_batches = int(np.ceil(len(df) / batch_size))
    logger.info(f"将数据分为{num_batches}个批次进行处理，每批次{batch_size}条记录")
    
    results = []
    
    for i in range(num_batches):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, len(df))
        
        logger.info(f"处理批次 {i+1}/{num_batches}，记录范围: {start_idx}-{end_idx}")
        
        batch = df.iloc[start_idx:end_idx].copy()
        
        with reduced_memory_context():
            result = processing_func(batch, *args, **kwargs)
            results.append(result)
            
        # 清理这个批次的数据
        del batch
        gc.collect()
    
    return results

def parallel_cross_validation(estimator, X, y, cv=5, scoring='roc_auc', n_jobs=None):
    """
    并行执行交叉验证
    
    Args:
        estimator: 要评估的模型
        X: 特征数据
        y: 目标变量
        cv: 交叉验证折数
        scoring: 评分标准
        n_jobs: 并行作业数，如果为None则自动确定
        
    Returns:
        tuple: (平均得分, 得分列表)
    """
    if n_jobs is None:
        n_jobs = get_optimal_num_jobs()
    
    cv_splitter = StratifiedKFold(n_splits=cv, shuffle=True, random_state=42)
    
    def _fit_and_score(train_idx, test_idx):
        X_train, X_test = X[train_idx], X[test_idx]
        y_train, y_test = y[train_idx], y[test_idx]
        
        estimator.fit(X_train, y_train)
        
        # 根据评分标准计算得分
        if scoring == 'roc_auc':
            from sklearn.metrics import roc_auc_score
            if hasattr(estimator, 'predict_proba'):
                y_pred = estimator.predict_proba(X_test)[:, 1]
            else:
                y_pred = estimator.predict(X_test)
            score = roc_auc_score(y_test, y_pred)
        else:
            from sklearn.metrics import get_scorer
            scorer = get_scorer(scoring)
            score = scorer(estimator, X_test, y_test)
        
        return score
    
    logger.info(f"使用{n_jobs}个作业并行执行{cv}折交叉验证")
    
    with parallel_backend('loky', n_jobs=n_jobs):
        scores = Parallel()(
            delayed(_fit_and_score)(train_idx, test_idx)
            for train_idx, test_idx in cv_splitter.split(X, y)
        )
    
    mean_score = np.mean(scores)
    logger.info(f"交叉验证完成，平均得分: {mean_score:.4f}")
    
    return mean_score, scores

def parallel_model_training(model_configs, X, y, cv=5, scoring='roc_auc', n_jobs=None):
    """
    并行训练多个模型
    
    Args:
        model_configs: 模型配置列表，每个元素为(模型名称, 模型对象)的元组
        X: 特征数据
        y: 目标变量
        cv: 交叉验证折数
        scoring: 评分标准
        n_jobs: 并行作业数，如果为None则自动确定
        
    Returns:
        dict: 模型名称到评估结果的映射
    """
    if n_jobs is None:
        n_jobs = get_optimal_num_jobs()
    
    def _train_and_evaluate(model_name, model):
        try:
            start_time = time.time()
            
            # 执行交叉验证
            mean_score, scores = parallel_cross_validation(
                model, X, y, cv=cv, scoring=scoring, n_jobs=1
            )
            
            end_time = time.time()
            training_time = end_time - start_time
            
            return {
                'model_name': model_name,
                'model': model,
                'mean_score': mean_score,
                'scores': scores,
                'training_time': training_time,
                'status': 'success'
            }
        except Exception as e:
            logger.error(f"模型 {model_name} 训练失败: {str(e)}")
            return {
                'model_name': model_name,
                'status': 'failed',
                'error': str(e)
            }
    
    logger.info(f"使用{n_jobs}个作业并行训练{len(model_configs)}个模型")
    
    with parallel_backend('loky', n_jobs=n_jobs):
        results = Parallel()(
            delayed(_train_and_evaluate)(model_name, model)
            for model_name, model in model_configs
        )
    
    # 将结果转换为字典
    results_dict = {r['model_name']: r for r in results}
    
    return results_dict

def process_large_dataset(file_path, processing_func, chunk_size=10000, *args, **kwargs):
    """
    处理大型数据集文件
    使用分块读取和处理方式，避免内存溢出
    
    Args:
        file_path: 数据文件路径
        processing_func: 处理函数，接受DataFrame块作为第一个参数
        chunk_size: 每块的大小
        args: 传递给处理函数的位置参数
        kwargs: 传递给处理函数的关键字参数
        
    Returns:
        list: 每个块处理的结果列表
    """
    logger.info(f"开始分块处理大型文件: {file_path}")
    
    results = []
    total_rows = 0
    
    # 使用迭代器逐块读取
    for i, chunk in enumerate(pd.read_csv(file_path, chunksize=chunk_size)):
        logger.info(f"处理第{i+1}块，大小: {len(chunk)}行")
        
        # 优化内存使用
        with reduced_memory_context():
            chunk = reduce_dataframe_memory(chunk)
            result = processing_func(chunk, *args, **kwargs)
            results.append(result)
        
        total_rows += len(chunk)
        
        # 手动清理
        del chunk
        gc.collect()
    
    logger.info(f"大型文件处理完成，共{total_rows}行")
    
    return results

# 示例使用
if __name__ == "__main__":
    # 测试内存优化
    def test_memory_optimization():
        # 创建一个大的DataFrame
        print("创建测试数据...")
        size = 1000000
        test_df = pd.DataFrame({
            'int_col': np.random.randint(0, 100, size=size),
            'float_col': np.random.random(size),
            'str_col': np.random.choice(['A', 'B', 'C', 'D'], size=size)
        })
        
        # 优化内存使用
        print("\n测试DataFrame内存优化...")
        optimized_df = reduce_dataframe_memory(test_df)
        
        # 测试批处理
        print("\n测试批处理...")
        def dummy_process(batch):
            return batch['int_col'].mean()
        
        results = batch_process_dataframe(optimized_df, 200000, dummy_process)
        print(f"批处理结果: {results}")
        
        # 测试内存上下文
        print("\n测试内存优化上下文...")
        with reduced_memory_context():
            # 做一些内存密集型操作
            large_array = np.random.random((10000, 1000))
            mean_val = large_array.mean()
            print(f"大数组平均值: {mean_val}")
            del large_array
    
    test_memory_optimization() 