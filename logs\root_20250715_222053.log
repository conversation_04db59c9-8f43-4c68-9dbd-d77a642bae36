2025-07-15 22:20:53 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:20:53 - model_ensemble - INFO - ============================================================
2025-07-15 22:20:53 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:20:53 - model_ensemble - INFO - ============================================================
2025-07-15 22:20:53 - model_ensemble - INFO - 基础模型: ['RandomForest']
2025-07-15 22:20:53 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 22:20:53 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:20:53 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:20:53 - model_training - INFO - 模型名称: Random Forest
2025-07-15 22:20:53 - model_training - INFO - 准确率: 0.9000
2025-07-15 22:20:53 - model_training - INFO - AUC: 0.9394
2025-07-15 22:20:53 - model_training - INFO - 混淆矩阵:
2025-07-15 22:20:53 - model_training - INFO - 
[[28  2]
 [ 4 26]]
2025-07-15 22:20:53 - model_training - INFO - 
分类报告:
2025-07-15 22:20:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.93      0.90        30
           1       0.93      0.87      0.90        30

    accuracy                           0.90        60
   macro avg       0.90      0.90      0.90        60
weighted avg       0.90      0.90      0.90        60

2025-07-15 22:20:53 - model_training - INFO - 训练时间: 0.13 秒
2025-07-15 22:20:53 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 22:20:53 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 22:20:53 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:20:53 - model_ensemble - INFO - 成功训练了 1 个基础模型
2025-07-15 22:20:53 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:20:53 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:20:53 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:20:53 - model_ensemble - INFO -     voting_soft - 准确率: 0.9000, F1: 0.8999
2025-07-15 22:20:53 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:20:53 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:20:53 - model_ensemble - INFO -     voting_hard - 准确率: 0.9000, F1: 0.8999
2025-07-15 22:20:53 - model_ensemble - INFO - ============================================================
2025-07-15 22:20:53 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:20:53 - model_ensemble - INFO - ============================================================
2025-07-15 22:20:53 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 22:20:53 - model_ensemble - INFO - 最佳F1分数: 0.8999
2025-07-15 22:20:53 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-15 22:20:53 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:20:53 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9000, 精确率: 0.9018, 召回率: 0.9000, F1: 0.8999, AUC: 0.9394
2025-07-15 22:20:53 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9000, 精确率: 0.9018, 召回率: 0.9000, F1: 0.8999, AUC: 0.0000
2025-07-15 22:20:53 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:20:54 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_222053.joblib
2025-07-15 22:20:54 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:20:54 - safe_visualization - INFO - Summary report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_summary_report.txt
2025-07-15 22:20:54 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 22:20:54 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:20:54 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:20:54 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:20:54 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([-0.03032095, -0.04099095, -0.13787238, -0.00622952, -0.01158095,
       -0.02280762, -0.05077095, -0.0743581 , -0.09491429, -0.01955429])
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([0.03032095, 0.04099095, 0.13787238, 0.00622952, 0.01158095,
       0.02280762, 0.05077095, 0.0743581 , 0.09491429, 0.01955429])
2025-07-15 22:20:54 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([ 0.04224476,  0.00306286,  0.13789429,  0.00669429,  0.00935952,
        0.02448286,  0.01918619, -0.02364571,  0.14840619,  0.02291476])
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([-0.04224476, -0.00306286, -0.13789429, -0.00669429, -0.00935952,
       -0.02448286, -0.01918619,  0.02364571, -0.14840619, -0.02291476])
2025-07-15 22:20:54 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([ 0.0299919 ,  0.00435048,  0.11318524,  0.01904667, -0.01347857,
       -0.02941619, -0.00390476,  0.01827   ,  0.12344524,  0.02911   ])
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([-0.0299919 , -0.00435048, -0.11318524, -0.01904667,  0.01347857,
        0.02941619,  0.00390476, -0.01827   , -0.12344524, -0.02911   ])
2025-07-15 22:20:54 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([ 0.01024476,  0.00183476,  0.10985476, -0.00686524,  0.00774143,
       -0.09745333, -0.0741319 , -0.15452524,  0.03928   ,  0.03462   ])
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([-0.01024476, -0.00183476, -0.10985476,  0.00686524, -0.00774143,
        0.09745333,  0.0741319 ,  0.15452524, -0.03928   , -0.03462   ])
2025-07-15 22:20:54 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([ 0.00976048,  0.00448619,  0.02278762,  0.01631952,  0.00139524,
       -0.04869143, -0.0957419 , -0.05016429,  0.06973095, -0.11928238])
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([-0.00976048, -0.00448619, -0.02278762, -0.01631952, -0.00139524,
        0.04869143,  0.0957419 ,  0.05016429, -0.06973095,  0.11928238])
2025-07-15 22:20:54 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([-0.03548667, -0.0299331 , -0.03077976, -0.05392167,  0.00113524,
        0.01238024, -0.08217976, -0.05708833, -0.11804643,  0.02452024])
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([ 0.03548667,  0.0299331 ,  0.03077976,  0.05392167, -0.00113524,
       -0.01238024,  0.08217976,  0.05708833,  0.11804643, -0.02452024])
2025-07-15 22:20:54 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([ 0.02601   , -0.00426333,  0.09015667, -0.00364667, -0.00784667,
        0.05184   ,  0.03442333, -0.02586667, -0.07126   ,  0.03105333])
2025-07-15 22:20:54 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:54 - shap - INFO - phi = array([-0.02601   ,  0.00426333, -0.09015667,  0.00364667,  0.00784667,
       -0.05184   , -0.03442333,  0.02586667,  0.07126   , -0.03105333])
2025-07-15 22:20:54 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.05098333, -0.02781667, -0.05565667,  0.00263   ,  0.00229333,
        0.00834667, -0.12544667, -0.01093   ,  0.03161333, -0.02345   ])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([ 0.05098333,  0.02781667,  0.05565667, -0.00263   , -0.00229333,
       -0.00834667,  0.12544667,  0.01093   , -0.03161333,  0.02345   ])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([ 0.02500095, -0.00804286,  0.10436762, -0.00719   , -0.04408905,
        0.0220719 , -0.08802095, -0.0872381 ,  0.00656714,  0.01717333])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.02500095,  0.00804286, -0.10436762,  0.00719   ,  0.04408905,
       -0.0220719 ,  0.08802095,  0.0872381 , -0.00656714, -0.01717333])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([ 0.00679333, -0.00754667, -0.08453857,  0.02267143,  0.01210333,
       -0.08780857, -0.06542524, -0.1331319 , -0.05601857, -0.05649857])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.00679333,  0.00754667,  0.08453857, -0.02267143, -0.01210333,
        0.08780857,  0.06542524,  0.1331319 ,  0.05601857,  0.05649857])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.00333667, -0.01548667, -0.16414667,  0.00202333, -0.01114   ,
       -0.06108333, -0.05708   , -0.07418   , -0.07906   , -0.00591   ])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([ 0.00333667,  0.01548667,  0.16414667, -0.00202333,  0.01114   ,
        0.06108333,  0.05708   ,  0.07418   ,  0.07906   ,  0.00591   ])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.04194667, -0.02185333, -0.15780667, -0.00902667,  0.00194   ,
       -0.04936   , -0.00459667, -0.06471667, -0.13163333,  0.0096    ])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([ 0.04194667,  0.02185333,  0.15780667,  0.00902667, -0.00194   ,
        0.04936   ,  0.00459667,  0.06471667,  0.13163333, -0.0096    ])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([1.76471429e-02, 2.12859524e-02, 6.69345238e-02, 1.28073810e-02,
       6.71428571e-05, 2.19940476e-02, 9.23178571e-02, 9.47840476e-02,
       9.02845238e-02, 2.24773810e-02])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-1.76471429e-02, -2.12859524e-02, -6.69345238e-02, -1.28073810e-02,
       -6.71428571e-05, -2.19940476e-02, -9.23178571e-02, -9.47840476e-02,
       -9.02845238e-02, -2.24773810e-02])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.04309024, -0.0258131 , -0.147455  , -0.0101781 , -0.01061833,
       -0.06049357,  0.0488569 , -0.04534714, -0.10442643, -0.000835  ])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([ 0.04309024,  0.0258131 ,  0.147455  ,  0.0101781 ,  0.01061833,
        0.06049357, -0.0488569 ,  0.04534714,  0.10442643,  0.000835  ])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([0.02107349, 0.01771159, 0.05294706, 0.0137073 , 0.00378111,
       0.05814968, 0.10150778, 0.07818111, 0.07127825, 0.02226262])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.02107349, -0.01771159, -0.05294706, -0.0137073 , -0.00378111,
       -0.05814968, -0.10150778, -0.07818111, -0.07127825, -0.02226262])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([ 0.0315119 , -0.00572667,  0.13339857, -0.00348476, -0.00122333,
        0.02458857,  0.11194   , -0.00788143,  0.1262219 ,  0.01125524])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.0315119 ,  0.00572667, -0.13339857,  0.00348476,  0.00122333,
       -0.02458857, -0.11194   ,  0.00788143, -0.1262219 , -0.01125524])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.07094333,  0.04278286, -0.05712429, -0.09138619,  0.00346095,
        0.01599857, -0.00872095,  0.17001048, -0.0906919 ,  0.01721381])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([ 0.07094333, -0.04278286,  0.05712429,  0.09138619, -0.00346095,
       -0.01599857,  0.00872095, -0.17001048,  0.0906919 , -0.01721381])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([ 0.00923833,  0.03732952,  0.03761976,  0.03599976, -0.009385  ,
        0.02485833, -0.03608167,  0.15716452, -0.01817167,  0.0320281 ])
2025-07-15 22:20:55 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:55 - shap - INFO - phi = array([-0.00923833, -0.03732952, -0.03761976, -0.03599976,  0.009385  ,
       -0.02485833,  0.03608167, -0.15716452,  0.01817167, -0.0320281 ])
2025-07-15 22:20:55 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.02000714,  0.0257319 , -0.04106   , -0.01308286,  0.00057048,
        0.06983   ,  0.05234381,  0.01482714,  0.1292519 , -0.07781952])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.02000714, -0.0257319 ,  0.04106   ,  0.01308286, -0.00057048,
       -0.06983   , -0.05234381, -0.01482714, -0.1292519 ,  0.07781952])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.0121381 , -0.02616714,  0.07151429, -0.02560143, -0.0242519 ,
        0.01651238, -0.04169238, -0.03218429, -0.09100238, -0.01438905])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.0121381 ,  0.02616714, -0.07151429,  0.02560143,  0.0242519 ,
       -0.01651238,  0.04169238,  0.03218429,  0.09100238,  0.01438905])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.05669286, -0.02678405, -0.04648595, -0.02629452,  0.00285571,
       -0.05532119, -0.06373071, -0.04769786, -0.07377738,  0.01452881])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.05669286,  0.02678405,  0.04648595,  0.02629452, -0.00285571,
        0.05532119,  0.06373071,  0.04769786,  0.07377738, -0.01452881])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.00475952,  0.00672357, -0.09778976, -0.00076214, -0.00762619,
       -0.10231357, -0.031395  , -0.13160214, -0.0914669 , -0.06840833])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.00475952, -0.00672357,  0.09778976,  0.00076214,  0.00762619,
        0.10231357,  0.031395  ,  0.13160214,  0.0914669 ,  0.06840833])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.01892   , -0.02037524, -0.10991524,  0.06101524,  0.01768333,
        0.02384857, -0.07212333, -0.09089476, -0.09815667,  0.0205981 ])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.01892   ,  0.02037524,  0.10991524, -0.06101524, -0.01768333,
       -0.02384857,  0.07212333,  0.09089476,  0.09815667, -0.0205981 ])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.01950524, -0.00169714,  0.01435286, -0.01015   ,  0.0004881 ,
       -0.09725381, -0.01904524,  0.13607143,  0.05734619,  0.01999286])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.01950524,  0.00169714, -0.01435286,  0.01015   , -0.0004881 ,
        0.09725381,  0.01904524, -0.13607143, -0.05734619, -0.01999286])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.04067563, -0.0191654 , -0.11999897,  0.00025222, -0.00344944,
       -0.0251323 , -0.03227754, -0.04632206, -0.12721563, -0.04541524])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.04067563,  0.0191654 ,  0.11999897, -0.00025222,  0.00344944,
        0.0251323 ,  0.03227754,  0.04632206,  0.12721563,  0.04541524])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.01960595,  0.00329024,  0.1137369 , -0.00220333,  0.00691071,
       -0.03325786,  0.1074369 ,  0.02449143,  0.11800548,  0.02258357])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.01960595, -0.00329024, -0.1137369 ,  0.00220333, -0.00691071,
        0.03325786, -0.1074369 , -0.02449143, -0.11800548, -0.02258357])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.00537571,  0.02929095, -0.06606429,  0.02039286, -0.00120643,
        0.05211476,  0.09176476,  0.14066952,  0.07156381, -0.07330167])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.00537571, -0.02929095,  0.06606429, -0.02039286,  0.00120643,
       -0.05211476, -0.09176476, -0.14066952, -0.07156381,  0.07330167])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([-0.02483143,  0.05683857, -0.08278143,  0.0531319 ,  0.00268857,
        0.04344333, -0.0142181 ,  0.22297857, -0.06654667,  0.00989667])
2025-07-15 22:20:56 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:56 - shap - INFO - phi = array([ 0.02483143, -0.05683857,  0.08278143, -0.0531319 , -0.00268857,
       -0.04344333,  0.0142181 , -0.22297857,  0.06654667, -0.00989667])
2025-07-15 22:20:56 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([-0.00757214, -0.02012548, -0.07731167, -0.01850667,  0.00432214,
        0.01451119,  0.10027024, -0.01052381,  0.00881786,  0.02671833])
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([ 0.00757214,  0.02012548,  0.07731167,  0.01850667, -0.00432214,
       -0.01451119, -0.10027024,  0.01052381, -0.00881786, -0.02671833])
2025-07-15 22:20:57 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([ 0.04356762, -0.00589952,  0.12500524, -0.01638714, -0.00632762,
        0.04857571,  0.00138952, -0.00364714,  0.13926238,  0.03506095])
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([-0.04356762,  0.00589952, -0.12500524,  0.01638714,  0.00632762,
       -0.04857571, -0.00138952,  0.00364714, -0.13926238, -0.03506095])
2025-07-15 22:20:57 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([ 0.02622905,  0.0091719 ,  0.06871048,  0.07243048, -0.00381429,
        0.03214571, -0.06686286,  0.0272419 , -0.08431762,  0.02966524])
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([-0.02622905, -0.0091719 , -0.06871048, -0.07243048,  0.00381429,
       -0.03214571,  0.06686286, -0.0272419 ,  0.08431762, -0.02966524])
2025-07-15 22:20:57 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([ 0.04222071, -0.0097419 ,  0.12542548, -0.00405929,  0.00789071,
        0.05354214,  0.04753548, -0.00316548,  0.13304548,  0.02790667])
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([-0.04222071,  0.0097419 , -0.12542548,  0.00405929, -0.00789071,
       -0.05354214, -0.04753548,  0.00316548, -0.13304548, -0.02790667])
2025-07-15 22:20:57 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([ 0.03075524,  0.00368071,  0.09347452, -0.00775643, -0.0049081 ,
        0.05526452, -0.13572405, -0.00639119,  0.10088643,  0.01131833])
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([-0.03075524, -0.00368071, -0.09347452,  0.00775643,  0.0049081 ,
       -0.05526452,  0.13572405,  0.00639119, -0.10088643, -0.01131833])
2025-07-15 22:20:57 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([ 0.03272405,  0.00020929,  0.09238167,  0.01455571, -0.000715  ,
        0.06257214,  0.11009548,  0.00959476,  0.0850269 ,  0.014155  ])
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([-0.03272405, -0.00020929, -0.09238167, -0.01455571,  0.000715  ,
       -0.06257214, -0.11009548, -0.00959476, -0.0850269 , -0.014155  ])
2025-07-15 22:20:57 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([0.02230476, 0.00563429, 0.09274762, 0.00021476, 0.01113143,
       0.06053143, 0.09138   , 0.02839476, 0.0495281 , 0.02873286])
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([-0.02230476, -0.00563429, -0.09274762, -0.00021476, -0.01113143,
       -0.06053143, -0.09138   , -0.02839476, -0.0495281 , -0.02873286])
2025-07-15 22:20:57 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([ 0.01953667,  0.00302024,  0.04434357, -0.00823833,  0.0119519 ,
       -0.05341976, -0.0370131 , -0.070585  , -0.11715976,  0.03816357])
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([-0.01953667, -0.00302024, -0.04434357,  0.00823833, -0.0119519 ,
        0.05341976,  0.0370131 ,  0.070585  ,  0.11715976, -0.03816357])
2025-07-15 22:20:57 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([-0.05919714,  0.00083667,  0.05551667, -0.01111429,  0.01450095,
        0.01255286,  0.08048238,  0.12855429,  0.02130952,  0.0071581 ])
2025-07-15 22:20:57 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:57 - shap - INFO - phi = array([ 0.05919714, -0.00083667, -0.05551667,  0.01111429, -0.01450095,
       -0.01255286, -0.08048238, -0.12855429, -0.02130952, -0.0071581 ])
2025-07-15 22:20:57 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.02055119, -0.01506214, -0.15542595, -0.01026333, -0.00367595,
       -0.06056929,  0.08678214, -0.05662333, -0.10472786,  0.0107169 ])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([ 0.02055119,  0.01506214,  0.15542595,  0.01026333,  0.00367595,
        0.06056929, -0.08678214,  0.05662333,  0.10472786, -0.0107169 ])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.05680738, -0.045285  , -0.04408357, -0.04485857, -0.01088405,
        0.00380357, -0.07752881, -0.04311857, -0.09239405,  0.02175643])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([ 0.05680738,  0.045285  ,  0.04408357,  0.04485857,  0.01088405,
       -0.00380357,  0.07752881,  0.04311857,  0.09239405, -0.02175643])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([ 0.01996238,  0.03386333, -0.02504762,  0.02846429,  0.01133476,
        0.09765952,  0.03945738,  0.1242281 ,  0.0324169 ,  0.02826095])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.01996238, -0.03386333,  0.02504762, -0.02846429, -0.01133476,
       -0.09765952, -0.03945738, -0.1242281 , -0.0324169 , -0.02826095])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.0011619 ,  0.00098   , -0.0793    ,  0.00556524, -0.00331333,
       -0.09782857, -0.05019143, -0.0552    , -0.07862667,  0.02967667])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([ 0.0011619 , -0.00098   ,  0.0793    , -0.00556524,  0.00331333,
        0.09782857,  0.05019143,  0.0552    ,  0.07862667, -0.02967667])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([ 0.03121286,  0.01059048,  0.14084619, -0.00120619,  0.00398381,
       -0.06675048,  0.04020762, -0.0097881 ,  0.13371619,  0.00778762])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.03121286, -0.01059048, -0.14084619,  0.00120619, -0.00398381,
        0.06675048, -0.04020762,  0.0097881 , -0.13371619, -0.00778762])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.02970952, -0.02916667, -0.05311667, -0.02335048,  0.00426714,
       -0.0078719 , -0.08268   , -0.10537476, -0.11398667, -0.05841048])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([ 0.02970952,  0.02916667,  0.05311667,  0.02335048, -0.00426714,
        0.0078719 ,  0.08268   ,  0.10537476,  0.11398667,  0.05841048])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.03217667,  0.011145  , -0.13472833,  0.01516167, -0.00585619,
       -0.06352405, -0.0368269 , -0.0690969 , -0.11528548,  0.00178786])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([ 0.03217667, -0.011145  ,  0.13472833, -0.01516167,  0.00585619,
        0.06352405,  0.0368269 ,  0.0690969 ,  0.11528548, -0.00178786])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([0.02118357, 0.01315476, 0.04795381, 0.01578333, 0.00203405,
       0.04640071, 0.07818476, 0.10337333, 0.08058476, 0.0219469 ])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.02118357, -0.01315476, -0.04795381, -0.01578333, -0.00203405,
       -0.04640071, -0.07818476, -0.10337333, -0.08058476, -0.0219469 ])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([ 0.03671667,  0.00233619,  0.09382762, -0.00892905,  0.0060081 ,
        0.02723143,  0.11661286, -0.01485905,  0.09879095,  0.03286429])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.03671667, -0.00233619, -0.09382762,  0.00892905, -0.0060081 ,
       -0.02723143, -0.11661286,  0.01485905, -0.09879095, -0.03286429])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([-0.0115119 ,  0.04487643, -0.08748881,  0.0018169 ,  0.00638476,
        0.02013976,  0.04763643,  0.17922024, -0.09124071,  0.0207669 ])
2025-07-15 22:20:58 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:58 - shap - INFO - phi = array([ 0.0115119 , -0.04487643,  0.08748881, -0.0018169 , -0.00638476,
       -0.02013976, -0.04763643, -0.17922024,  0.09124071, -0.0207669 ])
2025-07-15 22:20:58 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.0081919 ,  0.00815143,  0.03292524,  0.00630333,  0.00584524,
       -0.0941619 , -0.02523   ,  0.01530667,  0.08349857, -0.00023048])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.0081919 , -0.00815143, -0.03292524, -0.00630333, -0.00584524,
        0.0941619 ,  0.02523   , -0.01530667, -0.08349857,  0.00023048])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.00381238, -0.00099333, -0.04924238, -0.00382762,  0.01081024,
        0.11676571, -0.03032857, -0.03478762, -0.04503095, -0.07657786])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.00381238,  0.00099333,  0.04924238,  0.00382762, -0.01081024,
       -0.11676571,  0.03032857,  0.03478762,  0.04503095,  0.07657786])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.02155476,  0.00556619,  0.02331238,  0.00153286,  0.01045095,
        0.09236238,  0.00915762, -0.03242667,  0.10527905, -0.11618952])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.02155476, -0.00556619, -0.02331238, -0.00153286, -0.01045095,
       -0.09236238, -0.00915762,  0.03242667, -0.10527905,  0.11618952])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.03740429,  0.00498667,  0.09279095,  0.01110095, -0.00107714,
        0.08630429,  0.06601619,  0.02023143,  0.09713429,  0.0257081 ])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.03740429, -0.00498667, -0.09279095, -0.01110095,  0.00107714,
       -0.08630429, -0.06601619, -0.02023143, -0.09713429, -0.0257081 ])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.01511119,  0.068555  , -0.061475  ,  0.01068548,  0.00162595,
        0.02300095, -0.01834262,  0.19748881, -0.04542762, -0.08039976])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.01511119, -0.068555  ,  0.061475  , -0.01068548, -0.00162595,
       -0.02300095,  0.01834262, -0.19748881,  0.04542762,  0.08039976])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.0364381 ,  0.04771667, -0.00635143, -0.09648571, -0.03925429,
        0.00893   , -0.02843619,  0.13102286, -0.0532319 , -0.0668719 ])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.0364381 , -0.04771667,  0.00635143,  0.09648571,  0.03925429,
       -0.00893   ,  0.02843619, -0.13102286,  0.0532319 ,  0.0668719 ])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.06667333, -0.02816833,  0.07755167, -0.03741833,  0.0125581 ,
       -0.00461976, -0.08730881, -0.034155  , -0.112405  ,  0.00123881])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.06667333,  0.02816833, -0.07755167,  0.03741833, -0.0125581 ,
        0.00461976,  0.08730881,  0.034155  ,  0.112405  , -0.00123881])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.04586286,  0.01301095,  0.10261048, -0.00421762,  0.01562286,
        0.08073381,  0.01184095,  0.01307905,  0.02483048,  0.03722619])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.04586286, -0.01301095, -0.10261048,  0.00421762, -0.01562286,
       -0.08073381, -0.01184095, -0.01307905, -0.02483048, -0.03722619])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.01131905,  0.05323   , -0.06916476, -0.03838571, -0.00562429,
       -0.0035181 , -0.08193429, -0.01344905, -0.06776571, -0.08146905])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.01131905, -0.05323   ,  0.06916476,  0.03838571,  0.00562429,
        0.0035181 ,  0.08193429,  0.01344905,  0.06776571,  0.08146905])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([-0.01784333, -0.03990333, -0.12359   ,  0.03398   , -0.00746667,
        0.02769   , -0.07136333, -0.10901667, -0.08532   ,  0.01343333])
2025-07-15 22:20:59 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:20:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:20:59 - shap - INFO - phi = array([ 0.01784333,  0.03990333,  0.12359   , -0.03398   ,  0.00746667,
       -0.02769   ,  0.07136333,  0.10901667,  0.08532   , -0.01343333])
2025-07-15 22:20:59 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:00 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:00 - shap - INFO - phi = array([ 0.01025333, -0.02076   , -0.06666333,  0.02002333, -0.00115333,
       -0.03441667, -0.06400667, -0.14327667, -0.09464667,  0.02524667])
2025-07-15 22:21:00 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:00 - shap - INFO - phi = array([-0.01025333,  0.02076   ,  0.06666333, -0.02002333,  0.00115333,
        0.03441667,  0.06400667,  0.14327667,  0.09464667, -0.02524667])
2025-07-15 22:21:00 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:00 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:00 - shap - INFO - phi = array([ 0.03314952,  0.01176524,  0.08174571,  0.01146452,  0.00177714,
        0.06606333,  0.09879667, -0.01206571,  0.09002905,  0.01787452])
2025-07-15 22:21:00 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:00 - shap - INFO - phi = array([-0.03314952, -0.01176524, -0.08174571, -0.01146452, -0.00177714,
       -0.06606333, -0.09879667,  0.01206571, -0.09002905, -0.01787452])
2025-07-15 22:21:00 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:00 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:00 - shap - INFO - phi = array([-0.03614286, -0.03037619, -0.16307286, -0.00713333, -0.0109781 ,
       -0.06009952, -0.0140881 , -0.06109667, -0.10936286, -0.00704952])
2025-07-15 22:21:00 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:00 - shap - INFO - phi = array([0.03614286, 0.03037619, 0.16307286, 0.00713333, 0.0109781 ,
       0.06009952, 0.0140881 , 0.06109667, 0.10936286, 0.00704952])
2025-07-15 22:21:06 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_soft
2025-07-15 22:21:06 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:21:06 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:21:06 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:21:06 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:21:06 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:21:06 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:21:06 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:06 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:06 - shap - INFO - phi = array([ 0.02818254,  0.05050794,  0.14776984,  0.00773016, -0.00119048,
        0.02125397,  0.05252381,  0.11062698,  0.12148413,  0.02111111])
2025-07-15 22:21:06 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:06 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:06 - shap - INFO - phi = array([-0.0499127 ,  0.00403968, -0.15806349, -0.00571429, -0.00806349,
       -0.02262698, -0.01801587,  0.01279365, -0.17180952, -0.02262698])
2025-07-15 22:21:06 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:07 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:07 - shap - INFO - phi = array([-0.02189683, -0.01976984, -0.15870635, -0.02739683,  0.00968254,
        0.0072381 , -0.00904762, -0.04557143, -0.14233333, -0.03219841])
2025-07-15 22:21:07 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:07 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:07 - shap - INFO - phi = array([-0.0127619 , -0.00240476, -0.13804762,  0.03300794, -0.02413492,
        0.27288095,  0.1489127 ,  0.39771429, -0.0632619 , -0.05190476])
2025-07-15 22:21:07 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:07 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:07 - shap - INFO - phi = array([-0.01577778, -0.00807937, -0.01999206, -0.02697619, -0.0038254 ,
        0.07588095,  0.1642619 ,  0.13142857, -0.06257143,  0.32565079])
2025-07-15 22:21:07 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:08 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:08 - shap - INFO - phi = array([ 0.04095238,  0.03171429,  0.06352381,  0.05665873, -0.00153968,
       -0.01288889,  0.0840873 ,  0.12260317,  0.20492857, -0.03003968])
2025-07-15 22:21:08 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:08 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:08 - shap - INFO - phi = array([-0.04572222, -0.00639683, -0.20450794, -0.00706349,  0.00751587,
       -0.05973016, -0.07597619,  0.01259524,  0.02053175, -0.08124603])
2025-07-15 22:21:08 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:08 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:08 - shap - INFO - phi = array([ 0.07046208,  0.04111287,  0.15945414,  0.00247795,  0.        ,
       -0.00873633,  0.21491446,  0.07588272, -0.02272046,  0.02715256])
2025-07-15 22:21:08 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:08 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:08 - shap - INFO - phi = array([-0.14255556,  0.01962698, -0.33163492, -0.06207143,  0.09323016,
       -0.04844444,  0.0551746 ,  0.13813492, -0.09734127, -0.06411905])
2025-07-15 22:21:08 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:09 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:09 - shap - INFO - phi = array([ 0.00540476,  0.00756349,  0.09580952, -0.01745238, -0.00734127,
        0.0677381 ,  0.08418254,  0.19102381,  0.06080952,  0.0722619 ])
2025-07-15 22:21:09 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:09 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:09 - shap - INFO - phi = array([ 0.01302381,  0.00775397,  0.25161111, -0.00661905, -0.00850794,
        0.04165873,  0.04744444,  0.12383333,  0.0935    , -0.00369841])
2025-07-15 22:21:09 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:09 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:09 - shap - INFO - phi = array([ 0.05438889,  0.01651587,  0.1805873 ,  0.00498413,  0.00019841,
        0.04593651,  0.00501587,  0.1044127 ,  0.16086508, -0.01290476])
2025-07-15 22:21:09 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:10 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:10 - shap - INFO - phi = array([-0.01063316, -0.01876808, -0.0785776 , -0.01288713,  0.        ,
       -0.01524427, -0.06938713, -0.12854586, -0.08268871, -0.02326808])
2025-07-15 22:21:10 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:10 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:10 - shap - INFO - phi = array([ 0.04914286,  0.0338254 ,  0.23898413,  0.0075    , -0.00514286,
        0.07512698, -0.03869048,  0.07405556,  0.12797619, -0.00277778])
2025-07-15 22:21:10 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:10 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:10 - shap - INFO - phi = array([-0.01288095, -0.02338095, -0.05503968, -0.01168254, -0.00377778,
       -0.05225397, -0.08800794, -0.1136746 , -0.06161111, -0.01769048])
2025-07-15 22:21:10 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:10 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:10 - shap - INFO - phi = array([-0.02020635,  0.00356349, -0.16207937,  0.00556349, -0.00521429,
       -0.02093651, -0.10845238,  0.00854762, -0.12955556, -0.01123016])
2025-07-15 22:21:10 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:11 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:11 - shap - INFO - phi = array([ 0.21749206, -0.04015873,  0.12390476,  0.23985714,  0.01924603,
       -0.03646825,  0.12418254, -0.29188095,  0.22118254, -0.01735714])
2025-07-15 22:21:11 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:11 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:11 - shap - INFO - phi = array([-0.00653175, -0.03059524, -0.06807143, -0.03442857,  0.00506349,
       -0.01872222,  0.00585714, -0.24334921, -0.01712698, -0.03209524])
2025-07-15 22:21:11 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:11 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:11 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:11 - shap - INFO - phi = array([-0.01681746, -0.03646032,  0.0164127 ,  0.01530159, -0.00559524,
       -0.10239683, -0.10233333, -0.02430159, -0.26624603,  0.08243651])
2025-07-15 22:21:11 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:12 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:12 - shap - INFO - phi = array([ 0.01713492,  0.04721429, -0.11907143,  0.0558254 ,  0.06565079,
        0.04266667,  0.10509524,  0.05855556,  0.23952381,  0.04740476])
2025-07-15 22:21:12 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:12 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:12 - shap - INFO - phi = array([ 0.06292063,  0.0269127 ,  0.10312698,  0.02846825, -0.01274603,
        0.05899206,  0.08946032,  0.10465873,  0.11818254, -0.01997619])
2025-07-15 22:21:12 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:12 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:12 - shap - INFO - phi = array([ 0.00142063, -0.0038254 ,  0.09763492, -0.00684921, -0.00196032,
        0.09105556,  0.01292063,  0.18754762,  0.1121746 ,  0.06988095])
2025-07-15 22:21:12 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:13 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:13 - shap - INFO - phi = array([-0.00204762,  0.02170635,  0.23924603, -0.06013492, -0.00542857,
       -0.01148413,  0.07456349,  0.16559524,  0.17331746, -0.03533333])
2025-07-15 22:21:13 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:13 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:13 - shap - INFO - phi = array([ 0.00892063, -0.01215873, -0.04747619,  0.00790476, -0.00583333,
        0.06744444,  0.00134921, -0.3513254 , -0.07654762, -0.03227778])
2025-07-15 22:21:13 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:13 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:13 - shap - INFO - phi = array([ 0.05715873,  0.01819048,  0.12796032,  0.0231746 , -0.00257937,
        0.01816667,  0.0270873 ,  0.06013492,  0.15789683,  0.07280952])
2025-07-15 22:21:13 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:13 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:13 - shap - INFO - phi = array([-0.01011905, -0.00596825, -0.13507143,  0.00192857, -0.00697619,
        0.00653175, -0.10012698, -0.03627778, -0.12664286, -0.02727778])
2025-07-15 22:21:13 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:14 - shap - INFO - phi = array([-0.00252381, -0.0364127 ,  0.04584127, -0.01911905, -0.00184127,
       -0.05939683, -0.11851587, -0.20383333, -0.09446032,  0.0502619 ])
2025-07-15 22:21:14 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:14 - shap - INFO - phi = array([ 1.68730159e-02, -5.79047619e-02,  8.75634921e-02, -6.46666667e-02,
       -3.65079365e-04, -2.46904762e-02, -2.77777778e-04, -4.63388889e-01,
        7.64682540e-02, -9.61111111e-03])
2025-07-15 22:21:14 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:14 - shap - INFO - phi = array([-0.08365079,  0.05143651,  0.16854762,  0.04347619, -0.0106746 ,
       -0.10101587, -0.26995238,  0.02157937, -0.18056349, -0.07918254])
2025-07-15 22:21:14 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:15 - shap - INFO - phi = array([-0.04123016, -0.00500794, -0.15595238,  0.02087302,  0.01460317,
       -0.05164286, -0.01857143,  0.00590476, -0.17084921, -0.03812698])
2025-07-15 22:21:15 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:15 - shap - INFO - phi = array([-0.03811905, -0.01360317, -0.15184921, -0.17842063,  0.00580952,
       -0.0377619 ,  0.04642857, -0.07925397,  0.08633333, -0.07956349])
2025-07-15 22:21:15 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:15 - shap - INFO - phi = array([-0.03562698,  0.00613492, -0.14868254,  0.00814286, -0.01119841,
       -0.04956349, -0.04038889,  0.00762698, -0.15237302, -0.02407143])
2025-07-15 22:21:15 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:15 - shap - INFO - phi = array([-0.04250794, -0.00639683, -0.1626746 ,  0.0152381 ,  0.00827778,
       -0.07453968,  0.06783333,  0.00303175, -0.21734921, -0.0309127 ])
2025-07-15 22:21:15 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:16 - shap - INFO - phi = array([-0.02211905, -0.00606349, -0.1080873 , -0.02477778,  0.0025    ,
       -0.06518254, -0.1003254 , -0.01378571, -0.08719048, -0.01496825])
2025-07-15 22:21:16 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:16 - shap - INFO - phi = array([-0.0130873 , -0.00104762, -0.12542857,  0.00228571, -0.0094127 ,
       -0.05507143, -0.09648413, -0.04348413, -0.06940476, -0.02886508])
2025-07-15 22:21:16 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:16 - shap - INFO - phi = array([-0.01873016, -0.00369841,  0.04796825,  0.00971429, -0.01912698,
        0.12706349,  0.08368254,  0.15986508,  0.22414286, -0.05088095])
2025-07-15 22:21:16 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:16 - shap - INFO - phi = array([ 0.0141746 , -0.00281746, -0.07159524,  0.00637302, -0.01952381,
       -0.0085    , -0.09481746, -0.21438889, -0.03943651, -0.00946825])
2025-07-15 22:21:16 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:17 - shap - INFO - phi = array([ 0.01023016,  0.01123016,  0.30080952,  0.00275397, -0.00839683,
        0.06237302, -0.07613492,  0.12705556,  0.14721429, -0.01713492])
2025-07-15 22:21:17 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:17 - shap - INFO - phi = array([ 0.05449206,  0.04833333,  0.08037302,  0.0529127 ,  0.02868254,
       -0.01069841,  0.12113492,  0.07070635,  0.1413254 , -0.0272619 ])
2025-07-15 22:21:17 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:17 - shap - INFO - phi = array([-0.02003968, -0.03244444,  0.00803175, -0.03070635, -0.0052619 ,
       -0.09559524, -0.03003968, -0.16392063, -0.04661905, -0.02340476])
2025-07-15 22:21:17 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:18 - shap - INFO - phi = array([ 0.0013254 , -0.00057937,  0.14083333, -0.01043651,  0.01242063,
        0.15401587,  0.03753175,  0.1265873 ,  0.13811111, -0.03980952])
2025-07-15 22:21:18 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:18 - shap - INFO - phi = array([-0.0345    , -0.01042857, -0.20352381, -0.00184127, -0.00631746,
        0.02630952, -0.04954762,  0.00669048, -0.15094444, -0.01589683])
2025-07-15 22:21:18 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:18 - shap - INFO - phi = array([ 0.01765079,  0.02931746,  0.06730159,  0.0165873 , -0.00763492,
        0.00233333,  0.08342063,  0.1465    ,  0.14389683,  0.06062698])
2025-07-15 22:21:18 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:19 - shap - INFO - phi = array([ 0.03583333,  0.00792857,  0.15577778, -0.01506349, -0.00830159,
        0.05763492,  0.03480952,  0.14675397,  0.15261905, -0.00799206])
2025-07-15 22:21:19 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:19 - shap - INFO - phi = array([-0.00862698, -0.01930952, -0.05771429, -0.01899206, -0.00131746,
       -0.03639683, -0.06187302, -0.14819048, -0.07171429, -0.01586508])
2025-07-15 22:21:19 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:19 - shap - INFO - phi = array([-0.02480159,  0.00021429, -0.12303175,  0.01173016, -0.00685714,
       -0.02876984, -0.13398413,  0.00901587, -0.10985714, -0.03365873])
2025-07-15 22:21:19 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:19 - shap - INFO - phi = array([ 0.01057143, -0.06279365,  0.09199206, -0.01707937, -0.00546825,
       -0.01706349, -0.07471429, -0.42544444,  0.08977778, -0.02977778])
2025-07-15 22:21:19 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:20 - shap - INFO - phi = array([-0.00861111, -0.01628571, -0.16302381, -0.02426984, -0.00171429,
        0.08157937,  0.00056349, -0.05303968, -0.21394444, -0.04125397])
2025-07-15 22:21:20 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:20 - shap - INFO - phi = array([-0.00496825,  0.00830952,  0.15034921,  0.00571429, -0.03031746,
       -0.10714286,  0.08589683,  0.10228571,  0.15681746,  0.19305556])
2025-07-15 22:21:20 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:20 - shap - INFO - phi = array([-0.0207619 , -0.01040476, -0.04352381, -0.00154762, -0.00918254,
       -0.17092857, -0.08268254,  0.02989683, -0.24392857,  0.11306349])
2025-07-15 22:21:20 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:20 - shap - INFO - phi = array([-0.02880159, -0.00384921, -0.10993651, -0.01974603,  0.00296825,
       -0.08697619, -0.04906349, -0.02196032, -0.10195238, -0.02068254])
2025-07-15 22:21:20 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:21 - shap - INFO - phi = array([ 0.00498413, -0.16910317,  0.08203175, -0.04461111, -0.00565873,
       -0.01984921,  0.00065873, -0.42238889,  0.064     ,  0.06993651])
2025-07-15 22:21:21 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:21 - shap - INFO - phi = array([ 0.05892857, -0.07028571,  0.06503968,  0.28196032,  0.10289683,
       -0.00997619,  0.07239683, -0.21699206,  0.16125397,  0.11477778])
2025-07-15 22:21:21 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:21 - shap - INFO - phi = array([ 0.07954762,  0.04390476, -0.02829365,  0.06722222, -0.0087381 ,
        0.0022619 ,  0.14899206,  0.06595238,  0.19373016, -0.00457937])
2025-07-15 22:21:21 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:22 - shap - INFO - phi = array([-0.04599206, -0.00603968, -0.14743651,  0.00138095, -0.01242063,
       -0.08235714, -0.02718254, -0.01992063, -0.05270635, -0.0473254 ])
2025-07-15 22:21:22 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:22 - shap - INFO - phi = array([ 0.02597619, -0.0277619 ,  0.09886508,  0.03119048,  0.00357937,
       -0.00427778,  0.12730159,  0.09892857,  0.09662698,  0.10957143])
2025-07-15 22:21:22 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:22 - shap - INFO - phi = array([ 0.02860317,  0.03197619,  0.16990476, -0.02661111, -0.00930952,
       -0.0145    ,  0.09746032,  0.17897619,  0.11927778, -0.01577778])
2025-07-15 22:21:22 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:22 - shap - INFO - phi = array([ 0.0012381 ,  0.01316667,  0.11887302, -0.02257143, -0.0102619 ,
        0.04356349,  0.0669127 ,  0.24130159,  0.14034127, -0.03256349])
2025-07-15 22:21:23 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:23 - shap - INFO - phi = array([-0.0185    , -0.02586508, -0.09455556, -0.02323016, -0.01045238,
       -0.07243651, -0.08668254,  0.00033333, -0.0927381 , -0.01587302])
2025-07-15 22:21:23 - shap - INFO - num_full_subsets = 5
2025-07-15 22:21:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-15 22:21:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-15 22:21:23 - shap - INFO - phi = array([ 0.03707937,  0.02772222,  0.18951587,  0.00380952, -0.00279365,
        0.06886508,  0.01114286,  0.08934127,  0.13251587,  0.00280159])
2025-07-15 22:21:29 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_hard
2025-07-15 22:21:29 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:21:29 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:21:29 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:21:29 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -   voting_hard SHAP分析完成
