2025-07-15 23:24:24 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 23:24:24 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 23:24:24 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 23:24:24 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 23:24:24 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:24 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:24 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:24 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:25 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:25 - shap - INFO - phi = array([ 0.05601323, -0.15596045, -0.04135261,  0.        , -0.04282516,
       -0.14285809, -0.15564405,  0.        ,  0.0314532 , -0.00241596,
        0.00419547,  0.01295025])
2025-07-15 23:24:25 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:25 - shap - INFO - phi = array([-0.05601323,  0.15596045,  0.04135261,  0.        ,  0.04282516,
        0.14285809,  0.15564405,  0.        , -0.0314532 ,  0.00241596,
       -0.00419547, -0.01295025])
2025-07-15 23:24:25 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:25 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:25 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:25 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:25 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:25 - shap - INFO - phi = array([-0.01064804,  0.20715738,  0.0325817 , -0.01099492, -0.01605353,
        0.10218005, -0.17472826,  0.        , -0.02013834,  0.06420979,
        0.        ,  0.02018422])
2025-07-15 23:24:25 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:25 - shap - INFO - phi = array([ 0.01064804, -0.20715738, -0.0325817 ,  0.01099492,  0.01605353,
       -0.10218005,  0.17472826,  0.        ,  0.02013834, -0.06420979,
        0.        , -0.02018422])
2025-07-15 23:24:25 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:25 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:25 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:25 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:26 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:26 - shap - INFO - phi = array([ 0.043103  ,  0.00588235, -0.02027229,  0.        , -0.24291924,
       -0.16593399, -0.03194022, -0.01563465,  0.        , -0.00547148,
       -0.00293099, -0.00516709])
2025-07-15 23:24:26 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:26 - shap - INFO - phi = array([-0.043103  , -0.00588235,  0.02027229,  0.        ,  0.24291924,
        0.16593399,  0.03194022,  0.01563465,  0.        ,  0.00547148,
        0.00293099,  0.00516709])
2025-07-15 23:24:26 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:26 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:26 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:26 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:26 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:26 - shap - INFO - phi = array([-0.04897103, -0.16077531, -0.0339896 ,  0.0196791 ,  0.2207025 ,
       -0.33056926, -0.02813883,  0.        , -0.01887892,  0.008451  ,
       -0.01551629,  0.        ])
2025-07-15 23:24:26 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:26 - shap - INFO - phi = array([ 0.04897103,  0.16077531,  0.0339896 , -0.0196791 , -0.2207025 ,
        0.33056926,  0.02813883,  0.        ,  0.01887892, -0.008451  ,
        0.01551629,  0.        ])
2025-07-15 23:24:26 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:26 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:26 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:26 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:27 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:27 - shap - INFO - phi = array([-0.11738683, -0.10950338, -0.02404713,  0.00467783, -0.2200648 ,
        0.05508021, -0.00391774, -0.00936898, -0.00790053,  0.        ,
       -0.00377371,  0.        ])
2025-07-15 23:24:27 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:27 - shap - INFO - phi = array([ 0.11738683,  0.10950338,  0.02404713, -0.00467783,  0.2200648 ,
       -0.05508021,  0.00391774,  0.00936898,  0.00790053,  0.        ,
        0.00377371,  0.        ])
2025-07-15 23:24:27 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:27 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:27 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:27 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:27 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:27 - shap - INFO - phi = array([ 0.02694924, -0.26804601,  0.14827144,  0.01490185,  0.06460806,
       -0.25657455, -0.02375476, -0.01997647, -0.00838525,  0.        ,
        0.        , -0.02092307])
2025-07-15 23:24:27 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:27 - shap - INFO - phi = array([-0.02694924,  0.26804601, -0.14827144, -0.01490185, -0.06460806,
        0.25657455,  0.02375476,  0.01997647,  0.00838525,  0.        ,
        0.        ,  0.02092307])
2025-07-15 23:24:27 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:27 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:27 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:28 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:28 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:28 - shap - INFO - phi = array([-0.08718225, -0.27983204,  0.02517004,  0.02935757,  0.01639555,
        0.23392025,  0.09880996,  0.07182761, -0.00930124,  0.        ,
       -0.00778651,  0.        ])
2025-07-15 23:24:28 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:28 - shap - INFO - phi = array([ 0.08718225,  0.27983204, -0.02517004, -0.02935757, -0.01639555,
       -0.23392025, -0.09880996, -0.07182761,  0.00930124,  0.        ,
        0.00778651,  0.        ])
2025-07-15 23:24:28 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:28 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:28 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:28 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:29 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:29 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:29 - shap - INFO - phi = array([ 0.0706544 , -0.07592945,  0.02516622,  0.02407046, -0.29059628,
       -0.19990673,  0.08968365,  0.        ,  0.02019418,  0.01181293,
       -0.03027479,  0.        ])
2025-07-15 23:24:29 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:29 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:29 - shap - INFO - phi = array([-0.0706544 ,  0.07592945, -0.02516622, -0.02407046,  0.29059628,
        0.19990673, -0.08968365,  0.        , -0.02019418, -0.01181293,
        0.03027479,  0.        ])
2025-07-15 23:24:29 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:29 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:29 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:29 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:29 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:29 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:29 - shap - INFO - phi = array([ 0.26888511, -0.26994294, -0.1049539 ,  0.        ,  0.12246985,
        0.07575457,  0.18025648,  0.        , -0.02689692,  0.021108  ,
       -0.00785831, -0.00837599])
2025-07-15 23:24:29 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:29 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:29 - shap - INFO - phi = array([-0.26888511,  0.26994294,  0.1049539 ,  0.        , -0.12246985,
       -0.07575457, -0.18025648,  0.        ,  0.02689692, -0.021108  ,
        0.00785831,  0.00837599])
2025-07-15 23:24:29 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:29 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:29 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:29 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:30 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:30 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:30 - shap - INFO - phi = array([ 0.00291188, -0.10754375,  0.03492966, -0.01671351, -0.1726265 ,
       -0.14081331, -0.04834229,  0.        ,  0.00639103,  0.        ,
        0.00507731, -0.00264039])
2025-07-15 23:24:30 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:30 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:30 - shap - INFO - phi = array([-0.00291188,  0.10754375, -0.03492966,  0.01671351,  0.1726265 ,
        0.14081331,  0.04834229,  0.        , -0.00639103,  0.        ,
       -0.00507731,  0.00264039])
2025-07-15 23:24:30 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:30 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:30 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:30 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:30 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:30 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:30 - shap - INFO - phi = array([-0.14187397, -0.308661  , -0.01203567,  0.01243361, -0.14664945,
        0.10786489,  0.06586686,  0.        , -0.0117169 , -0.00329535,
        0.01113052,  0.        ])
2025-07-15 23:24:30 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:30 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:30 - shap - INFO - phi = array([ 0.14187397,  0.308661  ,  0.01203567, -0.01243361,  0.14664945,
       -0.10786489, -0.06586686,  0.        ,  0.0117169 ,  0.00329535,
       -0.01113052,  0.        ])
2025-07-15 23:24:30 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:30 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:30 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:30 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:31 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:31 - shap - INFO - phi = array([ 0.0792064 ,  0.11561384, -0.02031677,  0.08664131, -0.10940585,
        0.04806113, -0.12529231,  0.        ,  0.        ,  0.00813458,
       -0.00514009,  0.00780576])
2025-07-15 23:24:31 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:31 - shap - INFO - phi = array([-0.0792064 , -0.11561384,  0.02031677, -0.08664131,  0.10940585,
       -0.04806113,  0.12529231,  0.        ,  0.        , -0.00813458,
        0.00514009, -0.00780576])
2025-07-15 23:24:31 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:31 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:31 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:31 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:32 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:32 - shap - INFO - phi = array([ 0.0995894 , -0.2699576 , -0.0336292 , -0.05591704,  0.30208603,
        0.18409061,  0.03934794, -0.01849344,  0.        , -0.01142722,
        0.        ,  0.02024023])
2025-07-15 23:24:32 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:32 - shap - INFO - phi = array([-0.0995894 ,  0.2699576 ,  0.0336292 ,  0.05591704, -0.30208603,
       -0.18409061, -0.03934794,  0.01849344,  0.        ,  0.01142722,
        0.        , -0.02024023])
2025-07-15 23:24:32 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:32 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:32 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:32 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:32 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:32 - shap - INFO - phi = array([-0.03831669, -0.19750933, -0.00387074,  0.        , -0.12353206,
       -0.06786981, -0.01106178, -0.00473276, -0.00296676,  0.        ,
       -0.00311326,  0.00576101])
2025-07-15 23:24:32 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:32 - shap - INFO - phi = array([ 0.03831669,  0.19750933,  0.00387074,  0.        ,  0.12353206,
        0.06786981,  0.01106178,  0.00473276,  0.00296676,  0.        ,
        0.00311326, -0.00576101])
2025-07-15 23:24:32 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:32 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:32 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:32 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:33 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:33 - shap - INFO - phi = array([ 0.00690662, -0.02754545,  0.18735682,  0.        , -0.11331396,
        0.19282924,  0.05844703, -0.02679663,  0.01347027,  0.        ,
        0.00786652, -0.03007238])
2025-07-15 23:24:33 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:33 - shap - INFO - phi = array([-0.00690662,  0.02754545, -0.18735682,  0.        ,  0.11331396,
       -0.19282924, -0.05844703,  0.02679663, -0.01347027,  0.        ,
       -0.00786652,  0.03007238])
2025-07-15 23:24:33 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:33 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:33 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:33 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:33 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:33 - shap - INFO - phi = array([ 0.        ,  0.52922397, -0.07215641,  0.00295865, -0.12224732,
        0.02350277,  0.02324502,  0.        ,  0.01438321,  0.0025634 ,
        0.00207141,  0.01342958])
2025-07-15 23:24:33 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:33 - shap - INFO - phi = array([ 0.        , -0.52922397,  0.07215641, -0.00295865,  0.12224732,
       -0.02350277, -0.02324502,  0.        , -0.01438321, -0.0025634 ,
       -0.00207141, -0.01342958])
2025-07-15 23:24:33 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:33 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:33 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:33 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:34 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:34 - shap - INFO - phi = array([-0.08721726, -0.00797121,  0.06050216,  0.03296838,  0.01759124,
       -0.13739692, -0.23176688,  0.        ,  0.        , -0.00667726,
        0.00330738, -0.00872682])
2025-07-15 23:24:34 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:34 - shap - INFO - phi = array([ 0.08721726,  0.00797121, -0.06050216, -0.03296838, -0.01759124,
        0.13739692,  0.23176688,  0.        ,  0.        ,  0.00667726,
       -0.00330738,  0.00872682])
2025-07-15 23:24:34 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:34 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:34 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:34 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:35 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:35 - shap - INFO - phi = array([-0.19173479,  0.19222555,  0.07396418,  0.01564001,  0.12450729,
       -0.05927969,  0.10076541, -0.02077529,  0.        , -0.00904222,
        0.        , -0.01706362])
2025-07-15 23:24:35 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:35 - shap - INFO - phi = array([ 0.19173479, -0.19222555, -0.07396418, -0.01564001, -0.12450729,
        0.05927969, -0.10076541,  0.02077529,  0.        ,  0.00904222,
        0.        ,  0.01706362])
2025-07-15 23:24:35 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:35 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:35 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:35 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:35 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:35 - shap - INFO - phi = array([ 0.11445435,  0.32937379, -0.12683764,  0.00745444,  0.276781  ,
       -0.29471327, -0.02814492,  0.03085992, -0.00797736, -0.01426815,
        0.        ,  0.        ])
2025-07-15 23:24:35 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:35 - shap - INFO - phi = array([-0.11445435, -0.32937379,  0.12683764, -0.00745444, -0.276781  ,
        0.29471327,  0.02814492, -0.03085992,  0.00797736,  0.01426815,
        0.        ,  0.        ])
2025-07-15 23:24:35 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:35 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:35 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:35 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:36 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:36 - shap - INFO - phi = array([ 0.13690858, -0.19845001, -0.02684273, -0.01973548, -0.24475733,
        0.00892071,  0.03640124,  0.02646535,  0.02128069,  0.        ,
       -0.00986246,  0.        ])
2025-07-15 23:24:36 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:36 - shap - INFO - phi = array([-0.13690858,  0.19845001,  0.02684273,  0.01973548,  0.24475733,
       -0.00892071, -0.03640124, -0.02646535, -0.02128069,  0.        ,
        0.00986246,  0.        ])
2025-07-15 23:24:36 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:36 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:36 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:36 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:36 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:36 - shap - INFO - phi = array([-0.11840445, -0.33311997,  0.02857039, -0.0038913 , -0.08273345,
        0.05500496,  0.02258497,  0.01062386, -0.00576328,  0.        ,
       -0.00386879,  0.        ])
2025-07-15 23:24:36 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:36 - shap - INFO - phi = array([ 0.11840445,  0.33311997, -0.02857039,  0.0038913 ,  0.08273345,
       -0.05500496, -0.02258497, -0.01062386,  0.00576328,  0.        ,
        0.00386879,  0.        ])
2025-07-15 23:24:36 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:36 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:36 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:36 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:37 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:37 - shap - INFO - phi = array([-0.0019293 , -0.20784581, -0.09977124,  0.        , -0.03960977,
       -0.06026865, -0.00550622, -0.01715475, -0.00174669, -0.00290149,
       -0.00351338,  0.        ])
2025-07-15 23:24:37 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:37 - shap - INFO - phi = array([0.0019293 , 0.20784581, 0.09977124, 0.        , 0.03960977,
       0.06026865, 0.00550622, 0.01715475, 0.00174669, 0.00290149,
       0.00351338, 0.        ])
2025-07-15 23:24:37 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:37 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:37 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:37 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:37 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:37 - shap - INFO - phi = array([-0.01555029, -0.19617189, -0.09691992,  0.00766267, -0.10212262,
        0.01422155, -0.03835224,  0.05251845,  0.00449253,  0.        ,
        0.        , -0.00462054])
2025-07-15 23:24:37 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:37 - shap - INFO - phi = array([ 0.01555029,  0.19617189,  0.09691992, -0.00766267,  0.10212262,
       -0.01422155,  0.03835224, -0.05251845, -0.00449253,  0.        ,
        0.        ,  0.00462054])
2025-07-15 23:24:37 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:37 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:37 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:38 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:38 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:38 - shap - INFO - phi = array([ 0.08618587,  0.01089018, -0.08110116,  0.00504993,  0.21224231,
       -0.06924603,  0.10717091,  0.06696825,  0.        ,  0.00822527,
       -0.01437767,  0.        ])
2025-07-15 23:24:38 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:38 - shap - INFO - phi = array([-0.08618587, -0.01089018,  0.08110116, -0.00504993, -0.21224231,
        0.06924603, -0.10717091, -0.06696825,  0.        , -0.00822527,
        0.01437767,  0.        ])
2025-07-15 23:24:38 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:38 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:38 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:38 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:39 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:39 - shap - INFO - phi = array([-0.02872903, -0.28468207, -0.01070374, -0.004413  , -0.02020195,
       -0.06074818, -0.04371623, -0.00958116,  0.        ,  0.        ,
       -0.00284527,  0.02190413])
2025-07-15 23:24:39 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:39 - shap - INFO - phi = array([ 0.02872903,  0.28468207,  0.01070374,  0.004413  ,  0.02020195,
        0.06074818,  0.04371623,  0.00958116,  0.        ,  0.        ,
        0.00284527, -0.02190413])
2025-07-15 23:24:39 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:39 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:39 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:39 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:39 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:39 - shap - INFO - phi = array([ 0.        ,  0.16357392,  0.09075147,  0.00849471,  0.16868347,
       -0.02465147,  0.06724432, -0.00575582, -0.01034646,  0.        ,
        0.01846459,  0.00365479])
2025-07-15 23:24:39 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:39 - shap - INFO - phi = array([ 0.        , -0.16357392, -0.09075147, -0.00849471, -0.16868347,
        0.02465147, -0.06724432,  0.00575582,  0.01034646,  0.        ,
       -0.01846459, -0.00365479])
2025-07-15 23:24:39 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:39 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:39 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:39 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:40 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:40 - shap - INFO - phi = array([-0.03009201,  0.14121419,  0.10350062, -0.02863919, -0.1454318 ,
        0.04932738, -0.15306652,  0.03783688, -0.01872118,  0.        ,
        0.        ,  0.02343151])
2025-07-15 23:24:40 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:40 - shap - INFO - phi = array([ 0.03009201, -0.14121419, -0.10350062,  0.02863919,  0.1454318 ,
       -0.04932738,  0.15306652, -0.03783688,  0.01872118,  0.        ,
        0.        , -0.02343151])
2025-07-15 23:24:40 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:40 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:40 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:40 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:40 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:40 - shap - INFO - phi = array([ 0.03199769,  0.16750714,  0.06820232,  0.00836213,  0.07702984,
        0.14497887, -0.04329692, -0.00794685,  0.02130121,  0.        ,
        0.00817887,  0.        ])
2025-07-15 23:24:40 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:40 - shap - INFO - phi = array([-0.03199769, -0.16750714, -0.06820232, -0.00836213, -0.07702984,
       -0.14497887,  0.04329692,  0.00794685, -0.02130121,  0.        ,
       -0.00817887,  0.        ])
2025-07-15 23:24:40 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:40 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:40 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:40 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:41 - shap - INFO - np.sum(w_aug) = 11.999999999999996
2025-07-15 23:24:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:41 - shap - INFO - phi = array([ 0.26207731,  0.0859072 , -0.06669559, -0.03552382, -0.01393088,
        0.2368267 , -0.08312107, -0.03241837,  0.01063899, -0.015665  ,
        0.        ,  0.        ])
2025-07-15 23:24:41 - shap - INFO - np.sum(w_aug) = 11.999999999999996
2025-07-15 23:24:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:41 - shap - INFO - phi = array([-0.26207731, -0.0859072 ,  0.06669559,  0.03552382,  0.01393088,
       -0.2368267 ,  0.08312107,  0.03241837, -0.01063899,  0.015665  ,
        0.        ,  0.        ])
2025-07-15 23:24:41 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:41 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:41 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:41 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:42 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:42 - shap - INFO - phi = array([-0.05092647,  0.2062925 ,  0.06059553,  0.01956999, -0.04460586,
        0.2203427 ,  0.06589122, -0.01293098,  0.01682819,  0.        ,
       -0.00586365,  0.        ])
2025-07-15 23:24:42 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:42 - shap - INFO - phi = array([ 0.05092647, -0.2062925 , -0.06059553, -0.01956999,  0.04460586,
       -0.2203427 , -0.06589122,  0.01293098, -0.01682819,  0.        ,
        0.00586365,  0.        ])
2025-07-15 23:24:42 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:42 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:42 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:42 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:42 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:42 - shap - INFO - phi = array([-0.08781629,  0.02406177, -0.10974611, -0.05328725,  0.11456561,
        0.28258728,  0.0614688 , -0.01860097,  0.        ,  0.        ,
        0.06460041, -0.00333103])
2025-07-15 23:24:42 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:42 - shap - INFO - phi = array([ 0.08781629, -0.02406177,  0.10974611,  0.05328725, -0.11456561,
       -0.28258728, -0.0614688 ,  0.01860097,  0.        ,  0.        ,
       -0.06460041,  0.00333103])
2025-07-15 23:24:42 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:42 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:42 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:42 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:43 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:43 - shap - INFO - phi = array([ 0.12822005,  0.01473993,  0.14213756,  0.02955906, -0.10052063,
        0.0777025 ,  0.15446073, -0.01842485,  0.00617646,  0.        ,
        0.        , -0.00758726])
2025-07-15 23:24:43 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:43 - shap - INFO - phi = array([-0.12822005, -0.01473993, -0.14213756, -0.02955906,  0.10052063,
       -0.0777025 , -0.15446073,  0.01842485, -0.00617646,  0.        ,
        0.        ,  0.00758726])
2025-07-15 23:24:43 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:43 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:43 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:43 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:43 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:43 - shap - INFO - phi = array([-0.01809379,  0.15131031, -0.02235415,  0.0161677 ,  0.16337578,
        0.13226055,  0.06004905, -0.00194339,  0.00199198, -0.00390543,
        0.        ,  0.        ])
2025-07-15 23:24:43 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:43 - shap - INFO - phi = array([ 0.01809379, -0.15131031,  0.02235415, -0.0161677 , -0.16337578,
       -0.13226055, -0.06004905,  0.00194339, -0.00199198,  0.00390543,
        0.        ,  0.        ])
2025-07-15 23:24:43 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:43 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:43 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:43 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:44 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:44 - shap - INFO - phi = array([ 0.04633088,  0.03024656, -0.01371347, -0.00621405,  0.32046227,
       -0.02273329,  0.10929779,  0.00242406, -0.00492446,  0.00793904,
        0.        ,  0.        ])
2025-07-15 23:24:44 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:44 - shap - INFO - phi = array([-0.04633088, -0.03024656,  0.01371347,  0.00621405, -0.32046227,
        0.02273329, -0.10929779, -0.00242406,  0.00492446, -0.00793904,
        0.        ,  0.        ])
2025-07-15 23:24:44 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:44 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:44 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:44 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:45 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:45 - shap - INFO - phi = array([-0.01380109,  0.50578689, -0.07560973,  0.01070417, -0.00950126,
       -0.02411625,  0.01669592,  0.        ,  0.        ,  0.00789016,
       -0.00624454, -0.00614597])
2025-07-15 23:24:45 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:45 - shap - INFO - phi = array([ 0.01380109, -0.50578689,  0.07560973, -0.01070417,  0.00950126,
        0.02411625, -0.01669592,  0.        ,  0.        , -0.00789016,
        0.00624454,  0.00614597])
2025-07-15 23:24:45 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:45 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:45 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:45 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:45 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:45 - shap - INFO - phi = array([-0.10823128,  0.13744216, -0.03844589, -0.01405637,  0.07567425,
       -0.40383813,  0.18042714, -0.01267091, -0.00603619,  0.        ,
        0.        , -0.00589777])
2025-07-15 23:24:45 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:45 - shap - INFO - phi = array([ 0.10823128, -0.13744216,  0.03844589,  0.01405637, -0.07567425,
        0.40383813, -0.18042714,  0.01267091,  0.00603619,  0.        ,
        0.        ,  0.00589777])
2025-07-15 23:24:45 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:45 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:45 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:45 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:46 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:46 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:46 - shap - INFO - phi = array([-0.00515193, -0.04675948,  0.05784259, -0.01146774,  0.35662974,
        0.14266763, -0.01240321, -0.01065428, -0.00717157,  0.        ,
        0.        ,  0.00283855])
2025-07-15 23:24:46 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:46 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:46 - shap - INFO - phi = array([ 0.00515193,  0.04675948, -0.05784259,  0.01146774, -0.35662974,
       -0.14266763,  0.01240321,  0.01065428,  0.00717157,  0.        ,
        0.        , -0.00283855])
2025-07-15 23:24:46 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:46 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:46 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:46 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:46 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:46 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:46 - shap - INFO - phi = array([-0.2542418 , -0.06240903, -0.08840358, -0.07291679,  0.0051188 ,
        0.09775427,  0.01363506,  0.01820675,  0.01743719,  0.        ,
        0.        , -0.00984285])
2025-07-15 23:24:46 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:46 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:46 - shap - INFO - phi = array([ 0.2542418 ,  0.06240903,  0.08840358,  0.07291679, -0.0051188 ,
       -0.09775427, -0.01363506, -0.01820675, -0.01743719,  0.        ,
        0.        ,  0.00984285])
2025-07-15 23:24:46 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:46 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:46 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:46 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:47 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:47 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:47 - shap - INFO - phi = array([-0.15227742,  0.13443142, -0.05798533, -0.06073389, -0.15747632,
        0.08533134, -0.06796087,  0.        , -0.01055375, -0.00351979,
        0.        , -0.00790876])
2025-07-15 23:24:47 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:47 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:47 - shap - INFO - phi = array([ 0.15227742, -0.13443142,  0.05798533,  0.06073389,  0.15747632,
       -0.08533134,  0.06796087,  0.        ,  0.01055375,  0.00351979,
        0.        ,  0.00790876])
2025-07-15 23:24:47 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:47 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:47 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:47 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:48 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:48 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:48 - shap - INFO - phi = array([ 0.03881633,  0.17395088,  0.06624216,  0.        ,  0.1276076 ,
        0.14429666, -0.08776501, -0.00885387,  0.01992267, -0.00926429,
        0.        ,  0.01038843])
2025-07-15 23:24:48 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:48 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:48 - shap - INFO - phi = array([-0.03881633, -0.17395088, -0.06624216,  0.        , -0.1276076 ,
       -0.14429666,  0.08776501,  0.00885387, -0.01992267,  0.00926429,
        0.        , -0.01038843])
2025-07-15 23:24:48 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:48 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:48 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:48 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:48 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:48 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:48 - shap - INFO - phi = array([-0.05632699, -0.06045463,  0.04379391, -0.00433684, -0.07509156,
       -0.26707554,  0.00749743, -0.00820459,  0.        , -0.00301892,
       -0.00804867,  0.        ])
2025-07-15 23:24:48 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:48 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:48 - shap - INFO - phi = array([ 0.05632699,  0.06045463, -0.04379391,  0.00433684,  0.07509156,
        0.26707554, -0.00749743,  0.00820459,  0.        ,  0.00301892,
        0.00804867,  0.        ])
2025-07-15 23:24:48 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:48 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:48 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:48 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:49 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:49 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:49 - shap - INFO - phi = array([ 0.12628048,  0.17018531,  0.08584397,  0.        ,  0.09253378,
        0.05062769, -0.04264806, -0.00673865,  0.00382066, -0.01314771,
        0.        ,  0.00986669])
2025-07-15 23:24:49 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:49 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:49 - shap - INFO - phi = array([-0.12628048, -0.17018531, -0.08584397,  0.        , -0.09253378,
       -0.05062769,  0.04264806,  0.00673865, -0.00382066,  0.01314771,
        0.        , -0.00986669])
2025-07-15 23:24:49 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:49 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:49 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:49 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:49 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:49 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:49 - shap - INFO - phi = array([-0.04527996, -0.22646778,  0.00792051,  0.01901906,  0.08201313,
        0.16645918, -0.30764116, -0.0190187 ,  0.        , -0.03088221,
        0.        , -0.00760979])
2025-07-15 23:24:49 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:49 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:49 - shap - INFO - phi = array([ 0.04527996,  0.22646778, -0.00792051, -0.01901906, -0.08201313,
       -0.16645918,  0.30764116,  0.0190187 ,  0.        ,  0.03088221,
        0.        ,  0.00760979])
2025-07-15 23:24:49 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:49 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:49 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:49 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:50 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:50 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:50 - shap - INFO - phi = array([ 0.11597101, -0.11763754, -0.05708863,  0.        , -0.38855363,
       -0.13372809,  0.16661204, -0.01247455, -0.01618596,  0.01735824,
        0.01888804,  0.        ])
2025-07-15 23:24:50 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:50 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:50 - shap - INFO - phi = array([-0.11597101,  0.11763754,  0.05708863,  0.        ,  0.38855363,
        0.13372809, -0.16661204,  0.01247455,  0.01618596, -0.01735824,
       -0.01888804,  0.        ])
2025-07-15 23:24:50 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:50 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:50 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:50 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:51 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:51 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:51 - shap - INFO - phi = array([ 0.16992943, -0.16695637,  0.04350094, -0.02260858,  0.10763939,
       -0.24984141,  0.00824581,  0.00811487,  0.02010134,  0.        ,
        0.00766111,  0.        ])
2025-07-15 23:24:51 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:51 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:51 - shap - INFO - phi = array([-0.16992943,  0.16695637, -0.04350094,  0.02260858, -0.10763939,
        0.24984141, -0.00824581, -0.00811487, -0.02010134,  0.        ,
       -0.00766111,  0.        ])
2025-07-15 23:24:51 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:51 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:51 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:51 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:51 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:51 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:51 - shap - INFO - phi = array([-0.08095411,  0.12808704,  0.03388382,  0.00633241,  0.20592744,
        0.15236563,  0.03466879, -0.01375274, -0.00548116,  0.00387702,
        0.        ,  0.        ])
2025-07-15 23:24:51 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:51 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:51 - shap - INFO - phi = array([ 0.08095411, -0.12808704, -0.03388382, -0.00633241, -0.20592744,
       -0.15236563, -0.03466879,  0.01375274,  0.00548116, -0.00387702,
        0.        ,  0.        ])
2025-07-15 23:24:51 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:51 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:51 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:51 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:52 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:52 - shap - INFO - phi = array([-0.02807652, -0.1588911 , -0.06907859, -0.01738128, -0.12893128,
       -0.00501869, -0.0161683 , -0.00422759,  0.        ,  0.        ,
       -0.0050323 , -0.00430844])
2025-07-15 23:24:52 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:52 - shap - INFO - phi = array([0.02807652, 0.1588911 , 0.06907859, 0.01738128, 0.12893128,
       0.00501869, 0.0161683 , 0.00422759, 0.        , 0.        ,
       0.0050323 , 0.00430844])
2025-07-15 23:24:52 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:52 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:52 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:52 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:52 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:52 - shap - INFO - phi = array([ 0.        , -0.08523989,  0.0204346 ,  0.00311632, -0.39830726,
        0.01651846,  0.02315892,  0.        , -0.01151185,  0.00330719,
       -0.00576241,  0.01150747])
2025-07-15 23:24:52 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:52 - shap - INFO - phi = array([ 0.        ,  0.08523989, -0.0204346 , -0.00311632,  0.39830726,
       -0.01651846, -0.02315892,  0.        ,  0.01151185, -0.00330719,
        0.00576241, -0.01150747])
2025-07-15 23:24:52 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:52 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:52 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:52 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:53 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:53 - shap - INFO - phi = array([-0.07597501,  0.36264557,  0.0236965 ,  0.02389328,  0.25246525,
       -0.09618382, -0.03224329,  0.        , -0.01004462,  0.        ,
       -0.01235833,  0.01741011])
2025-07-15 23:24:53 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:53 - shap - INFO - phi = array([ 0.07597501, -0.36264557, -0.0236965 , -0.02389328, -0.25246525,
        0.09618382,  0.03224329,  0.        ,  0.01004462,  0.        ,
        0.01235833, -0.01741011])
2025-07-15 23:24:53 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:53 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:53 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:53 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:54 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:54 - shap - INFO - phi = array([-0.01833393,  0.38925272, -0.05604188,  0.01759238,  0.03188882,
        0.10206878,  0.01065921, -0.00645635, -0.01393075,  0.00392229,
        0.        ,  0.        ])
2025-07-15 23:24:54 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:54 - shap - INFO - phi = array([ 0.01833393, -0.38925272,  0.05604188, -0.01759238, -0.03188882,
       -0.10206878, -0.01065921,  0.00645635,  0.01393075, -0.00392229,
        0.        ,  0.        ])
2025-07-15 23:24:54 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:54 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:54 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:54 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:54 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:54 - shap - INFO - phi = array([ 0.10060117,  0.15092742, -0.09059052, -0.01686491,  0.16404181,
        0.15447778, -0.00454694,  0.        , -0.01390197,  0.00401836,
       -0.00485113,  0.        ])
2025-07-15 23:24:54 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:54 - shap - INFO - phi = array([-0.10060117, -0.15092742,  0.09059052,  0.01686491, -0.16404181,
       -0.15447778,  0.00454694,  0.        ,  0.01390197, -0.00401836,
        0.00485113,  0.        ])
2025-07-15 23:24:54 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:54 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:54 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:54 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:55 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:55 - shap - INFO - phi = array([0.02577217, 0.21615499, 0.13267522, 0.        , 0.00510665,
       0.06743729, 0.00629492, 0.00946579, 0.00332209, 0.00061529,
       0.00405486, 0.        ])
2025-07-15 23:24:55 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000007
2025-07-15 23:24:55 - shap - INFO - phi = array([-0.02577217, -0.21615499, -0.13267522,  0.        , -0.00510665,
       -0.06743729, -0.00629492, -0.00946579, -0.00332209, -0.00061529,
       -0.00405486,  0.        ])
2025-07-15 23:24:55 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:55 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:55 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:55 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:55 - shap - INFO - np.sum(w_aug) = 11.999999999999996
2025-07-15 23:24:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:55 - shap - INFO - phi = array([-0.0832231 ,  0.04783332, -0.16958307,  0.        , -0.10471697,
        0.005135  , -0.11770659, -0.00859309, -0.01120105, -0.01647546,
        0.02089397,  0.        ])
2025-07-15 23:24:55 - shap - INFO - np.sum(w_aug) = 11.999999999999996
2025-07-15 23:24:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:55 - shap - INFO - phi = array([ 0.0832231 , -0.04783332,  0.16958307,  0.        ,  0.10471697,
       -0.005135  ,  0.11770659,  0.00859309,  0.01120105,  0.01647546,
       -0.02089397,  0.        ])
2025-07-15 23:24:55 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:55 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:55 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:55 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:56 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:56 - shap - INFO - phi = array([-0.09135406, -0.10673691, -0.04540683, -0.00651539, -0.13861706,
       -0.07003041, -0.02027258, -0.00152765,  0.        , -0.00218378,
        0.0413443 ,  0.        ])
2025-07-15 23:24:56 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:56 - shap - INFO - phi = array([ 0.09135406,  0.10673691,  0.04540683,  0.00651539,  0.13861706,
        0.07003041,  0.02027258,  0.00152765,  0.        ,  0.00218378,
       -0.0413443 ,  0.        ])
2025-07-15 23:24:56 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:56 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:56 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:56 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:57 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:57 - shap - INFO - phi = array([-0.04807017, -0.08969091,  0.05253142, -0.01041147, -0.1590365 ,
       -0.10710126, -0.07260812, -0.00449157,  0.        , -0.00489489,
        0.        ,  0.00966452])
2025-07-15 23:24:57 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:57 - shap - INFO - phi = array([ 0.04807017,  0.08969091, -0.05253142,  0.01041147,  0.1590365 ,
        0.10710126,  0.07260812,  0.00449157,  0.        ,  0.00489489,
        0.        , -0.00966452])
2025-07-15 23:24:57 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:57 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:57 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:57 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:57 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:57 - shap - INFO - phi = array([ 0.24838877,  0.00430065,  0.01860258, -0.01569404, -0.11813211,
        0.14871455,  0.14130104, -0.02318708,  0.00423915, -0.00768917,
        0.        ,  0.        ])
2025-07-15 23:24:57 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:24:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:57 - shap - INFO - phi = array([-0.24838877, -0.00430065, -0.01860258,  0.01569404,  0.11813211,
       -0.14871455, -0.14130104,  0.02318708, -0.00423915,  0.00768917,
        0.        ,  0.        ])
2025-07-15 23:24:57 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:57 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:57 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:57 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:58 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:58 - shap - INFO - phi = array([ 0.08631946,  0.04136592,  0.07669368,  0.        , -0.54567216,
        0.        , -0.01196629, -0.00492741, -0.01174515, -0.00558691,
       -0.0077551 ,  0.01508205])
2025-07-15 23:24:58 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:58 - shap - INFO - phi = array([-0.08631946, -0.04136592, -0.07669368,  0.        ,  0.54567216,
        0.        ,  0.01196629,  0.00492741,  0.01174515,  0.00558691,
        0.0077551 , -0.01508205])
2025-07-15 23:24:58 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:58 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:58 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:58 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:58 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:58 - shap - INFO - phi = array([ 0.        ,  0.12277651, -0.16553522,  0.0123156 , -0.24156074,
       -0.128272  , -0.00686797, -0.00351066,  0.02388586,  0.        ,
       -0.00839978, -0.00735031])
2025-07-15 23:24:58 - shap - INFO - np.sum(w_aug) = 11.999999999999998
2025-07-15 23:24:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:58 - shap - INFO - phi = array([ 0.        , -0.12277651,  0.16553522, -0.0123156 ,  0.24156074,
        0.128272  ,  0.00686797,  0.00351066, -0.02388586,  0.        ,
        0.00839978,  0.00735031])
2025-07-15 23:24:58 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:58 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:58 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:59 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:24:59 - shap - INFO - np.sum(w_aug) = 11.999999999999996
2025-07-15 23:24:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:59 - shap - INFO - phi = array([-0.00925596,  0.24239395,  0.02926693,  0.01088745,  0.259101  ,
        0.02302193, -0.06478935, -0.00624503, -0.01165416,  0.        ,
        0.00456276,  0.        ])
2025-07-15 23:24:59 - shap - INFO - np.sum(w_aug) = 11.999999999999996
2025-07-15 23:24:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:24:59 - shap - INFO - phi = array([ 0.00925596, -0.24239395, -0.02926693, -0.01088745, -0.259101  ,
       -0.02302193,  0.06478935,  0.00624503,  0.01165416,  0.        ,
       -0.00456276,  0.        ])
2025-07-15 23:24:59 - shap - INFO - num_full_subsets = 3
2025-07-15 23:24:59 - shap - INFO - remaining_weight_vector = array([0.35673839, 0.32616082, 0.31710079])
2025-07-15 23:24:59 - shap - INFO - num_paired_subset_sizes = 5
2025-07-15 23:24:59 - shap - INFO - weight_left = 0.2929005745959313
2025-07-15 23:25:00 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:25:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:25:00 - shap - INFO - phi = array([-0.07941812,  0.05481637,  0.06406388,  0.        , -0.29144372,
       -0.0928712 , -0.09337891, -0.00565187,  0.0013623 ,  0.00194755,
        0.        ,  0.00325795])
2025-07-15 23:25:00 - shap - INFO - np.sum(w_aug) = 12.0
2025-07-15 23:25:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-15 23:25:00 - shap - INFO - phi = array([ 0.07941812, -0.05481637, -0.06406388,  0.        ,  0.29144372,
        0.0928712 ,  0.09337891,  0.00565187, -0.0013623 , -0.00194755,
        0.        , -0.00325795])
2025-07-15 23:25:04 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
