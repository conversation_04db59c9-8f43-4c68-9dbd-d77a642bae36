2025-07-16 01:07:30 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:30 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.        ,  0.        , -0.13341342, -0.04535598, -0.08019075,
       -0.01794196, -0.01786326, -0.10588739,  0.        ,  0.02137009,
        0.        ,  0.02761667,  0.        , -0.01232708, -0.12890069])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.13341342,  0.04535598,  0.08019075,
        0.01794196,  0.01786326,  0.10588739,  0.        , -0.02137009,
        0.        , -0.02761667,  0.        ,  0.01232708,  0.12890069])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.0475478 , -0.02283489, -0.30552065,  0.03121071, -0.09872224,
        0.05969607,  0.09181833,  0.        ,  0.        ,  0.        ,
       -0.15555427, -0.04648718,  0.        ,  0.        ,  0.09557273])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.0475478 ,  0.02283489,  0.30552065, -0.03121071,  0.09872224,
       -0.05969607, -0.09181833,  0.        ,  0.        ,  0.        ,
        0.15555427,  0.04648718,  0.        ,  0.        , -0.09557273])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.05324592,  0.        , -0.12283247, -0.05658596, -0.06269074,
        0.        , -0.03504045, -0.01298209,  0.        ,  0.        ,
        0.01488294, -0.1146738 ,  0.        , -0.01295047, -0.04527826])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.05324592,  0.        ,  0.12283247,  0.05658596,  0.06269074,
        0.        ,  0.03504045,  0.01298209,  0.        ,  0.        ,
       -0.01488294,  0.1146738 ,  0.        ,  0.01295047,  0.04527826])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.04769266, -0.01410328,  0.07072695,  0.        ,  0.25965836,
        0.03891553,  0.14098277, -0.02184249,  0.        , -0.0139773 ,
       -0.01469429,  0.        ,  0.        ,  0.        , -0.09773825])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.04769266,  0.01410328, -0.07072695,  0.        , -0.25965836,
       -0.03891553, -0.14098277,  0.02184249,  0.        ,  0.0139773 ,
        0.01469429,  0.        ,  0.        ,  0.        ,  0.09773825])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.02460764, -0.02047204, -0.06486552,  0.        ,  0.        ,
        0.14538464,  0.04320216, -0.14747444,  0.        ,  0.        ,
        0.11935673,  0.01665833,  0.        , -0.02191045, -0.06101902])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.02460764,  0.02047204,  0.06486552,  0.        ,  0.        ,
       -0.14538464, -0.04320216,  0.14747444,  0.        ,  0.        ,
       -0.11935673, -0.01665833,  0.        ,  0.02191045,  0.06101902])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([0.        , 0.        , 0.02205556, 0.02495772, 0.02790812,
       0.05449932, 0.06986999, 0.00705844, 0.        , 0.        ,
       0.0214594 , 0.17535951, 0.        , 0.01381556, 0.04907496])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.        ,  0.        , -0.02205556, -0.02495772, -0.02790812,
       -0.05449932, -0.06986999, -0.00705844,  0.        ,  0.        ,
       -0.0214594 , -0.17535951,  0.        , -0.01381556, -0.04907496])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.02257582,  0.        , -0.01616219, -0.06836388, -0.01658483,
       -0.03960529, -0.07338764, -0.02529303,  0.        ,  0.        ,
        0.        , -0.07975092,  0.        , -0.01181696, -0.14661898])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([0.02257582, 0.        , 0.01616219, 0.06836388, 0.01658483,
       0.03960529, 0.07338764, 0.02529303, 0.        , 0.        ,
       0.        , 0.07975092, 0.        , 0.01181696, 0.14661898])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.03094701,  0.        , -0.1324618 , -0.07602686,  0.01943003,
       -0.0383515 ,  0.        , -0.04575575,  0.        ,  0.        ,
        0.03189393, -0.08236478,  0.        , -0.0099051 , -0.09999831])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.03094701,  0.        ,  0.1324618 ,  0.07602686, -0.01943003,
        0.0383515 ,  0.        ,  0.04575575,  0.        ,  0.        ,
       -0.03189393,  0.08236478,  0.        ,  0.0099051 ,  0.09999831])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.11811309,  0.        ,  0.05868666,  0.        ,  0.06971599,
       -0.10503761, -0.03820682,  0.0824272 ,  0.        ,  0.        ,
       -0.15645033,  0.04909159,  0.        ,  0.05179708, -0.09218887])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.11811309,  0.        , -0.05868666,  0.        , -0.06971599,
        0.10503761,  0.03820682, -0.0824272 ,  0.        ,  0.        ,
        0.15645033, -0.04909159,  0.        , -0.05179708,  0.09218887])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.        ,  0.        , -0.1539925 , -0.03553305, -0.06533433,
       -0.02562007,  0.03791335, -0.12235267,  0.        ,  0.        ,
       -0.00829332,  0.04985472,  0.        , -0.02724485, -0.14664409])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.1539925 ,  0.03553305,  0.06533433,
        0.02562007, -0.03791335,  0.12235267,  0.        ,  0.        ,
        0.00829332, -0.04985472,  0.        ,  0.02724485,  0.14664409])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.06941265,  0.        , -0.04974385, -0.11105221, -0.09362333,
        0.05453449, -0.11369031,  0.        ,  0.        ,  0.02464797,
        0.01842248, -0.03577982,  0.        ,  0.        ,  0.08514855])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.06941265,  0.        ,  0.04974385,  0.11105221,  0.09362333,
       -0.05453449,  0.11369031,  0.        ,  0.        , -0.02464797,
       -0.01842248,  0.03577982,  0.        ,  0.        , -0.08514855])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.04309093,  0.        ,  0.        , -0.01735359, -0.03176876,
        0.02894139, -0.01407025,  0.08436738,  0.        ,  0.        ,
       -0.01441038,  0.11034934,  0.        ,  0.03372992,  0.14679085])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.04309093,  0.        ,  0.        ,  0.01735359,  0.03176876,
       -0.02894139,  0.01407025, -0.08436738,  0.        ,  0.        ,
        0.01441038, -0.11034934,  0.        , -0.03372992, -0.14679085])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.03281187,  0.        ,  0.13918072,  0.02988998,  0.10945543,
        0.        ,  0.03747259,  0.08499313,  0.        ,  0.        ,
       -0.08387301,  0.02465231,  0.        ,  0.00903901,  0.08435039])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.03281187,  0.        , -0.13918072, -0.02988998, -0.10945543,
        0.        , -0.03747259, -0.08499313,  0.        ,  0.        ,
        0.08387301, -0.02465231,  0.        , -0.00903901, -0.08435039])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.        ,  0.        , -0.09101954, -0.15029845,  0.16296642,
        0.09724395,  0.1536405 , -0.1038361 ,  0.        ,  0.        ,
        0.08232006,  0.1392044 ,  0.        , -0.04164484,  0.14299739])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.09101954,  0.15029845, -0.16296642,
       -0.09724395, -0.1536405 ,  0.1038361 ,  0.        ,  0.        ,
       -0.08232006, -0.1392044 ,  0.        ,  0.04164484, -0.14299739])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.0443307 , -0.00590329, -0.16506311, -0.03745342, -0.06030602,
        0.        ,  0.        , -0.03689934,  0.        ,  0.        ,
       -0.02505192, -0.05652427,  0.        , -0.00579051, -0.06424791])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([0.0443307 , 0.00590329, 0.16506311, 0.03745342, 0.06030602,
       0.        , 0.        , 0.03689934, 0.        , 0.        ,
       0.02505192, 0.05652427, 0.        , 0.00579051, 0.06424791])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.0686458 ,  0.00191987,  0.12914266,
        0.00858246,  0.08441539,  0.01681526,  0.        , -0.01024124,
        0.00541479,  0.02593656,  0.        ,  0.        ,  0.07389522])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.        ,  0.        , -0.0686458 , -0.00191987, -0.12914266,
       -0.00858246, -0.08441539, -0.01681526,  0.        ,  0.01024124,
       -0.00541479, -0.02593656,  0.        ,  0.        , -0.07389522])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.02021523,  0.        , -0.06040377,  0.06800595,  0.0178606 ,
        0.08398761,  0.13039318, -0.07661474,  0.        ,  0.        ,
        0.05112852,  0.21202839,  0.        ,  0.01493009,  0.        ])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.02021523,  0.        ,  0.06040377, -0.06800595, -0.0178606 ,
       -0.08398761, -0.13039318,  0.07661474,  0.        ,  0.        ,
       -0.05112852, -0.21202839,  0.        , -0.01493009,  0.        ])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.01512517, -0.01502572, -0.05919266,  0.        ,  0.08352789,
        0.07720472,  0.11427729, -0.10008871,  0.        ,  0.        ,
        0.05951741,  0.09552081,  0.        ,  0.        , -0.01528139])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.01512517,  0.01502572,  0.05919266,  0.        , -0.08352789,
       -0.07720472, -0.11427729,  0.10008871,  0.        ,  0.        ,
       -0.05951741, -0.09552081,  0.        ,  0.        ,  0.01528139])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.09573715,  0.        ,  0.2029167 ,  0.05319539,  0.01736412,
       -0.1102442 , -0.08017763,  0.09811213,  0.        ,  0.        ,
       -0.06750842,  0.08192445,  0.        ,  0.03407108,  0.        ])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.09573715,  0.        , -0.2029167 , -0.05319539, -0.01736412,
        0.1102442 ,  0.08017763, -0.09811213,  0.        ,  0.        ,
        0.06750842, -0.08192445,  0.        , -0.03407108,  0.        ])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.07239894,  0.        , -0.05095293, -0.03646055, -0.07890613,
        0.        , -0.06190429, -0.00934457,  0.        ,  0.        ,
        0.04449524, -0.11913123,  0.        , -0.01557037, -0.0761929 ])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.07239894,  0.        ,  0.05095293,  0.03646055,  0.07890613,
        0.        ,  0.06190429,  0.00934457,  0.        ,  0.        ,
       -0.04449524,  0.11913123,  0.        ,  0.01557037,  0.0761929 ])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([ 0.06332754,  0.        ,  0.27046964,  0.12857668,  0.        ,
       -0.09858349, -0.07862023,  0.15116686,  0.        ,  0.        ,
       -0.04647462, -0.06541296,  0.        ,  0.01969952,  0.0642179 ])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.06332754,  0.        , -0.27046964, -0.12857668,  0.        ,
        0.09858349,  0.07862023, -0.15116686,  0.        ,  0.        ,
        0.04647462,  0.06541296,  0.        , -0.01969952, -0.0642179 ])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.01142042, -0.00743781, -0.14689834, -0.0442756 , -0.05917669,
       -0.02863808,  0.        , -0.05951214,  0.        ,  0.        ,
       -0.035498  ,  0.        ,  0.        , -0.00899731, -0.0948329 ])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:31 - shap - INFO - phi = array([0.01142042, 0.00743781, 0.14689834, 0.0442756 , 0.05917669,
       0.02863808, 0.        , 0.05951214, 0.        , 0.        ,
       0.035498  , 0.        , 0.        , 0.00899731, 0.0948329 ])
2025-07-16 01:07:31 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:31 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:31 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:31 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:31 - shap - INFO - phi = array([-0.04343069,  0.        , -0.12229078,  0.03676057, -0.08453546,
        0.12362502,  0.07549571, -0.07922442,  0.        ,  0.        ,
        0.10677521,  0.1294546 ,  0.        ,  0.        ,  0.10479883])
2025-07-16 01:07:31 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.04343069,  0.        ,  0.12229078, -0.03676057,  0.08453546,
       -0.12362502, -0.07549571,  0.07922442,  0.        ,  0.        ,
       -0.10677521, -0.1294546 ,  0.        ,  0.        , -0.10479883])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.        ,  0.01427694,  0.17474112,  0.052907  ,  0.0406697 ,
       -0.09065055,  0.        ,  0.13425581,  0.        , -0.01091433,
       -0.03822693, -0.03655181,  0.        ,  0.        ,  0.22448742])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.        , -0.01427694, -0.17474112, -0.052907  , -0.0406697 ,
        0.09065055,  0.        , -0.13425581,  0.        ,  0.01091433,
        0.03822693,  0.03655181,  0.        ,  0.        , -0.22448742])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.10693831,  0.        ,  0.35055073,  0.16222667,  0.06560218,
       -0.20637571, -0.10124134,  0.21117963,  0.        ,  0.        ,
       -0.12710921, -0.20477408,  0.        ,  0.        ,  0.03474818])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.10693831,  0.        , -0.35055073, -0.16222667, -0.06560218,
        0.20637571,  0.10124134, -0.21117963,  0.        ,  0.        ,
        0.12710921,  0.20477408,  0.        ,  0.        , -0.03474818])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.01782441,  0.00485074,  0.07583318,  0.        ,  0.09771817,
        0.        ,  0.05866875,  0.04743529,  0.        , -0.01453588,
        0.01516215,  0.02291807,  0.        ,  0.        ,  0.11530926])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.01782441, -0.00485074, -0.07583318,  0.        , -0.09771817,
        0.        , -0.05866875, -0.04743529,  0.        ,  0.01453588,
       -0.01516215, -0.02291807,  0.        ,  0.        , -0.11530926])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.05881947,  0.        ,  0.03053216,  0.10410169,  0.02755337,
        0.        ,  0.07188407,  0.04896382,  0.        ,  0.        ,
       -0.06349827,  0.18825588,  0.        ,  0.05090009, -0.03556324])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.05881947,  0.        , -0.03053216, -0.10410169, -0.02755337,
        0.        , -0.07188407, -0.04896382,  0.        ,  0.        ,
        0.06349827, -0.18825588,  0.        , -0.05090009,  0.03556324])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.0301424 , -0.01759253, -0.10922798,  0.09343236, -0.03275971,
        0.        ,  0.09822412,  0.        ,  0.        , -0.02405615,
       -0.09156416, -0.12595057,  0.        ,  0.        , -0.04654579])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.0301424 ,  0.01759253,  0.10922798, -0.09343236,  0.03275971,
        0.        , -0.09822412,  0.        ,  0.        ,  0.02405615,
        0.09156416,  0.12595057,  0.        ,  0.        ,  0.04654579])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.01160099,  0.        , -0.03014402,  0.        ,  0.1349632 ,
        0.12786097,  0.15641668, -0.0352105 ,  0.        , -0.02650089,
        0.02461194,  0.14521364,  0.        ,  0.        , -0.01705234])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.01160099,  0.        ,  0.03014402,  0.        , -0.1349632 ,
       -0.12786097, -0.15641668,  0.0352105 ,  0.        ,  0.02650089,
       -0.02461194, -0.14521364,  0.        ,  0.        ,  0.01705234])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.02524022,  0.        , -0.01451537, -0.11310955,  0.13159451,
        0.05657084,  0.        , -0.03204143,  0.        ,  0.03447678,
       -0.06612931,  0.0386728 ,  0.        ,  0.        , -0.04972635])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.02524022,  0.        ,  0.01451537,  0.11310955, -0.13159451,
       -0.05657084,  0.        ,  0.03204143,  0.        , -0.03447678,
        0.06612931, -0.0386728 ,  0.        ,  0.        ,  0.04972635])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.10430393,  0.        ,  0.0431736 ,  0.03441559,  0.        ,
       -0.02890638, -0.03412276,  0.        ,  0.        ,  0.00493644,
       -0.05273363,  0.1460859 ,  0.        ,  0.04404482, -0.10410686])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.10430393,  0.        , -0.0431736 , -0.03441559,  0.        ,
        0.02890638,  0.03412276,  0.        ,  0.        , -0.00493644,
        0.05273363, -0.1460859 ,  0.        , -0.04404482,  0.10410686])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.03143493,  0.01133348,  0.16679754,  0.03163604, -0.01305584,
       -0.02111174, -0.04862261,  0.10774697,  0.        ,  0.        ,
        0.        , -0.02372363,  0.        ,  0.        ,  0.22927994])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.03143493, -0.01133348, -0.16679754, -0.03163604,  0.01305584,
        0.02111174,  0.04862261, -0.10774697,  0.        ,  0.        ,
        0.        ,  0.02372363,  0.        ,  0.        , -0.22927994])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.07155379,  0.        , -0.04747018,  0.04324736,  0.0919103 ,
        0.03999407,  0.08227815, -0.06745399,  0.        ,  0.        ,
        0.        ,  0.08354042,  0.        ,  0.03043799, -0.21794675])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.07155379,  0.        ,  0.04747018, -0.04324736, -0.0919103 ,
       -0.03999407, -0.08227815,  0.06745399,  0.        ,  0.        ,
        0.        , -0.08354042,  0.        , -0.03043799,  0.21794675])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.02362702,  0.        , -0.13556827, -0.06389283, -0.00879155,
       -0.03261387,  0.        , -0.02663477,  0.        ,  0.        ,
       -0.04253731, -0.08227312,  0.        , -0.01206021, -0.07171575])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([0.02362702, 0.        , 0.13556827, 0.06389283, 0.00879155,
       0.03261387, 0.        , 0.02663477, 0.        , 0.        ,
       0.04253731, 0.08227312, 0.        , 0.01206021, 0.07171575])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.04097522, -0.00893322,  0.        ,  0.03070658, -0.02361469,
        0.02103494, -0.03911227,  0.        ,  0.        ,  0.02313084,
        0.        , -0.0192935 ,  0.        ,  0.01963622,  0.10010138])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.04097522,  0.00893322,  0.        , -0.03070658,  0.02361469,
       -0.02103494,  0.03911227,  0.        ,  0.        , -0.02313084,
        0.        ,  0.0192935 ,  0.        , -0.01963622, -0.10010138])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.02887474,  0.        ,  0.04487262,  0.16263485,  0.01000601,
        0.05493305,  0.06533792,  0.        ,  0.        , -0.03283598,
        0.02166128, -0.01947472,  0.        ,  0.01640311,  0.        ])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.02887474,  0.        , -0.04487262, -0.16263485, -0.01000601,
       -0.05493305, -0.06533792,  0.        ,  0.        ,  0.03283598,
       -0.02166128,  0.01947472,  0.        , -0.01640311,  0.        ])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.0448424 ,  0.        , -0.01446312, -0.01387651, -0.02496117,
        0.10414297,  0.0363932 , -0.05067862,  0.        ,  0.        ,
        0.11588293,  0.14440743,  0.        ,  0.        ,  0.10445842])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.0448424 ,  0.        ,  0.01446312,  0.01387651,  0.02496117,
       -0.10414297, -0.0363932 ,  0.05067862,  0.        ,  0.        ,
       -0.11588293, -0.14440743,  0.        ,  0.        , -0.10445842])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.06727549,  0.        ,  0.09130691,  0.02971901,  0.05950128,
       -0.06630689,  0.04135113,  0.02754056,  0.        ,  0.        ,
        0.        ,  0.12683632,  0.        ,  0.01044462,  0.01041687])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.06727549,  0.        , -0.09130691, -0.02971901, -0.05950128,
        0.06630689, -0.04135113, -0.02754056,  0.        ,  0.        ,
        0.        , -0.12683632,  0.        , -0.01044462, -0.01041687])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.        , -0.00912026, -0.10394453, -0.03040911, -0.03061061,
       -0.04698168, -0.01788478, -0.04439712,  0.        ,  0.        ,
       -0.03637549, -0.04253634,  0.        ,  0.        , -0.08700562])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([0.        , 0.00912026, 0.10394453, 0.03040911, 0.03061061,
       0.04698168, 0.01788478, 0.04439712, 0.        , 0.        ,
       0.03637549, 0.04253634, 0.        , 0.        , 0.08700562])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.        ,  0.        , -0.08639539, -0.02694362, -0.05578357,
       -0.03345414, -0.00925018, -0.06101714,  0.        ,  0.        ,
       -0.02722022, -0.05012661,  0.        , -0.02539067, -0.12614272])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([0.        , 0.        , 0.08639539, 0.02694362, 0.05578357,
       0.03345414, 0.00925018, 0.06101714, 0.        , 0.        ,
       0.02722022, 0.05012661, 0.        , 0.02539067, 0.12614272])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.12173601,  0.        , -0.14448901, -0.02308119, -0.06444151,
        0.07480548,  0.        ,  0.06190171,  0.        ,  0.        ,
       -0.07139641, -0.13145142, -0.00796649,  0.        ,  0.03500208])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.12173601,  0.        ,  0.14448901,  0.02308119,  0.06444151,
       -0.07480548,  0.        , -0.06190171,  0.        ,  0.        ,
        0.07139641,  0.13145142,  0.00796649,  0.        , -0.03500208])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.03775842,  0.        ,  0.02742961,  0.11755956,  0.04370497,
        0.13993984,  0.09775188,  0.02876495,  0.        , -0.03225048,
        0.        ,  0.        ,  0.        ,  0.01901089,  0.03819953])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.03775842,  0.        , -0.02742961, -0.11755956, -0.04370497,
       -0.13993984, -0.09775188, -0.02876495,  0.        ,  0.03225048,
        0.        ,  0.        ,  0.        , -0.01901089, -0.03819953])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([-0.06300028,  0.        , -0.09873681, -0.04297508, -0.07440247,
        0.        , -0.01853319,  0.        ,  0.        , -0.00043978,
       -0.02288461, -0.11376614,  0.        , -0.02512884, -0.04143957])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:32 - shap - INFO - phi = array([0.06300028, 0.        , 0.09873681, 0.04297508, 0.07440247,
       0.        , 0.01853319, 0.        , 0.        , 0.00043978,
       0.02288461, 0.11376614, 0.        , 0.02512884, 0.04143957])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.        ,  0.        , -0.06199324, -0.05438759, -0.02931817,
       -0.05061793, -0.05381574, -0.07058687,  0.        ,  0.        ,
        0.03196896, -0.01925212,  0.        , -0.00726572, -0.14324859])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.06199324,  0.05438759,  0.02931817,
        0.05061793,  0.05381574,  0.07058687,  0.        ,  0.        ,
       -0.03196896,  0.01925212,  0.        ,  0.00726572,  0.14324859])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:32 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:32 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:32 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.04781504, -0.02945747,  0.18771437,
        0.03043929,  0.14197022, -0.0370872 ,  0.        ,  0.        ,
        0.05228269, -0.02972893,  0.        , -0.04449583,  0.09782991])
2025-07-16 01:07:32 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:32 - shap - INFO - phi = array([ 0.        ,  0.        , -0.04781504,  0.02945747, -0.18771437,
       -0.03043929, -0.14197022,  0.0370872 ,  0.        ,  0.        ,
       -0.05228269,  0.02972893,  0.        ,  0.04449583, -0.09782991])
2025-07-16 01:07:32 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.05233986,  0.        , -0.09590501, -0.06834794, -0.03731703,
       -0.00786465, -0.03726551, -0.00419399,  0.        ,  0.        ,
        0.        , -0.11801607,  0.        , -0.0112585 , -0.02603763])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([0.05233986, 0.        , 0.09590501, 0.06834794, 0.03731703,
       0.00786465, 0.03726551, 0.00419399, 0.        , 0.        ,
       0.        , 0.11801607, 0.        , 0.0112585 , 0.02603763])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.07585607,  0.        , -0.08837059, -0.06511443,  0.12016624,
        0.14601508,  0.16882058, -0.08257912,  0.        ,  0.        ,
        0.11709866,  0.10407719,  0.        ,  0.        ,  0.11941732])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.07585607,  0.        ,  0.08837059,  0.06511443, -0.12016624,
       -0.14601508, -0.16882058,  0.08257912,  0.        ,  0.        ,
       -0.11709866, -0.10407719,  0.        ,  0.        , -0.11941732])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.10530711,  0.01461008,  0.15255809,  0.07481793,  0.        ,
       -0.16939237, -0.04542453,  0.06674123,  0.        ,  0.        ,
       -0.02525882,  0.04341424,  0.        ,  0.01505718,  0.        ])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.10530711, -0.01461008, -0.15255809, -0.07481793,  0.        ,
        0.16939237,  0.04542453, -0.06674123,  0.        ,  0.        ,
        0.02525882, -0.04341424,  0.        , -0.01505718,  0.        ])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.0102513 ,  0.        , -0.02995912, -0.05384441,  0.02191114,
       -0.05312196,  0.        , -0.06545962,  0.        ,  0.        ,
       -0.01530315, -0.05738272,  0.        , -0.03969902, -0.14986165])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.0102513 ,  0.        ,  0.02995912,  0.05384441, -0.02191114,
        0.05312196,  0.        ,  0.06545962,  0.        ,  0.        ,
        0.01530315,  0.05738272,  0.        ,  0.03969902,  0.14986165])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.06268375,  0.        ,  0.11600978,  0.11892492,  0.        ,
       -0.05752954,  0.00498172,  0.05403395,  0.        , -0.02344592,
        0.        ,  0.08327511,  0.        ,  0.02672484,  0.02962989])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.06268375,  0.        , -0.11600978, -0.11892492,  0.        ,
        0.05752954, -0.00498172, -0.05403395,  0.        ,  0.02344592,
        0.        , -0.08327511,  0.        , -0.02672484, -0.02962989])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.01319805,  0.        , -0.06445227, -0.04680864,  0.        ,
       -0.05887327, -0.00926327, -0.02088707,  0.        ,  0.        ,
       -0.03745331, -0.09071039,  0.        , -0.01966636, -0.11731927])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([0.01319805, 0.        , 0.06445227, 0.04680864, 0.        ,
       0.05887327, 0.00926327, 0.02088707, 0.        , 0.        ,
       0.03745331, 0.09071039, 0.        , 0.01966636, 0.11731927])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.07019476,  0.        ,  0.21156891,  0.12093758,  0.        ,
       -0.13476967, -0.08223656,  0.14142527,  0.        ,  0.        ,
       -0.06504975, -0.05295558,  0.        ,  0.02978267,  0.01953485])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.07019476,  0.        , -0.21156891, -0.12093758,  0.        ,
        0.13476967,  0.08223656, -0.14142527,  0.        ,  0.        ,
        0.06504975,  0.05295558,  0.        , -0.02978267, -0.01953485])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.        ,  0.        , -0.0866739 , -0.05926291, -0.02448528,
       -0.02179633, -0.04575889, -0.0963555 ,  0.        ,  0.        ,
        0.13679798, -0.0984068 ,  0.        , -0.02508029, -0.10503111])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.0866739 ,  0.05926291,  0.02448528,
        0.02179633,  0.04575889,  0.0963555 ,  0.        ,  0.        ,
       -0.13679798,  0.0984068 ,  0.        ,  0.02508029,  0.10503111])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([0.00276796, 0.        , 0.02484736, 0.04510891, 0.04653065,
       0.01806629, 0.0769854 , 0.04547497, 0.        , 0.        ,
       0.        , 0.09863351, 0.        , 0.01843911, 0.08322513])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.00276796,  0.        , -0.02484736, -0.04510891, -0.04653065,
       -0.01806629, -0.0769854 , -0.04547497,  0.        ,  0.        ,
        0.        , -0.09863351,  0.        , -0.01843911, -0.08322513])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.05012197,  0.        , -0.17355933, -0.0201832 , -0.1044706 ,
        0.01201889,  0.01081956, -0.04796079,  0.        ,  0.        ,
        0.        , -0.05357338,  0.        , -0.01657742, -0.05569032])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.05012197,  0.        ,  0.17355933,  0.0201832 ,  0.1044706 ,
       -0.01201889, -0.01081956,  0.04796079,  0.        ,  0.        ,
        0.        ,  0.05357338,  0.        ,  0.01657742,  0.05569032])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.05381368, -0.10281344, -0.03249548,
       -0.031789  , -0.17415663, -0.0208443 ,  0.        ,  0.        ,
        0.07322274, -0.12448625,  0.        , -0.02861597, -0.09885344])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.        ,  0.        , -0.05381368,  0.10281344,  0.03249548,
        0.031789  ,  0.17415663,  0.0208443 ,  0.        ,  0.        ,
       -0.07322274,  0.12448625,  0.        ,  0.02861597,  0.09885344])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.01374758,  0.        , -0.06764273,  0.        , -0.05259702,
        0.05896026, -0.05097674,  0.01608865,  0.        ,  0.02511466,
        0.        , -0.07391961,  0.        ,  0.02690783,  0.10081625])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.01374758,  0.        ,  0.06764273,  0.        ,  0.05259702,
       -0.05896026,  0.05097674, -0.01608865,  0.        , -0.02511466,
        0.        ,  0.07391961,  0.        , -0.02690783, -0.10081625])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.0660526 ,  0.        ,  0.19558393,  0.02925689,  0.13514071,
       -0.12859663,  0.01362204,  0.14570525,  0.        ,  0.        ,
       -0.09992317, -0.03039789,  0.        ,  0.        ,  0.11844223])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.0660526 ,  0.        , -0.19558393, -0.02925689, -0.13514071,
        0.12859663, -0.01362204, -0.14570525,  0.        ,  0.        ,
        0.09992317,  0.03039789,  0.        ,  0.        , -0.11844223])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.07610563,  0.        , -0.05534627, -0.03073885, -0.0643592 ,
        0.06452693, -0.07564459,  0.01868185,  0.        ,  0.        ,
        0.        , -0.17655566,  0.        , -0.00537273, -0.01361705])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.07610563,  0.        ,  0.05534627,  0.03073885,  0.0643592 ,
       -0.06452693,  0.07564459, -0.01868185,  0.        ,  0.        ,
        0.        ,  0.17655566,  0.        ,  0.00537273,  0.01361705])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.06713542,  0.        , -0.06417707,  0.05088236,  0.10815542,
        0.02945681,  0.15948458,  0.        ,  0.        ,  0.        ,
       -0.06747908,  0.18266708,  0.        ,  0.04531868, -0.07016961])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.06713542,  0.        ,  0.06417707, -0.05088236, -0.10815542,
       -0.02945681, -0.15948458,  0.        ,  0.        ,  0.        ,
        0.06747908, -0.18266708,  0.        , -0.04531868,  0.07016961])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.02133137, -0.01665669, -0.02002109,  0.        ,  0.07078996,
        0.04327886,  0.        ,  0.03304303,  0.        ,  0.01853726,
       -0.03602418, -0.08619982,  0.        ,  0.02944288,  0.        ])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.02133137,  0.01665669,  0.02002109,  0.        , -0.07078996,
       -0.04327886,  0.        , -0.03304303,  0.        , -0.01853726,
        0.03602418,  0.08619982,  0.        , -0.02944288,  0.        ])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.03416399, -0.01514715,  0.14475437,  0.12160258, -0.08005296,
       -0.01282079, -0.09467411, -0.02041675,  0.        ,  0.        ,
        0.05248082, -0.10184699,  0.        ,  0.        ,  0.        ])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.03416399,  0.01514715, -0.14475437, -0.12160258,  0.08005296,
        0.01282079,  0.09467411,  0.02041675,  0.        ,  0.        ,
       -0.05248082,  0.10184699,  0.        ,  0.        ,  0.        ])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.01624502,  0.        ,  0.        ,  0.08202731, -0.03979683,
        0.06433267,  0.04963955,  0.01546219,  0.        , -0.01505809,
        0.        ,  0.07581308,  0.        ,  0.01683813,  0.09528198])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.01624502,  0.        ,  0.        , -0.08202731,  0.03979683,
       -0.06433267, -0.04963955, -0.01546219,  0.        ,  0.01505809,
        0.        , -0.07581308,  0.        , -0.01683813, -0.09528198])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.03981315,  0.        , -0.09663771, -0.05417734, -0.0469523 ,
       -0.01688425,  0.        , -0.02891689,  0.        ,  0.        ,
       -0.00957845, -0.09152674,  0.        , -0.02999968, -0.08302807])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:33 - shap - INFO - phi = array([0.03981315, 0.        , 0.09663771, 0.05417734, 0.0469523 ,
       0.01688425, 0.        , 0.02891689, 0.        , 0.        ,
       0.00957845, 0.09152674, 0.        , 0.02999968, 0.08302807])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.0310775 ,  0.        , -0.09501227, -0.06223887, -0.00574132,
       -0.03396467, -0.02810591, -0.03122147,  0.        ,  0.        ,
        0.        , -0.08158271,  0.        , -0.00294446, -0.12459628])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([0.0310775 , 0.        , 0.09501227, 0.06223887, 0.00574132,
       0.03396467, 0.02810591, 0.03122147, 0.        , 0.        ,
       0.        , 0.08158271, 0.        , 0.00294446, 0.12459628])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.05641114,  0.        , -0.08013139, -0.07210996, -0.03985932,
        0.        , -0.04185266, -0.01461303,  0.        ,  0.        ,
        0.        , -0.07766548, -0.00280819, -0.01602447, -0.09610158])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([0.05641114, 0.        , 0.08013139, 0.07210996, 0.03985932,
       0.        , 0.04185266, 0.01461303, 0.        , 0.        ,
       0.        , 0.07766548, 0.00280819, 0.01602447, 0.09610158])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([-0.07661349,  0.        ,  0.        , -0.03808993, -0.11411718,
        0.09070007, -0.11831561,  0.        ,  0.        ,  0.        ,
        0.01497517, -0.04486107, -0.0066071 , -0.00724647, -0.02490473])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.07661349,  0.        ,  0.        ,  0.03808993,  0.11411718,
       -0.09070007,  0.11831561,  0.        ,  0.        ,  0.        ,
       -0.01497517,  0.04486107,  0.0066071 ,  0.00724647,  0.02490473])
2025-07-16 01:07:33 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:33 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:33 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:33 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:33 - shap - INFO - phi = array([ 0.06751364,  0.        ,  0.08135011,  0.01469435,  0.09178777,
       -0.02154811,  0.04474043, -0.01503274,  0.        , -0.01011307,
        0.        ,  0.0983177 ,  0.        ,  0.        , -0.06254446])
2025-07-16 01:07:33 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.06751364,  0.        , -0.08135011, -0.01469435, -0.09178777,
        0.02154811, -0.04474043,  0.01503274,  0.        ,  0.01011307,
        0.        , -0.0983177 ,  0.        ,  0.        ,  0.06254446])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.        ,  0.        , -0.10260534, -0.0504954 , -0.04134701,
       -0.04607638, -0.01789809, -0.06629924,  0.        ,  0.0144317 ,
        0.        , -0.05538376,  0.        , -0.02207517, -0.10959712])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.10260534,  0.0504954 ,  0.04134701,
        0.04607638,  0.01789809,  0.06629924,  0.        , -0.0144317 ,
        0.        ,  0.05538376,  0.        ,  0.02207517,  0.10959712])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.29964382,  0.08383541,  0.02629585,
       -0.07217794, -0.06571909,  0.18174708,  0.        , -0.01099928,
       -0.05815436, -0.09545124,  0.        ,  0.        ,  0.19050123])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.        ,  0.        , -0.29964382, -0.08383541, -0.02629585,
        0.07217794,  0.06571909, -0.18174708,  0.        ,  0.01099928,
        0.05815436,  0.09545124,  0.        ,  0.        , -0.19050123])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.07881177,  0.        ,  0.        , -0.07561917, -0.0381645 ,
       -0.02954222, -0.04723057, -0.14921836,  0.        ,  0.        ,
        0.10814518,  0.07844283,  0.        , -0.057259  , -0.04414119])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.07881177,  0.        ,  0.        ,  0.07561917,  0.0381645 ,
        0.02954222,  0.04723057,  0.14921836,  0.        ,  0.        ,
       -0.10814518, -0.07844283,  0.        ,  0.057259  ,  0.04414119])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.09972341,  0.        , -0.17421221, -0.01553592, -0.08301581,
        0.18186127,  0.07557153, -0.05924002,  0.        ,  0.        ,
        0.06130805,  0.0708444 ,  0.        ,  0.        ,  0.19488873])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.09972341,  0.        ,  0.17421221,  0.01553592,  0.08301581,
       -0.18186127, -0.07557153,  0.05924002,  0.        ,  0.        ,
       -0.06130805, -0.0708444 ,  0.        ,  0.        , -0.19488873])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.09380038,  0.        , -0.04088318,  0.        , -0.01668316,
       -0.02357606,  0.07891379, -0.2035733 ,  0.        ,  0.        ,
        0.06551159,  0.12704018,  0.        , -0.0661593 , -0.08794053])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.09380038,  0.        ,  0.04088318,  0.        ,  0.01668316,
        0.02357606, -0.07891379,  0.2035733 ,  0.        ,  0.        ,
       -0.06551159, -0.12704018,  0.        ,  0.0661593 ,  0.08794053])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.02447126,  0.        ,  0.00853774,  0.        ,  0.03864875,
        0.09440005,  0.05383435, -0.06958748,  0.        ,  0.        ,
        0.08698915,  0.00833688,  0.        , -0.02652013,  0.11031571])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.02447126,  0.        , -0.00853774,  0.        , -0.03864875,
       -0.09440005, -0.05383435,  0.06958748,  0.        ,  0.        ,
       -0.08698915, -0.00833688,  0.        ,  0.02652013, -0.11031571])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.05096361,  0.        ,  0.24971818,  0.        , -0.0464867 ,
       -0.04991578, -0.13408111,  0.12313385,  0.        ,  0.        ,
        0.03785177, -0.13067411,  0.        , -0.02057995,  0.26117193])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.05096361,  0.        , -0.24971818,  0.        ,  0.0464867 ,
        0.04991578,  0.13408111, -0.12313385,  0.        ,  0.        ,
       -0.03785177,  0.13067411,  0.        ,  0.02057995, -0.26117193])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.05794684,  0.        ,  0.10968439, -0.02154438, -0.04598871,
        0.04854885, -0.16916157,  0.        ,  0.        ,  0.        ,
        0.0689113 , -0.04121928, -0.00958159,  0.        , -0.18453516])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.05794684,  0.        , -0.10968439,  0.02154438,  0.04598871,
       -0.04854885,  0.16916157,  0.        ,  0.        ,  0.        ,
       -0.0689113 ,  0.04121928,  0.00958159,  0.        ,  0.18453516])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.02624354,  0.        ,  0.10569279,  0.14240481, -0.06929426,
        0.01661122,  0.        ,  0.08914807,  0.        , -0.02678719,
       -0.05508361,  0.12880861,  0.        ,  0.04611702,  0.        ])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.02624354,  0.        , -0.10569279, -0.14240481,  0.06929426,
       -0.01661122,  0.        , -0.08914807,  0.        ,  0.02678719,
        0.05508361, -0.12880861,  0.        , -0.04611702,  0.        ])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.07368382,  0.        ,  0.00708898,  0.04217415,  0.05466531,
       -0.02055504,  0.07842539,  0.        ,  0.        , -0.0132439 ,
       -0.02268295,  0.18618389,  0.        ,  0.03422882,  0.        ])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.07368382,  0.        , -0.00708898, -0.04217415, -0.05466531,
        0.02055504, -0.07842539,  0.        ,  0.        ,  0.0132439 ,
        0.02268295, -0.18618389,  0.        , -0.03422882,  0.        ])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.10570868,  0.        , -0.06581291, -0.08522776, -0.06956653,
        0.07656888, -0.09963369,  0.08287903,  0.        ,  0.        ,
       -0.05835885, -0.25187756,  0.        ,  0.        ,  0.09482335])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.10570868,  0.        ,  0.06581291,  0.08522776,  0.06956653,
       -0.07656888,  0.09963369, -0.08287903,  0.        ,  0.        ,
        0.05835885,  0.25187756,  0.        ,  0.        , -0.09482335])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.00818252,  0.        ,  0.15319115,  0.06695869,  0.01642091,
       -0.04392199,  0.        ,  0.13261496,  0.        ,  0.        ,
       -0.063529  ,  0.02399134,  0.        ,  0.02428118,  0.1373129 ])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.00818252,  0.        , -0.15319115, -0.06695869, -0.01642091,
        0.04392199,  0.        , -0.13261496,  0.        ,  0.        ,
        0.063529  , -0.02399134,  0.        , -0.02428118, -0.1373129 ])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.0189386 ,  0.        ,  0.200283  ,  0.08397743,  0.        ,
       -0.09350195, -0.03386621,  0.1826559 ,  0.        ,  0.        ,
       -0.08839403, -0.0233742 ,  0.        ,  0.01986562,  0.21412855])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.0189386 ,  0.        , -0.200283  , -0.08397743,  0.        ,
        0.09350195,  0.03386621, -0.1826559 ,  0.        ,  0.        ,
        0.08839403,  0.0233742 ,  0.        , -0.01986562, -0.21412855])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.07385709,  0.07209034,  0.        ,
        0.02979978,  0.02017167,  0.07505687,  0.        , -0.01521463,
       -0.03331567,  0.04226251,  0.        ,  0.02105254,  0.1374941 ])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.        ,  0.        , -0.07385709, -0.07209034,  0.        ,
       -0.02979978, -0.02017167, -0.07505687,  0.        ,  0.01521463,
        0.03331567, -0.04226251,  0.        , -0.02105254, -0.1374941 ])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.08021532,  0.        , -0.23310505, -0.09725964, -0.11538554,
        0.15056186,  0.        , -0.1684631 ,  0.        ,  0.03064194,
        0.09965495,  0.05866171,  0.        ,  0.        ,  0.13586966])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.08021532,  0.        ,  0.23310505,  0.09725964,  0.11538554,
       -0.15056186,  0.        ,  0.1684631 ,  0.        , -0.03064194,
       -0.09965495, -0.05866171,  0.        ,  0.        , -0.13586966])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.        , -0.00671228, -0.13739984, -0.07437474,  0.        ,
       -0.06767227, -0.02754869, -0.04327716,  0.        ,  0.0402717 ,
       -0.02824562, -0.05579736,  0.        ,  0.        , -0.06387935])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.        ,  0.00671228,  0.13739984,  0.07437474,  0.        ,
        0.06767227,  0.02754869,  0.04327716,  0.        , -0.0402717 ,
        0.02824562,  0.05579736,  0.        ,  0.        ,  0.06387935])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.03236649,  0.0132695 ,  0.13376573,  0.09504958, -0.17028713,
       -0.10845069, -0.14959115,  0.07587849,  0.        ,  0.        ,
        0.        , -0.02496028,  0.        ,  0.        ,  0.10969889])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.03236649, -0.0132695 , -0.13376573, -0.09504958,  0.17028713,
        0.10845069,  0.14959115, -0.07587849,  0.        ,  0.        ,
        0.        ,  0.02496028,  0.        ,  0.        , -0.10969889])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.03510453,  0.02204994,  0.14613421,  0.0188995 ,  0.        ,
       -0.1011798 , -0.06465393,  0.10940693,  0.        ,  0.        ,
        0.        ,  0.0649741 ,  0.        ,  0.02390776,  0.09567623])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.03510453, -0.02204994, -0.14613421, -0.0188995 ,  0.        ,
        0.1011798 ,  0.06465393, -0.10940693,  0.        ,  0.        ,
        0.        , -0.0649741 ,  0.        , -0.02390776, -0.09567623])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.12825462,  0.01950708,  0.12199924,  0.        , -0.05935429,
       -0.12522423, -0.13211289, -0.03919225,  0.        ,  0.        ,
        0.01583328,  0.17881087,  0.        ,  0.        , -0.17497941])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.12825462, -0.01950708, -0.12199924,  0.        ,  0.05935429,
        0.12522423,  0.13211289,  0.03919225,  0.        ,  0.        ,
       -0.01583328, -0.17881087,  0.        ,  0.        ,  0.17497941])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.07947482,  0.        ,  0.31330769,  0.12516107, -0.02764324,
       -0.12104692, -0.09921247,  0.13157788,  0.        ,  0.        ,
       -0.04579774,  0.04770691,  0.        ,  0.0262425 ,  0.        ])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.07947482,  0.        , -0.31330769, -0.12516107,  0.02764324,
        0.12104692,  0.09921247, -0.13157788,  0.        ,  0.        ,
        0.04579774, -0.04770691,  0.        , -0.0262425 ,  0.        ])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.00143039,  0.        , -0.07550967, -0.07359506, -0.02143579,
       -0.05594961, -0.03924602, -0.05118499,  0.        ,  0.        ,
        0.        , -0.0510848 ,  0.        , -0.01260535, -0.11940273])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([0.00143039, 0.        , 0.07550967, 0.07359506, 0.02143579,
       0.05594961, 0.03924602, 0.05118499, 0.        , 0.        ,
       0.        , 0.0510848 , 0.        , 0.01260535, 0.11940273])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([-0.01939656, -0.00394588, -0.15605451, -0.01894398, -0.09563922,
       -0.01667086,  0.        , -0.06665495,  0.        ,  0.        ,
        0.        , -0.03121189,  0.        , -0.00669013, -0.08613984])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([0.01939656, 0.00394588, 0.15605451, 0.01894398, 0.09563922,
       0.01667086, 0.        , 0.06665495, 0.        , 0.        ,
       0.        , 0.03121189, 0.        , 0.00669013, 0.08613984])
2025-07-16 01:07:34 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:34 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:34 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:34 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:34 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.1600863 ,  0.0722282 , -0.04411037,
       -0.03461529, -0.05667885,  0.13691521,  0.        ,  0.        ,
       -0.04410766,  0.04250294,  0.        ,  0.02657865,  0.16259358])
2025-07-16 01:07:34 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.        ,  0.        , -0.1600863 , -0.0722282 ,  0.04411037,
        0.03461529,  0.05667885, -0.13691521,  0.        ,  0.        ,
        0.04410766, -0.04250294,  0.        , -0.02657865, -0.16259358])
2025-07-16 01:07:35 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:35 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:35 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:35 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.0698123 ,  0.        ,  0.21360705,  0.03854842,  0.15310602,
       -0.08963395,  0.03427247,  0.0168592 ,  0.        ,  0.        ,
        0.00878989, -0.02635117,  0.        , -0.02230137,  0.        ])
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:35 - shap - INFO - phi = array([-0.0698123 ,  0.        , -0.21360705, -0.03854842, -0.15310602,
        0.08963395, -0.03427247, -0.0168592 ,  0.        ,  0.        ,
       -0.00878989,  0.02635117,  0.        ,  0.02230137,  0.        ])
2025-07-16 01:07:35 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:35 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:35 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:35 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.        ,  0.        , -0.01970023, -0.09835797,  0.        ,
       -0.02652918, -0.05587538, -0.11884873,  0.        ,  0.03531379,
        0.0715227 , -0.07757627,  0.        , -0.0388367 , -0.09778935])
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.        ,  0.        ,  0.01970023,  0.09835797,  0.        ,
        0.02652918,  0.05587538,  0.11884873,  0.        , -0.03531379,
       -0.0715227 ,  0.07757627,  0.        ,  0.0388367 ,  0.09778935])
2025-07-16 01:07:35 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:35 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:35 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:35 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([-0.05006538,  0.        , -0.07563435, -0.08924816,  0.        ,
        0.07856897,  0.0312402 , -0.02023351,  0.        ,  0.01557249,
        0.04820087,  0.08287741,  0.        ,  0.        ,  0.14988007])
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.05006538,  0.        ,  0.07563435,  0.08924816,  0.        ,
       -0.07856897, -0.0312402 ,  0.02023351,  0.        , -0.01557249,
       -0.04820087, -0.08287741,  0.        ,  0.        , -0.14988007])
2025-07-16 01:07:35 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:35 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:35 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:35 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([-0.07686878,  0.        , -0.01079373, -0.07999627, -0.05257719,
        0.        , -0.1401225 ,  0.04081962,  0.        ,  0.        ,
        0.04179952, -0.15778076, -0.00582915,  0.        , -0.04554   ])
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.07686878,  0.        ,  0.01079373,  0.07999627,  0.05257719,
        0.        ,  0.1401225 , -0.04081962,  0.        ,  0.        ,
       -0.04179952,  0.15778076,  0.00582915,  0.        ,  0.04554   ])
2025-07-16 01:07:35 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:35 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:35 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:35 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:35 - shap - INFO - phi = array([-0.12145292,  0.01450859, -0.01347897, -0.10182253,  0.        ,
        0.04176862, -0.12490657,  0.07923923,  0.        ,  0.        ,
        0.02499399, -0.25311697, -0.01078834,  0.        ,  0.        ])
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.12145292, -0.01450859,  0.01347897,  0.10182253,  0.        ,
       -0.04176862,  0.12490657, -0.07923923,  0.        ,  0.        ,
       -0.02499399,  0.25311697,  0.01078834,  0.        ,  0.        ])
2025-07-16 01:07:35 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:35 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:35 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:35 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.03132344,  0.        ,  0.0181923 ,  0.07488303,  0.08242212,
        0.        ,  0.10504764,  0.06723713,  0.        ,  0.        ,
       -0.05498585,  0.06024786,  0.        ,  0.03227741,  0.04673008])
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([-0.03132344,  0.        , -0.0181923 , -0.07488303, -0.08242212,
        0.        , -0.10504764, -0.06723713,  0.        ,  0.        ,
        0.05498585, -0.06024786,  0.        , -0.03227741, -0.04673008])
2025-07-16 01:07:35 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:35 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:35 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:35 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.04763829, -0.01902931, -0.2090396 , -0.07827751,  0.07010654,
        0.09179051,  0.09445109, -0.19539589,  0.        ,  0.        ,
        0.        ,  0.09721156,  0.        ,  0.        , -0.23174255])
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999996
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([-0.04763829,  0.01902931,  0.2090396 ,  0.07827751, -0.07010654,
       -0.09179051, -0.09445109,  0.19539589,  0.        ,  0.        ,
        0.        , -0.09721156,  0.        ,  0.        ,  0.23174255])
2025-07-16 01:07:35 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:35 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:35 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:35 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:35 - shap - INFO - phi = array([ 0.03244108,  0.        , -0.22202911, -0.03793734,  0.02652137,
        0.10244754,  0.1794968 , -0.16405239,  0.        ,  0.        ,
        0.10003346,  0.21061833,  0.        ,  0.        ,  0.02994512])
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 15.0
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-07-16 01:07:35 - shap - INFO - phi = array([-0.03244108,  0.        ,  0.22202911,  0.03793734, -0.02652137,
       -0.10244754, -0.1794968 ,  0.16405239,  0.        ,  0.        ,
       -0.10003346, -0.21061833,  0.        ,  0.        , -0.02994512])
2025-07-16 01:07:35 - shap - INFO - num_full_subsets = 2
2025-07-16 01:07:35 - shap - INFO - remaining_weight_vector = array([0.25989514, 0.21264148, 0.1871245 , 0.17326343, 0.16707545])
2025-07-16 01:07:35 - shap - INFO - num_paired_subset_sizes = 7
2025-07-16 01:07:35 - shap - INFO - weight_left = 0.4930585722173909
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([-0.03042685,  0.        , -0.10316259, -0.05450151, -0.09052543,
        0.        , -0.05429141, -0.0352112 ,  0.        ,  0.        ,
       -0.0017116 , -0.06041285,  0.        , -0.01305273, -0.05737216])
2025-07-16 01:07:35 - shap - INFO - np.sum(w_aug) = 14.999999999999998
2025-07-16 01:07:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-07-16 01:07:35 - shap - INFO - phi = array([0.03042685, 0.        , 0.10316259, 0.05450151, 0.09052543,
       0.        , 0.05429141, 0.0352112 , 0.        , 0.        ,
       0.0017116 , 0.06041285, 0.        , 0.01305273, 0.05737216])
