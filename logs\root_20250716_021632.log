2025-07-16 02:16:33 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 02:16:34 - GUI - INFO - GUI界面初始化完成
2025-07-16 02:16:54 - model_ensemble - INFO - ============================================================
2025-07-16 02:16:54 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 02:16:54 - model_ensemble - INFO - ============================================================
2025-07-16 02:16:54 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-16 02:16:54 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 02:16:54 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 02:16:54 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 02:16:55 - model_training - INFO - 模型名称: Random Forest
2025-07-16 02:16:55 - model_training - INFO - 准确率: 0.8250
2025-07-16 02:16:55 - model_training - INFO - AUC: 0.9015
2025-07-16 02:16:55 - model_training - INFO - 混淆矩阵:
2025-07-16 02:16:55 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-16 02:16:55 - model_training - INFO - 
分类报告:
2025-07-16 02:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 02:16:55 - model_training - INFO - 训练时间: 0.09 秒
2025-07-16 02:16:55 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 02:16:55 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-16 02:16:55 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 02:16:55 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-16 02:16:55 - model_training - INFO - 模型名称: XGBoost
2025-07-16 02:16:55 - model_training - INFO - 准确率: 0.7750
2025-07-16 02:16:55 - model_training - INFO - AUC: 0.8568
2025-07-16 02:16:55 - model_training - INFO - 混淆矩阵:
2025-07-16 02:16:55 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-16 02:16:55 - model_training - INFO - 
分类报告:
2025-07-16 02:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-16 02:16:55 - model_training - INFO - 训练时间: 0.05 秒
2025-07-16 02:16:55 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 02:16:55 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-16 02:16:55 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-16 02:16:55 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-16 02:16:56 - model_training - INFO - 模型名称: LightGBM
2025-07-16 02:16:56 - model_training - INFO - 准确率: 0.7500
2025-07-16 02:16:56 - model_training - INFO - AUC: 0.8568
2025-07-16 02:16:56 - model_training - INFO - 混淆矩阵:
2025-07-16 02:16:56 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-07-16 02:16:56 - model_training - INFO - 
分类报告:
2025-07-16 02:16:56 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-07-16 02:16:56 - model_training - INFO - 训练时间: 1.66 秒
2025-07-16 02:16:56 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-16 02:16:56 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-16 02:16:56 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-16 02:16:56 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-16 02:16:56 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 02:16:56 - model_training - INFO - 准确率: 0.8250
2025-07-16 02:16:56 - model_training - INFO - AUC: 0.9028
2025-07-16 02:16:56 - model_training - INFO - 混淆矩阵:
2025-07-16 02:16:56 - model_training - INFO - 
[[19  4]
 [ 3 14]]
2025-07-16 02:16:56 - model_training - INFO - 
分类报告:
2025-07-16 02:16:56 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.83      0.84        23
           1       0.78      0.82      0.80        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.83      0.82      0.83        40

2025-07-16 02:16:56 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 02:16:56 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 02:16:56 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-16 02:16:56 - model_ensemble - INFO -   Logistic 训练完成
2025-07-16 02:16:56 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-16 02:16:56 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 02:16:56 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 02:16:58 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-16 02:16:58 - model_ensemble - INFO - ============================================================
2025-07-16 02:16:58 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 02:16:58 - model_ensemble - INFO - ============================================================
2025-07-16 02:16:58 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 02:16:58 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-16 02:16:58 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-16 02:16:58 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 02:16:58 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-16 02:16:58 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 02:16:58 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_021658.joblib
2025-07-16 02:16:58 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 02:16:58 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-16 02:16:58 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 02:16:58 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 02:16:58 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 02:16:58 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-16 02:16:58 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-16 02:16:58 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:58 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:58 - shap - INFO - phi = array([ 0.05793002,  0.12045515,  0.04591876,  0.01527489, -0.00714712,
        0.00132707,  0.14702441, -0.00258756, -0.02109828])
2025-07-16 02:16:58 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:58 - shap - INFO - phi = array([-0.05793002, -0.12045515, -0.04591876, -0.01527489,  0.00714712,
       -0.00132707, -0.14702441,  0.00258756,  0.02109828])
2025-07-16 02:16:58 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:58 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:58 - shap - INFO - phi = array([ 0.0477588 ,  0.08788138,  0.04695627,  0.02045211, -0.01362022,
       -0.01585175,  0.19276975, -0.01072042,  0.0176649 ])
2025-07-16 02:16:58 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.0477588 , -0.08788138, -0.04695627, -0.02045211,  0.01362022,
        0.01585175, -0.19276975,  0.01072042, -0.0176649 ])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.12664885, -0.16157978, -0.08747176, -0.03673952, -0.00976965,
        0.00659212, -0.07730405, -0.01429322,  0.01553086])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.12664885,  0.16157978,  0.08747176,  0.03673952,  0.00976965,
       -0.00659212,  0.07730405,  0.01429322, -0.01553086])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.05588764, -0.07577701, -0.13943054,  0.01373185, -0.01109992,
       -0.01733146, -0.23522913,  0.00573581, -0.00229526])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.05588764,  0.07577701,  0.13943054, -0.01373185,  0.01109992,
        0.01733146,  0.23522913, -0.00573581,  0.00229526])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.02624078,  0.15012616,  0.05966024,  0.01707613, -0.00607131,
        0.02294647,  0.10657346,  0.04578632, -0.06857157])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.02624078, -0.15012616, -0.05966024, -0.01707613,  0.00607131,
       -0.02294647, -0.10657346, -0.04578632,  0.06857157])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.04121576,  0.11210427,  0.05918699, -0.02114271,  0.04114237,
        0.01965359,  0.08058778, -0.00910362,  0.039061  ])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.04121576, -0.11210427, -0.05918699,  0.02114271, -0.04114237,
       -0.01965359, -0.08058778,  0.00910362, -0.039061  ])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.04202077,  0.11758455,  0.06362272,  0.01238644, -0.00386014,
       -0.01430494,  0.16883163, -0.00465082, -0.01690854])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.04202077, -0.11758455, -0.06362272, -0.01238644,  0.00386014,
        0.01430494, -0.16883163,  0.00465082,  0.01690854])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.02760515,  0.10505647,  0.04517261,  0.02611034, -0.02552949,
       -0.0605627 , -0.33620441,  0.0467344 , -0.07111081])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.02760515, -0.10505647, -0.04517261, -0.02611034,  0.02552949,
        0.0605627 ,  0.33620441, -0.0467344 ,  0.07111081])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.12094595, -0.15192546, -0.19227017,  0.03270319, -0.04072483,
       -0.00477083,  0.06945612, -0.03298133,  0.06276487])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.12094595,  0.15192546,  0.19227017, -0.03270319,  0.04072483,
        0.00477083, -0.06945612,  0.03298133, -0.06276487])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.01249373, -0.15829949, -0.03547078, -0.041248  , -0.01898688,
        0.02318515, -0.1713216 , -0.02019386,  0.00701518])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.01249373,  0.15829949,  0.03547078,  0.041248  ,  0.01898688,
       -0.02318515,  0.1713216 ,  0.02019386, -0.00701518])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.04443228,  0.22105693, -0.00699543,  0.02307876, -0.01874677,
       -0.01783808,  0.19524653, -0.01510329, -0.00821113])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.04443228, -0.22105693,  0.00699543, -0.02307876,  0.01874677,
        0.01783808, -0.19524653,  0.01510329,  0.00821113])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.07094932,  0.08469146,  0.04857048,  0.02034567, -0.01755392,
       -0.01381775,  0.17406762, -0.00946634,  0.01551582])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.07094932, -0.08469146, -0.04857048, -0.02034567,  0.01755392,
        0.01381775, -0.17406762,  0.00946634, -0.01551582])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.08666885, -0.27509568, -0.0245148 ,  0.00029488, -0.01972924,
       -0.02474672, -0.05638701,  0.03378269, -0.0446375 ])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.08666885,  0.27509568,  0.0245148 , -0.00029488,  0.01972924,
        0.02474672,  0.05638701, -0.03378269,  0.0446375 ])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.05762842, -0.31013564, -0.08440625,  0.00280314, -0.02220948,
        0.07156679,  0.01755053,  0.04930673, -0.04972591])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.05762842,  0.31013564,  0.08440625, -0.00280314,  0.02220948,
       -0.07156679, -0.01755053, -0.04930673,  0.04972591])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.01170085, -0.15497507, -0.13379222,  0.01379445, -0.00753714,
        0.03160943, -0.23920091,  0.01917418, -0.02078729])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.01170085,  0.15497507,  0.13379222, -0.01379445,  0.00753714,
       -0.03160943,  0.23920091, -0.01917418,  0.02078729])
2025-07-16 02:16:59 - shap - INFO - num_full_subsets = 4
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([ 0.1254539 , -0.19334466,  0.09772105,  0.01996678, -0.01800899,
        0.0163658 ,  0.0487857 ,  0.02175645, -0.07730998])
2025-07-16 02:16:59 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:16:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:16:59 - shap - INFO - phi = array([-0.1254539 ,  0.19334466, -0.09772105, -0.01996678,  0.01800899,
       -0.0163658 , -0.0487857 , -0.02175645,  0.07730998])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.02203551,  0.10015169,  0.02915814, -0.00037463, -0.01060084,
       -0.05334304, -0.37121965,  0.04242798, -0.04411484])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.02203551, -0.10015169, -0.02915814,  0.00037463,  0.01060084,
        0.05334304,  0.37121965, -0.04242798,  0.04411484])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.13642455, -0.02161413,  0.09333006,  0.0076438 , -0.00572745,
        0.0048076 ,  0.10368871, -0.00491256, -0.00082642])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.13642455,  0.02161413, -0.09333006, -0.0076438 ,  0.00572745,
       -0.0048076 , -0.10368871,  0.00491256,  0.00082642])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.07814533,  0.08809192,  0.06747352, -0.01675673,  0.02846274,
        0.02039065,  0.0740997 , -0.00833674,  0.03582159])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.07814533, -0.08809192, -0.06747352,  0.01675673, -0.02846274,
       -0.02039065, -0.0740997 ,  0.00833674, -0.03582159])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.06991596, -0.15462661, -0.04043806, -0.02796804,  0.04577729,
       -0.01761463, -0.26170693, -0.01411966,  0.03784387])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.06991596,  0.15462661,  0.04043806,  0.02796804, -0.04577729,
        0.01761463,  0.26170693,  0.01411966, -0.03784387])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.06485362, -0.04788122,  0.08843542, -0.05897831,  0.00107577,
        0.01216769,  0.0441409 , -0.03182279,  0.04316649])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.06485362,  0.04788122, -0.08843542,  0.05897831, -0.00107577,
       -0.01216769, -0.0441409 ,  0.03182279, -0.04316649])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.02152255,  0.02630486,  0.09867263, -0.04082387, -0.00300106,
        0.0106612 ,  0.114306  , -0.02806174,  0.06900547])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.02152255, -0.02630486, -0.09867263,  0.04082387,  0.00300106,
       -0.0106612 , -0.114306  ,  0.02806174, -0.06900547])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.0991307 ,  0.12278892,  0.06303034, -0.01610514,  0.05217289,
        0.02226947,  0.0325018 , -0.01028641,  0.00445236])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.0991307 , -0.12278892, -0.06303034,  0.01610514, -0.05217289,
       -0.02226947, -0.0325018 ,  0.01028641, -0.00445236])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.07360494,  0.09795834,  0.05446695,  0.01926681, -0.01692461,
       -0.01445203,  0.16752919, -0.01083405,  0.00230844])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.07360494, -0.09795834, -0.05446695, -0.01926681,  0.01692461,
        0.01445203, -0.16752919,  0.01083405, -0.00230844])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-6.76888641e-02, -1.45563447e-01, -8.28947741e-02,  1.24004642e-04,
       -8.85053086e-03,  2.64946126e-02, -2.42188813e-01,  2.25888206e-02,
       -2.33411246e-02])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 6.76888641e-02,  1.45563447e-01,  8.28947741e-02, -1.24004642e-04,
        8.85053086e-03, -2.64946126e-02,  2.42188813e-01, -2.25888206e-02,
        2.33411246e-02])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.08888916,  0.10109349,  0.05312942, -0.0163409 ,  0.05051593,
        0.02331023,  0.03443394, -0.00981082,  0.04406756])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.08888916, -0.10109349, -0.05312942,  0.0163409 , -0.05051593,
       -0.02331023, -0.03443394,  0.00981082, -0.04406756])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.05003737,  0.1896071 , -0.14921846,  0.02961176, -0.02230314,
       -0.00713114,  0.1163899 , -0.01754645, -0.01919128])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.05003737, -0.1896071 ,  0.14921846, -0.02961176,  0.02230314,
        0.00713114, -0.1163899 ,  0.01754645,  0.01919128])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.11340056, -0.22415296,  0.11606761,  0.02798678, -0.0178895 ,
       -0.04126669,  0.11059844,  0.04657037, -0.11877671])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.11340056,  0.22415296, -0.11606761, -0.02798678,  0.0178895 ,
        0.04126669, -0.11059844, -0.04657037,  0.11877671])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.03374872, -0.00399658,  0.04404311, -0.00777002,  0.07974779,
       -0.06102011, -0.33957216,  0.00415832,  0.0101935 ])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.03374872,  0.00399658, -0.04404311,  0.00777002, -0.07974779,
        0.06102011,  0.33957216, -0.00415832, -0.0101935 ])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([-0.05390736, -0.25725811, -0.18162027,  0.02604623, -0.01822337,
        0.10275753,  0.16481317,  0.03435534, -0.04098327])
2025-07-16 02:17:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:00 - shap - INFO - phi = array([ 0.05390736,  0.25725811,  0.18162027, -0.02604623,  0.01822337,
       -0.10275753, -0.16481317, -0.03435534,  0.04098327])
2025-07-16 02:17:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.04112782,  0.16258655,  0.04188165,  0.00345144, -0.01135231,
        0.00633355,  0.13227263, -0.00436886, -0.01674847])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.04112782, -0.16258655, -0.04188165, -0.00345144,  0.01135231,
       -0.00633355, -0.13227263,  0.00436886,  0.01674847])
2025-07-16 02:17:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.07590247, -0.15467555, -0.06299516, -0.00518089, -0.00133675,
       -0.00560243, -0.22021253, -0.00898636,  0.0173275 ])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.07590247,  0.15467555,  0.06299516,  0.00518089,  0.00133675,
        0.00560243,  0.22021253,  0.00898636, -0.0173275 ])
2025-07-16 02:17:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.1002104 , -0.1313346 ,  0.00566005, -0.03238153,  0.06873626,
       -0.00856858, -0.30455244, -0.01386215,  0.03890342])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.1002104 ,  0.1313346 , -0.00566005,  0.03238153, -0.06873626,
        0.00856858,  0.30455244,  0.01386215, -0.03890342])
2025-07-16 02:17:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.10510365,  0.18992784,  0.00793428, -0.05246793, -0.00083885,
        0.01402682, -0.01888977, -0.02845682,  0.02710262])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.10510365, -0.18992784, -0.00793428,  0.05246793,  0.00083885,
       -0.01402682,  0.01888977,  0.02845682, -0.02710262])
2025-07-16 02:17:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.04389418,  0.19131121, -0.07089967,  0.02419791, -0.01612431,
       -0.00049469,  0.22685102, -0.0131321 ,  0.03834666])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.04389418, -0.19131121,  0.07089967, -0.02419791,  0.01612431,
        0.00049469, -0.22685102,  0.0131321 , -0.03834666])
2025-07-16 02:17:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.06384528,  0.04472501,  0.03989973,  0.02462565, -0.02242568,
       -0.02794674,  0.24684419, -0.02138969,  0.02448457])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.06384528, -0.04472501, -0.03989973, -0.02462565,  0.02242568,
        0.02794674, -0.24684419,  0.02138969, -0.02448457])
2025-07-16 02:17:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.0638903 ,  0.16714453, -0.14327589,  0.02034248, -0.01433634,
       -0.00311865,  0.21135447, -0.00955557,  0.05274626])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.0638903 , -0.16714453,  0.14327589, -0.02034248,  0.01433634,
        0.00311865, -0.21135447,  0.00955557, -0.05274626])
2025-07-16 02:17:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.0461112 ,  0.13123545,  0.06773649,  0.00153901, -0.00682908,
       -0.01756099,  0.13253712,  0.00237158, -0.00503419])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.0461112 , -0.13123545, -0.06773649, -0.00153901,  0.00682908,
        0.01756099, -0.13253712, -0.00237158,  0.00503419])
2025-07-16 02:17:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.0863873 , -0.09328306,  0.03833799, -0.03287123,  0.05649832,
       -0.02294494, -0.34126634, -0.01104521,  0.02627652])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.0863873 ,  0.09328306, -0.03833799,  0.03287123, -0.05649832,
        0.02294494,  0.34126634,  0.01104521, -0.02627652])
2025-07-16 02:17:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([ 0.08399227,  0.10363578,  0.05962772,  0.00429496, -0.00677046,
        0.01382313,  0.10200102, -0.00912058,  0.02007312])
2025-07-16 02:17:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:01 - shap - INFO - phi = array([-0.08399227, -0.10363578, -0.05962772, -0.00429496,  0.00677046,
       -0.01382313, -0.10200102,  0.00912058, -0.02007312])
2025-07-16 02:17:06 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-16 02:17:06 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 02:17:06 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 02:17:06 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 02:17:06 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 02:17:19 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 02:17:20 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-16 02:17:20 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-0.00057741,  0.07201039,  0.0084989 ,  0.        ,  0.00544509,
       -0.00146503,  0.25136162, -0.00205605, -0.00205595])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 0.00057741, -0.07201039, -0.0084989 ,  0.        , -0.00544509,
        0.00146503, -0.25136162,  0.00205605,  0.00205595])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 0.00000000e+00,  6.50806326e-02, -8.54956624e-04,  3.99875379e-05,
        6.21040925e-03,  1.34749510e-02,  2.51641790e-01,  5.07799279e-03,
        1.31450645e-03])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 0.00000000e+00, -6.50806326e-02,  8.54956624e-04, -3.99875379e-05,
       -6.21040925e-03, -1.34749510e-02, -2.51641790e-01, -5.07799279e-03,
       -1.31450645e-03])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 0.00068226, -0.00557587, -0.00063646,  0.        ,  0.00209636,
       -0.00216633,  0.33060605, -0.0008097 , -0.00456681])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-0.00068226,  0.00557587,  0.00063646,  0.        , -0.00209636,
        0.00216633, -0.33060605,  0.0008097 ,  0.00456681])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 6.76744488e-04, -5.90995392e-03, -6.17285300e-04,  0.00000000e+00,
        4.69997079e-03,  2.04521426e-04,  3.18539758e-01,  6.42463353e-03,
       -1.21757943e-03])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-6.76744488e-04,  5.90995392e-03,  6.17285300e-04,  0.00000000e+00,
       -4.69997079e-03, -2.04521426e-04, -3.18539758e-01, -6.42463353e-03,
        1.21757943e-03])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-6.00827654e-04,  1.83986137e-02, -1.13573779e-03,  8.39379149e-05,
       -1.17592306e-03, -3.64388139e-03,  3.04627692e-01,  1.21135006e-03,
       -3.43485597e-03])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 6.00827654e-04, -1.83986137e-02,  1.13573779e-03, -8.39379149e-05,
        1.17592306e-03,  3.64388139e-03, -3.04627692e-01, -1.21135006e-03,
        3.43485597e-03])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-0.00061569,  0.07262045,  0.00317975,  0.        , -0.0010635 ,
       -0.00483227,  0.25327692, -0.00164731, -0.00164716])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 0.00061569, -0.07262045, -0.00317975,  0.        ,  0.0010635 ,
        0.00483227, -0.25327692,  0.00164731,  0.00164716])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 2.61058870e-04, -1.54542370e-02, -3.66841566e-04, -1.62155326e-04,
        5.36530594e-03,  1.98501567e-02,  3.17684040e-01,  1.59679261e-02,
       -1.15994156e-03])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-2.61058870e-04,  1.54542370e-02,  3.66841566e-04,  1.62155326e-04,
       -5.36530594e-03, -1.98501567e-02, -3.17684040e-01, -1.59679261e-02,
        1.15994156e-03])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 2.34401848e-04, -3.77793636e-04, -3.08304466e-04,  0.00000000e+00,
        5.21276897e-03,  1.36457158e-02,  3.17577224e-01,  1.05634020e-02,
       -4.56210181e-03])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-2.34401848e-04,  3.77793636e-04,  3.08304466e-04,  0.00000000e+00,
       -5.21276897e-03, -1.36457158e-02, -3.17577224e-01, -1.05634020e-02,
        4.56210181e-03])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 2.53177339e-04, -4.92954879e-02, -1.90171554e-04,  0.00000000e+00,
       -2.01784514e-03, -3.00847936e-03, -4.01071779e-01, -1.81587939e-03,
        5.60282010e-03])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-2.53177339e-04,  4.92954879e-02,  1.90171554e-04,  0.00000000e+00,
        2.01784514e-03,  3.00847936e-03,  4.01071779e-01,  1.81587939e-03,
       -5.60282010e-03])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-1.10334240e-04, -1.64806689e-02, -1.40367615e-04, -7.79256043e-05,
       -1.99534696e-03, -4.05983502e-03, -4.37098341e-01,  1.94785198e-03,
        6.46618565e-03])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 1.10334240e-04,  1.64806689e-02,  1.40367615e-04,  7.79256043e-05,
        1.99534696e-03,  4.05983502e-03,  4.37098341e-01, -1.94785198e-03,
       -6.46618565e-03])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-1.51133455e-04, -3.89232018e-02, -1.61171867e-04,  0.00000000e+00,
       -1.99248723e-03, -4.03594942e-03, -4.05149204e-01, -2.94395805e-03,
        1.80832339e-03])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 1.51133455e-04,  3.89232018e-02,  1.61171867e-04,  0.00000000e+00,
        1.99248723e-03,  4.03594942e-03,  4.05149204e-01,  2.94395805e-03,
       -1.80832339e-03])
2025-07-16 02:17:20 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([ 2.55473734e-04, -6.62643067e-02, -4.78715330e-04,  0.00000000e+00,
       -1.75570228e-03, -2.68931677e-03, -3.83631079e-01, -7.46562856e-04,
        3.76656411e-03])
2025-07-16 02:17:20 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:20 - shap - INFO - phi = array([-2.55473734e-04,  6.62643067e-02,  4.78715330e-04,  0.00000000e+00,
        1.75570228e-03,  2.68931677e-03,  3.83631079e-01,  7.46562856e-04,
       -3.76656411e-03])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-0.00067638,  0.10210051, -0.00118059,  0.        , -0.00112188,
       -0.00468446,  0.23732588, -0.01336622, -0.0040665 ])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.00067638, -0.10210051,  0.00118059,  0.        ,  0.00112188,
        0.00468446, -0.23732588,  0.01336622,  0.0040665 ])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.00099403, -0.08220841, -0.00099392, -0.00028105, -0.00233154,
       -0.01827633,  0.18304983, -0.01189767, -0.03295627])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-0.00099403,  0.08220841,  0.00099392,  0.00028105,  0.00233154,
        0.01827633, -0.18304983,  0.01189767,  0.03295627])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.00000000e+00,  5.37430055e-02, -2.61892132e-04,  1.38398717e-04,
        5.55844433e-03,  1.39801071e-02,  2.72447100e-01, -3.05298328e-03,
       -5.66867712e-04])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.00000000e+00, -5.37430055e-02,  2.61892132e-04, -1.38398717e-04,
       -5.55844433e-03, -1.39801071e-02, -2.72447100e-01,  3.05298328e-03,
        5.66867712e-04])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-1.15183862e-04, -5.18086594e-02, -1.50938817e-04,  0.00000000e+00,
       -2.02105939e-03, -4.27986914e-03, -3.87944980e-01, -5.82433205e-03,
        5.96241188e-04])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 1.15183862e-04,  5.18086594e-02,  1.50938817e-04,  0.00000000e+00,
        2.02105939e-03,  4.27986914e-03,  3.87944980e-01,  5.82433205e-03,
       -5.96241188e-04])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-0.00063481,  0.08600843, -0.00191678,  0.        , -0.00240052,
       -0.00686518,  0.25053951, -0.00816154, -0.00405941])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.00063481, -0.08600843,  0.00191678,  0.        ,  0.00240052,
        0.00686518, -0.25053951,  0.00816154,  0.00405941])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 2.51874874e-04, -4.89285193e-02, -2.41958662e-03,  0.00000000e+00,
       -2.00563563e-03, -2.70015656e-03, -3.98771485e-01, -3.42658636e-03,
        4.32063668e-03])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-2.51874874e-04,  4.89285193e-02,  2.41958662e-03,  0.00000000e+00,
        2.00563563e-03,  2.70015656e-03,  3.98771485e-01,  3.42658636e-03,
       -4.32063668e-03])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 2.48428697e-04, -6.34604225e-02, -2.10085482e-04, -1.80829783e-04,
       -2.01745706e-03, -3.10500977e-03, -3.82817673e-01,  1.10396336e-04,
       -1.10992307e-04])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-2.48428697e-04,  6.34604225e-02,  2.10085482e-04,  1.80829783e-04,
        2.01745706e-03,  3.10500977e-03,  3.82817673e-01, -1.10396336e-04,
        1.10992307e-04])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-0.00063323,  0.07139244, -0.00197772,  0.        , -0.0023742 ,
       -0.00568208,  0.25247199, -0.00210458,  0.00141708])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.00063323, -0.07139244,  0.00197772,  0.        ,  0.0023742 ,
        0.00568208, -0.25247199,  0.00210458, -0.00141708])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-1.34906921e-04, -3.66434127e-02, -1.66273843e-04,  0.00000000e+00,
       -2.00018544e-03, -2.17360132e-03, -4.10595914e-01, -2.24543260e-03,
        2.41094475e-03])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 1.34906921e-04,  3.66434127e-02,  1.66273843e-04,  0.00000000e+00,
        2.00018544e-03,  2.17360132e-03,  4.10595914e-01,  2.24543260e-03,
       -2.41094475e-03])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 2.53107191e-04, -4.72790682e-02, -2.44136776e-03, -8.44496618e-05,
       -1.98849563e-03, -4.04758366e-03, -4.02296978e-01, -1.70457028e-03,
        5.90994795e-03])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-2.53107191e-04,  4.72790682e-02,  2.44136776e-03,  8.44496618e-05,
        1.98849563e-03,  4.04758366e-03,  4.02296978e-01,  1.70457028e-03,
       -5.90994795e-03])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-0.00058844,  0.07305244,  0.00882573,  0.        , -0.00099971,
       -0.00446578,  0.25314262, -0.00201634, -0.00184298])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.00058844, -0.07305244, -0.00882573,  0.        ,  0.00099971,
        0.00446578, -0.25314262,  0.00201634,  0.00184298])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 2.82275660e-04, -6.61924712e-02, -2.45466169e-03, -1.51599927e-04,
       -2.08741572e-03, -3.93858003e-03, -3.69279185e-01, -7.25451351e-03,
       -2.60330714e-03])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-2.82275660e-04,  6.61924712e-02,  2.45466169e-03,  1.51599927e-04,
        2.08741572e-03,  3.93858003e-03,  3.69279185e-01,  7.25451351e-03,
        2.60330714e-03])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 2.30938267e-04, -5.25769272e-02, -1.63619243e-04,  0.00000000e+00,
       -2.09415785e-03, -6.03121305e-03, -3.77221069e-01, -1.18020584e-02,
       -1.88553856e-03])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-2.30938267e-04,  5.25769272e-02,  1.63619243e-04,  0.00000000e+00,
        2.09415785e-03,  6.03121305e-03,  3.77221069e-01,  1.18020584e-02,
        1.88553856e-03])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.00079867, -0.03734441, -0.00070381, -0.00033582, -0.00185683,
        0.03794563,  0.22824528,  0.06079716,  0.02634347])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([-0.00079867,  0.03734441,  0.00070381,  0.00033582,  0.00185683,
       -0.03794563, -0.22824528, -0.06079716, -0.02634347])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.        ,  0.00494173, -0.00031108,  0.        ,  0.0052617 ,
        0.01586173,  0.30650872,  0.00931946,  0.00040305])
2025-07-16 02:17:21 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:21 - shap - INFO - phi = array([ 0.        , -0.00494173,  0.00031108,  0.        , -0.0052617 ,
       -0.01586173, -0.30650872, -0.00931946, -0.00040305])
2025-07-16 02:17:21 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-0.00060537,  0.07294665,  0.00811377,  0.        , -0.00223005,
       -0.00489254,  0.25345095, -0.00193695, -0.00139336])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 0.00060537, -0.07294665, -0.00811377,  0.        ,  0.00223005,
        0.00489254, -0.25345095,  0.00193695,  0.00139336])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 2.82675167e-04, -4.78481193e-02, -1.59252193e-04,  0.00000000e+00,
       -1.99748632e-03, -2.95674642e-03, -3.99114738e-01, -3.63802562e-03,
        3.88804775e-03])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-2.82675167e-04,  4.78481193e-02,  1.59252193e-04,  0.00000000e+00,
        1.99748632e-03,  2.95674642e-03,  3.99114738e-01,  3.63802562e-03,
       -3.88804775e-03])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 2.81259922e-04, -5.41990432e-02, -1.59249827e-04,  0.00000000e+00,
       -2.02803211e-03, -3.12585045e-03, -3.93550464e-01, -1.34849346e-03,
        2.58622861e-03])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-2.81259922e-04,  5.41990432e-02,  1.59249827e-04,  0.00000000e+00,
        2.02803211e-03,  3.12585045e-03,  3.93550464e-01,  1.34849346e-03,
       -2.58622861e-03])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 2.61779132e-04, -4.63470021e-02, -1.60606409e-04,  0.00000000e+00,
       -2.00868621e-03, -3.22809699e-03, -3.98721429e-01, -1.83786675e-03,
        4.98263727e-04])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-2.61779132e-04,  4.63470021e-02,  1.60606409e-04,  0.00000000e+00,
        2.00868621e-03,  3.22809699e-03,  3.98721429e-01,  1.83786675e-03,
       -4.98263727e-04])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 0.        ,  0.0797046 , -0.00026479,  0.00032287,  0.00607462,
        0.01703036,  0.21097999,  0.02324554,  0.0048921 ])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 0.        , -0.0797046 ,  0.00026479, -0.00032287, -0.00607462,
       -0.01703036, -0.21097999, -0.02324554, -0.0048921 ])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-8.05115453e-04,  1.28113836e-01, -3.13755698e-04,  1.40429217e-04,
       -2.11605198e-03, -7.46456691e-03,  2.04622675e-01, -7.77396292e-03,
       -7.31195149e-05])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 8.05115453e-04, -1.28113836e-01,  3.13755698e-04, -1.40429217e-04,
        2.11605198e-03,  7.46456691e-03, -2.04622675e-01,  7.77396292e-03,
        7.31195149e-05])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 0.0006527 ,  0.01101893, -0.00113231,  0.        ,  0.00518099,
       -0.00070833,  0.30096993,  0.01012349, -0.00330459])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-0.0006527 , -0.01101893,  0.00113231,  0.        , -0.00518099,
        0.00070833, -0.30096993, -0.01012349,  0.00330459])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-0.00062374,  0.00896749, -0.00184307,  0.        , -0.0023759 ,
       -0.00545648,  0.31867825, -0.0033638 , -0.00147306])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 0.00062374, -0.00896749,  0.00184307,  0.        ,  0.0023759 ,
        0.00545648, -0.31867825,  0.0033638 ,  0.00147306])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 2.25464512e-04,  6.73185045e-02, -8.92106175e-04,  0.00000000e+00,
        5.53477657e-03,  1.13657463e-02,  2.55094148e-01,  4.69297180e-03,
       -2.73915572e-03])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-2.25464512e-04, -6.73185045e-02,  8.92106175e-04,  0.00000000e+00,
       -5.53477657e-03, -1.13657463e-02, -2.55094148e-01, -4.69297180e-03,
        2.73915572e-03])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 2.70489923e-04, -5.10173968e-02, -1.71818516e-04,  0.00000000e+00,
       -2.01883886e-03, -5.25005296e-03, -3.91161677e-01, -4.50179340e-03,
        2.30744309e-03])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-2.70489923e-04,  5.10173968e-02,  1.71818516e-04,  0.00000000e+00,
        2.01883886e-03,  5.25005296e-03,  3.91161677e-01,  4.50179340e-03,
       -2.30744309e-03])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 0.00072126, -0.01154789, -0.00258986,  0.        , -0.00171237,
       -0.00452525,  0.334773  ,  0.00196064, -0.00274917])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-0.00072126,  0.01154789,  0.00258986,  0.        ,  0.00171237,
        0.00452525, -0.334773  , -0.00196064,  0.00274917])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-0.00116242, -0.04969214, -0.0004964 ,  0.        , -0.00283106,
       -0.00991144,  0.13671972, -0.04103585, -0.00103936])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 0.00116242,  0.04969214,  0.0004964 ,  0.        ,  0.00283106,
        0.00991144, -0.13671972,  0.04103585,  0.00103936])
2025-07-16 02:17:22 - shap - INFO - num_full_subsets = 4
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([ 2.75904401e-04, -4.20162108e-02, -1.65552772e-04,  0.00000000e+00,
       -1.99505384e-03, -3.66159004e-03, -4.05881650e-01, -3.10273241e-03,
        5.00324042e-03])
2025-07-16 02:17:22 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:17:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:17:22 - shap - INFO - phi = array([-2.75904401e-04,  4.20162108e-02,  1.65552772e-04,  0.00000000e+00,
        1.99505384e-03,  3.66159004e-03,  4.05881650e-01,  3.10273241e-03,
       -5.00324042e-03])
