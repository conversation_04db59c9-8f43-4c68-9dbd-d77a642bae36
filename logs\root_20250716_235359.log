2025-07-16 23:54:01 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 23:54:01 - GUI - INFO - GUI界面初始化完成
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:54:18 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 23:54:18 - model_training - INFO - 准确率: 0.8500
2025-07-16 23:54:18 - model_training - INFO - AUC: 0.8590
2025-07-16 23:54:18 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:18 - model_training - INFO - 
[[ 9  4]
 [ 2 25]]
2025-07-16 23:54:18 - model_training - INFO - 
分类报告:
2025-07-16 23:54:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.69      0.75        13
           1       0.86      0.93      0.89        27

    accuracy                           0.85        40
   macro avg       0.84      0.81      0.82        40
weighted avg       0.85      0.85      0.85        40

2025-07-16 23:54:18 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 23:54:18 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 23:54:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8511
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:54:18 - model_training - INFO - 模型名称: Random Forest
2025-07-16 23:54:18 - model_training - INFO - 准确率: 0.8500
2025-07-16 23:54:18 - model_training - INFO - AUC: 0.9003
2025-07-16 23:54:18 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:18 - model_training - INFO - 
[[10  3]
 [ 3 24]]
2025-07-16 23:54:18 - model_training - INFO - 
分类报告:
2025-07-16 23:54:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.77      0.77      0.77        13
           1       0.89      0.89      0.89        27

    accuracy                           0.85        40
   macro avg       0.83      0.83      0.83        40
weighted avg       0.85      0.85      0.85        40

2025-07-16 23:54:18 - model_training - INFO - 训练时间: 0.10 秒
2025-07-16 23:54:18 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 23:54:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8626
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:54:18 - model_training - INFO - 模型名称: XGBoost
2025-07-16 23:54:18 - model_training - INFO - 准确率: 0.8000
2025-07-16 23:54:18 - model_training - INFO - AUC: 0.8547
2025-07-16 23:54:18 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:18 - model_training - INFO - 
[[ 9  4]
 [ 4 23]]
2025-07-16 23:54:18 - model_training - INFO - 
分类报告:
2025-07-16 23:54:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.69      0.69      0.69        13
           1       0.85      0.85      0.85        27

    accuracy                           0.80        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.80      0.80      0.80        40

2025-07-16 23:54:18 - model_training - INFO - 训练时间: 0.06 秒
2025-07-16 23:54:18 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 23:54:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8137
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:54:20 - model_training - INFO - 模型名称: LightGBM
2025-07-16 23:54:20 - model_training - INFO - 准确率: 0.8000
2025-07-16 23:54:20 - model_training - INFO - AUC: 0.8661
2025-07-16 23:54:20 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:20 - model_training - INFO - 
[[ 9  4]
 [ 4 23]]
2025-07-16 23:54:20 - model_training - INFO - 
分类报告:
2025-07-16 23:54:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.69      0.69      0.69        13
           1       0.85      0.85      0.85        27

    accuracy                           0.80        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.80      0.80      0.80        40

2025-07-16 23:54:20 - model_training - INFO - 训练时间: 1.52 秒
2025-07-16 23:54:20 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-16 23:54:20 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-16 23:54:20 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8165
2025-07-16 23:54:20 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:54:21 - model_training - INFO - 模型名称: CatBoost
2025-07-16 23:54:21 - model_training - INFO - 准确率: 0.7750
2025-07-16 23:54:21 - model_training - INFO - AUC: 0.8917
2025-07-16 23:54:21 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:21 - model_training - INFO - 
[[ 9  4]
 [ 5 22]]
2025-07-16 23:54:21 - model_training - INFO - 
分类报告:
2025-07-16 23:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.64      0.69      0.67        13
           1       0.85      0.81      0.83        27

    accuracy                           0.78        40
   macro avg       0.74      0.75      0.75        40
weighted avg       0.78      0.78      0.78        40

2025-07-16 23:54:21 - model_training - INFO - 训练时间: 1.17 秒
2025-07-16 23:54:21 - model_training - INFO - 模型 CatBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-07-16 23:54:21 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_feature_names.joblib
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8053
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:54:21 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 23:54:21 - model_training - INFO - 准确率: 0.8250
2025-07-16 23:54:21 - model_training - INFO - AUC: 0.8746
2025-07-16 23:54:21 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:21 - model_training - INFO - 
[[10  3]
 [ 4 23]]
2025-07-16 23:54:21 - model_training - INFO - 
分类报告:
2025-07-16 23:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.71      0.77      0.74        13
           1       0.88      0.85      0.87        27

    accuracy                           0.82        40
   macro avg       0.80      0.81      0.80        40
weighted avg       0.83      0.82      0.83        40

2025-07-16 23:54:21 - model_training - INFO - 训练时间: 0.00 秒
2025-07-16 23:54:21 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 23:54:21 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8383
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:54:21 - model_training - INFO - 模型名称: SVM
2025-07-16 23:54:21 - model_training - INFO - 准确率: 0.8750
2025-07-16 23:54:21 - model_training - INFO - AUC: 0.8974
2025-07-16 23:54:21 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:21 - model_training - INFO - 
[[11  2]
 [ 3 24]]
2025-07-16 23:54:21 - model_training - INFO - 
分类报告:
2025-07-16 23:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.85      0.81        13
           1       0.92      0.89      0.91        27

    accuracy                           0.88        40
   macro avg       0.85      0.87      0.86        40
weighted avg       0.88      0.88      0.88        40

2025-07-16 23:54:21 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 23:54:21 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 23:54:21 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8813
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:54:21 - model_training - INFO - 模型名称: KNN
2025-07-16 23:54:21 - model_training - INFO - 准确率: 0.8250
2025-07-16 23:54:21 - model_training - INFO - AUC: 0.8647
2025-07-16 23:54:21 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:21 - model_training - INFO - 
[[ 8  5]
 [ 2 25]]
2025-07-16 23:54:21 - model_training - INFO - 
分类报告:
2025-07-16 23:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.62      0.70        13
           1       0.83      0.93      0.88        27

    accuracy                           0.82        40
   macro avg       0.82      0.77      0.79        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 23:54:21 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 23:54:21 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-16 23:54:21 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8329
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:54:21 - model_training - INFO - 模型名称: Naive Bayes
2025-07-16 23:54:21 - model_training - INFO - 准确率: 0.7500
2025-07-16 23:54:21 - model_training - INFO - AUC: 0.8433
2025-07-16 23:54:21 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:21 - model_training - INFO - 
[[ 8  5]
 [ 5 22]]
2025-07-16 23:54:21 - model_training - INFO - 
分类报告:
2025-07-16 23:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.62      0.62      0.62        13
           1       0.81      0.81      0.81        27

    accuracy                           0.75        40
   macro avg       0.72      0.72      0.72        40
weighted avg       0.75      0.75      0.75        40

2025-07-16 23:54:21 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:54:21 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-07-16 23:54:21 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.7733
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:54:21 - model_training - INFO - 模型名称: Neural Network
2025-07-16 23:54:21 - model_training - INFO - 准确率: 0.7500
2025-07-16 23:54:21 - model_training - INFO - AUC: 0.8120
2025-07-16 23:54:21 - model_training - INFO - 混淆矩阵:
2025-07-16 23:54:21 - model_training - INFO - 
[[ 8  5]
 [ 5 22]]
2025-07-16 23:54:21 - model_training - INFO - 
分类报告:
2025-07-16 23:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.62      0.62      0.62        13
           1       0.81      0.81      0.81        27

    accuracy                           0.75        40
   macro avg       0.72      0.72      0.72        40
weighted avg       0.75      0.75      0.75        40

2025-07-16 23:54:21 - model_training - INFO - 训练时间: 0.54 秒
2025-07-16 23:54:21 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-07-16 23:54:21 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.7655
2025-07-16 23:54:21 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 23:54:21 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.251
2025-07-16 23:54:21 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:54:21 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.440
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.251
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.440
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.840, 多样性=0.160
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.811, 多样性=0.189
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.797, 多样性=0.203
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.830, 多样性=0.170
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.653, 多样性=0.347
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.746, 多样性=0.254
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.591, 多样性=0.409
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.473, 多样性=0.527
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.529, 多样性=0.471
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.909, 多样性=0.091
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.929, 多样性=0.071
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.971, 多样性=0.029
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.872, 多样性=0.128
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.923, 多样性=0.077
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.745, 多样性=0.255
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.742, 多样性=0.258
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.632, 多样性=0.368
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.983, 多样性=0.017
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.951, 多样性=0.049
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.804, 多样性=0.196
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.825, 多样性=0.175
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.617, 多样性=0.383
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.631, 多样性=0.369
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.652, 多样性=0.348
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.967, 多样性=0.033
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.868, 多样性=0.132
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.681, 多样性=0.319
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.699, 多样性=0.301
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.652, 多样性=0.348
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.875, 多样性=0.125
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.912, 多样性=0.088
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.753, 多样性=0.247
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.716, 多样性=0.284
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.727, 多样性=0.273
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.877, 多样性=0.123
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.746, 多样性=0.254
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.873, 多样性=0.127
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.568, 多样性=0.432
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.845, 多样性=0.155
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.834, 多样性=0.166
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.653, 多样性=0.347
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.741, 多样性=0.259
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.681, 多样性=0.319
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.431, 多样性=0.569
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.406
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO - 开始计算 10 个模型间的量化多样性指标...
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:21 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.361
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.361
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.347
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO -   熵多样性: 0.315
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始评估 120 个模型组合...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.229
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.024
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'XGBoost'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.842
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.229
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.536
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.229
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.024
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'LightGBM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.843
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.229
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.536
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.242
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.030
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'CatBoost'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.840
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.242
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.541
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.070
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.851
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.576
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.281
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.865
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.281
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.573
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.421
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.145
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.849
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.421
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.635
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.369
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.117
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.829
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.369
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.599
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.405
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.134
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.826
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.405
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.616
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.229
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.048
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'XGBoost', 'LightGBM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.827
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.229
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.528
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.242
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'XGBoost', 'CatBoost'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.823
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.242
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.532
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.314
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.053
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'XGBoost', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.834
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.314
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.574
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.317
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'XGBoost', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.849
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.317
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.583
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.470
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.151
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'XGBoost', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.833
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.470
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.651
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.384
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.098
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'XGBoost', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.813
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.384
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.598
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.384
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.098
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'XGBoost', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.810
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.384
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.597
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.242
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'LightGBM', 'CatBoost'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.824
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.242
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.533
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.314
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.053
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'LightGBM', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.835
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.314
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.575
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.317
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'LightGBM', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.850
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.317
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.583
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.470
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.151
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'LightGBM', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.834
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.470
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.652
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.384
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.098
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'LightGBM', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.814
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.384
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.599
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.384
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.098
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.263
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'LightGBM', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.811
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.384
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.597
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.308
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.057
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'CatBoost', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.832
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.308
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.304
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.014
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'CatBoost', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.846
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.304
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.575
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.441
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.111
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'CatBoost', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.830
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.441
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.635
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.092
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'CatBoost', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.810
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.593
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.092
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.284
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'CatBoost', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.807
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.592
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.323
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.049
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.857
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.323
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.590
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.448
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.059
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.841
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.448
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.644
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.370
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.115
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.821
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.370
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.595
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.441
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.047
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.387
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.818
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.441
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.630
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.369
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.114
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'SVM', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.855
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.369
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.612
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.361
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.101
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'SVM', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.835
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.361
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.598
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.465
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.112
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'SVM', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.833
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.465
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.649
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.460
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.078
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'KNN', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.819
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.460
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.639
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.529
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.023
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'KNN', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.816
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.529
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.673
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 良好的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.525
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.032
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.502
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'NaiveBayes', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.797
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.525
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.661
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 良好的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.192
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.023
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'LightGBM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.831
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.192
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.511
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.204
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.020
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'CatBoost'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.827
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.204
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.516
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.267
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.042
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.838
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.267
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.552
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.299
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.069
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.853
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.299
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.576
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.449
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.836
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.449
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.642
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.328
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.085
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.817
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.328
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.572
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.364
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.119
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.814
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.364
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.589
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.204
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.020
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'CatBoost'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.828
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.204
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.516
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.267
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.042
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.839
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.267
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.553
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.299
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.069
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.853
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.299
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.576
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.449
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.837
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.449
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.643
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.328
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.085
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.817
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.328
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.573
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.364
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.119
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.208
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.815
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.364
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.589
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.260
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.031
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.835
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.260
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.548
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.285
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.042
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.850
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.285
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.567
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.419
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.137
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.834
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.419
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.626
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.319
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.069
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.814
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.319
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.566
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.355
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.111
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.226
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.811
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.355
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.583
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.294
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.019
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.861
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.294
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.577
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.416
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.089
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.845
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.416
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.630
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.304
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.069
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.825
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.304
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.564
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.411
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.082
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.301
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.822
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.411
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.616
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.366
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.110
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'SVM', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.859
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.366
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.613
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.324
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.050
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'SVM', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.839
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.324
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.581
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.464
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.111
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.313
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'SVM', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.836
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.464
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.650
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.420
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.072
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'KNN', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.823
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.420
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.621
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.525
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'KNN', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.820
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.525
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.673
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 良好的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.486
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.074
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.389
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'NaiveBayes', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.800
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.486
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.643
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.172
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.009
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'CatBoost'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.812
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.172
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.492
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.247
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.062
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.823
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.247
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.535
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.304
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.102
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.837
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.304
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.466
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.216
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.821
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.466
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.644
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.311
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.107
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.801
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.311
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.556
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.311
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.107
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.799
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.311
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.555
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.241
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.047
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.819
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.241
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.530
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.290
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.082
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.833
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.290
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.562
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.437
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.188
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.817
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.437
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.627
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.302
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.089
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.797
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.302
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.550
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.302
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.089
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.795
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.302
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.548
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.311
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'Logistic', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.844
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.311
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.578
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.446
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.134
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'Logistic', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.828
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.446
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.637
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.299
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.068
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'Logistic', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.808
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.299
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.554
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.370
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.059
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'Logistic', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.806
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.370
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.588
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.420
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.147
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'SVM', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.843
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.420
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.631
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.343
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.053
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'SVM', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.823
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.343
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.583
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.447
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.094
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'SVM', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.820
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.447
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.634
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.452
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.119
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'KNN', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.807
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.452
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.629
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.521
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.099
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'KNN', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.804
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.521
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.663
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 良好的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.448
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.087
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'NaiveBayes', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.784
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.448
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.616
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.241
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.047
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'Logistic'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.820
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.241
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.531
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.290
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.082
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.834
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.290
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.562
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.437
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.188
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.818
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.437
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.627
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.302
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.089
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.798
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.302
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.550
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.302
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.089
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.178
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.796
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.302
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.549
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.311
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'Logistic', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.845
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.311
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.578
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.446
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.134
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'Logistic', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.829
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.446
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.638
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.299
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.068
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'Logistic', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.809
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.299
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.554
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.370
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.059
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.291
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'Logistic', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.807
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.370
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.589
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.420
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.147
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'SVM', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.844
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.420
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.632
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.343
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.053
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'SVM', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.824
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.343
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.584
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.447
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.094
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.376
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'SVM', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.821
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.447
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.634
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.452
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.119
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'KNN', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.808
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.452
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.630
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.521
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.099
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'KNN', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.805
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.521
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.663
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 良好的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.448
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.087
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.386
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'NaiveBayes', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.785
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.448
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.616
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.279
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.026
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'Logistic', 'SVM'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.842
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.279
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.560
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.398
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.108
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'Logistic', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.826
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.398
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.612
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.272
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.051
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'Logistic', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.806
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.272
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.539
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.343
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.073
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.254
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'Logistic', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.803
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.343
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.573
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.365
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.106
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'SVM', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.840
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.365
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.602
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.308
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.030
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'SVM', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.820
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.308
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.564
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.412
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.119
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.315
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'SVM', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.817
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.412
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.615
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.402
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.078
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'KNN', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.804
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.402
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.603
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.471
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.093
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'KNN', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.801
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.471
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.636
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.418
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.108
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.342
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.570
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'NaiveBayes', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.781
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.418
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.600
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.321
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.076
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.266
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'SVM', 'KNN'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.851
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.321
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.586
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.252
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.022
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'SVM', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.831
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.252
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.542
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.427
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.128
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.580
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'SVM', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.828
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.427
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.628
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.333
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.086
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.221
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'KNN', 'NaiveBayes'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.815
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.333
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.574
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   平均多样性: 0.474
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.060
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最小多样性: 0.429
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   最大多样性: 0.558
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'KNN', 'NeuralNet'):
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     性能得分: 0.812
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性得分: 0.474
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     综合得分: 0.643
2025-07-16 23:54:22 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   平均多样性: 0.408
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.144
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最小多样性: 0.221
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最大多样性: 0.570
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'NaiveBayes', 'NeuralNet'):
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     性能得分: 0.792
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性得分: 0.408
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合得分: 0.600
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   平均多样性: 0.295
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.039
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最小多样性: 0.266
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最大多样性: 0.350
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   组合 ('SVM', 'KNN', 'NaiveBayes'):
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     性能得分: 0.829
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性得分: 0.295
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合得分: 0.562
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   平均多样性: 0.468
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.143
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最小多样性: 0.266
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最大多样性: 0.580
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   组合 ('SVM', 'KNN', 'NeuralNet'):
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     性能得分: 0.827
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性得分: 0.468
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合得分: 0.647
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   平均多样性: 0.473
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.145
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最大多样性: 0.580
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   组合 ('SVM', 'NaiveBayes', 'NeuralNet'):
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     性能得分: 0.807
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性得分: 0.473
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合得分: 0.640
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   平均多样性: 0.493
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.101
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最小多样性: 0.350
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最大多样性: 0.570
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   组合 ('KNN', 'NaiveBayes', 'NeuralNet'):
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     性能得分: 0.791
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性得分: 0.493
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合得分: 0.642
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 最优组合排序完成，共评估 120 个组合
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   性能得分: 0.8165
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   多样性得分: 0.5290
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   综合得分: 0.6728
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   多样性等级: 良好的多样性
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 开始计算 10 个模型间的量化多样性指标...
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.988 (多样性: 0.012)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.888 (多样性: 0.112)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.768 (多样性: 0.232)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.263
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.605 (多样性: 0.395)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.387
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.420 (多样性: 0.580)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.527
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.409 (多样性: 0.591)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.502
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.886 (多样性: 0.114)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.208
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.226
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.301
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.313
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.487 (多样性: 0.513)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.462 (多样性: 0.538)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.519
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.389
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.588 (多样性: 0.412)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.430 (多样性: 0.570)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.497
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.000
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.160
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.946 (多样性: 0.054)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.178
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.291
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.376
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.286 (多样性: 0.714)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.619
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.544 (多样性: 0.456)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.386
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.967 (多样性: 0.033)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.254
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.670 (多样性: 0.330)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.543 (多样性: 0.457)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.424 (多样性: 0.576)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.512
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.862 (多样性: 0.138)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.610 (多样性: 0.390)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.342
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.780 (多样性: 0.220)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.689 (多样性: 0.311)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.545 (多样性: 0.455)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.429
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.498 (多样性: 0.502)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.434
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.722 (多样性: 0.278)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.385 (多样性: 0.615)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.386 (多样性: 0.614)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.580
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.586 (多样性: 0.414)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.350
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.472 (多样性: 0.528)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.339 (多样性: 0.661)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.558
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     Q统计量: 0.455 (多样性: 0.545)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     相关系数: 0.316 (多样性: 0.684)
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -     综合多样性: 0.570
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   平均多样性: 0.361
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.125
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最小多样性: 0.160
2025-07-16 23:54:23 - quantified_diversity_evaluator - INFO -   最大多样性: 0.619
2025-07-16 23:55:04 - model_ensemble - INFO - ============================================================
2025-07-16 23:55:04 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 23:55:04 - model_ensemble - INFO - ============================================================
2025-07-16 23:55:04 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-16 23:55:04 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 23:55:04 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 23:55:04 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 23:55:04 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 23:55:04 - model_training - INFO - 准确率: 0.8500
2025-07-16 23:55:04 - model_training - INFO - AUC: 0.8590
2025-07-16 23:55:04 - model_training - INFO - 混淆矩阵:
2025-07-16 23:55:04 - model_training - INFO - 
[[ 9  4]
 [ 2 25]]
2025-07-16 23:55:04 - model_training - INFO - 
分类报告:
2025-07-16 23:55:04 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.69      0.75        13
           1       0.86      0.93      0.89        27

    accuracy                           0.85        40
   macro avg       0.84      0.81      0.82        40
weighted avg       0.85      0.85      0.85        40

2025-07-16 23:55:04 - model_training - INFO - 训练时间: 0.00 秒
2025-07-16 23:55:04 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 23:55:04 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-07-16 23:55:04 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 23:55:04 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 23:55:04 - model_training - INFO - 模型名称: KNN
2025-07-16 23:55:04 - model_training - INFO - 准确率: 0.8250
2025-07-16 23:55:04 - model_training - INFO - AUC: 0.8647
2025-07-16 23:55:04 - model_training - INFO - 混淆矩阵:
2025-07-16 23:55:04 - model_training - INFO - 
[[ 8  5]
 [ 2 25]]
2025-07-16 23:55:04 - model_training - INFO - 
分类报告:
2025-07-16 23:55:04 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.62      0.70        13
           1       0.83      0.93      0.88        27

    accuracy                           0.82        40
   macro avg       0.82      0.77      0.79        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 23:55:04 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 23:55:04 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-16 23:55:04 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-16 23:55:04 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 23:55:04 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-07-16 23:55:05 - model_training - INFO - 模型名称: Neural Network
2025-07-16 23:55:05 - model_training - INFO - 准确率: 0.7500
2025-07-16 23:55:05 - model_training - INFO - AUC: 0.8120
2025-07-16 23:55:05 - model_training - INFO - 混淆矩阵:
2025-07-16 23:55:05 - model_training - INFO - 
[[ 8  5]
 [ 5 22]]
2025-07-16 23:55:05 - model_training - INFO - 
分类报告:
2025-07-16 23:55:05 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.62      0.62      0.62        13
           1       0.81      0.81      0.81        27

    accuracy                           0.75        40
   macro avg       0.72      0.72      0.72        40
weighted avg       0.75      0.75      0.75        40

2025-07-16 23:55:05 - model_training - INFO - 训练时间: 0.53 秒
2025-07-16 23:55:05 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-07-16 23:55:05 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-07-16 23:55:05 - model_ensemble - INFO -   NeuralNet 训练完成
2025-07-16 23:55:05 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-16 23:55:05 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 23:55:05 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 23:55:08 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8116
2025-07-16 23:55:08 - model_ensemble - INFO - ============================================================
2025-07-16 23:55:08 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 23:55:08 - model_ensemble - INFO - ============================================================
2025-07-16 23:55:08 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 23:55:08 - model_ensemble - INFO - 最佳F1分数: 0.8116
2025-07-16 23:55:08 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-16 23:55:08 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 23:55:08 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8328, 召回率: 0.8250, F1: 0.8116, AUC: 0.9174
2025-07-16 23:55:08 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 23:55:08 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_235508.joblib
2025-07-16 23:55:08 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 23:55:08 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-16 23:55:08 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 23:55:08 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 23:55:08 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 23:55:08 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-16 23:55:08 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.21300879,  0.00397333, -0.01365495,  0.00107895,  0.06208491,
        0.03901961, -0.00663334, -0.00415911])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.21300879, -0.00397333,  0.01365495, -0.00107895, -0.06208491,
       -0.03901961,  0.00663334,  0.00415911])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.17608722,  0.07344425,  0.03397935,  0.01574728,  0.0678295 ,
        0.07811011, -0.00544481,  0.01086325])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.17608722, -0.07344425, -0.03397935, -0.01574728, -0.0678295 ,
       -0.07811011,  0.00544481, -0.01086325])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.02701915,  0.0285787 , -0.03261264, -0.00686126, -0.01198093,
       -0.03796339,  0.02931497, -0.00415023])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.02701915, -0.0285787 ,  0.03261264,  0.00686126,  0.01198093,
        0.03796339, -0.02931497,  0.00415023])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.01716558,  0.04031438,  0.03257985,  0.02769467, -0.04075497,
       -0.09598678, -0.01095569, -0.00906841])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.01716558, -0.04031438, -0.03257985, -0.02769467,  0.04075497,
        0.09598678,  0.01095569,  0.00906841])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([0.24004216, 0.04342054, 0.03582361, 0.00161178, 0.01510307,
       0.14089622, 0.00722584, 0.02933822])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.24004216, -0.04342054, -0.03582361, -0.00161178, -0.01510307,
       -0.14089622, -0.00722584, -0.02933822])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.0367936 , -0.10769316, -0.02524413, -0.02211035,  0.05247666,
       -0.04341183,  0.01079471, -0.00601749])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.0367936 ,  0.10769316,  0.02524413,  0.02211035, -0.05247666,
        0.04341183, -0.01079471,  0.00601749])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.05888143, -0.07288315, -0.00735284,  0.00524223, -0.03826645,
       -0.04385669,  0.00083655, -0.00423827])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.05888143,  0.07288315,  0.00735284, -0.00524223,  0.03826645,
        0.04385669, -0.00083655,  0.00423827])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.10649125,  0.07894046,  0.05422025,  0.00414076, -0.17839984,
        0.10086386,  0.00433951,  0.03278558])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.10649125, -0.07894046, -0.05422025, -0.00414076,  0.17839984,
       -0.10086386, -0.00433951, -0.03278558])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.00677163,  0.00447154, -0.06610928,  0.00177568, -0.02802564,
       -0.0523099 ,  0.0026175 , -0.00384817])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.00677163, -0.00447154,  0.06610928, -0.00177568,  0.02802564,
        0.0523099 , -0.0026175 ,  0.00384817])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.02327586,  0.07448079,  0.03932164,  0.00546154,  0.01016111,
       -0.06119954,  0.01206281, -0.00297496])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.02327586, -0.07448079, -0.03932164, -0.00546154, -0.01016111,
        0.06119954, -0.01206281,  0.00297496])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.09858829,  0.02555456, -0.03411306,  0.00504307,  0.01341026,
        0.01677732, -0.04422484,  0.0019421 ])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.09858829, -0.02555456,  0.03411306, -0.00504307, -0.01341026,
       -0.01677732,  0.04422484, -0.0019421 ])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.06772065, -0.12116411, -0.01852125,  0.01813181,  0.05053953,
       -0.02900821, -0.01593392,  0.00368463])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.06772065,  0.12116411,  0.01852125, -0.01813181, -0.05053953,
        0.02900821,  0.01593392, -0.00368463])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.06215558, -0.07072308, -0.04937457,  0.00154083,  0.00996138,
       -0.04287573, -0.00154796, -0.00421002])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.06215558,  0.07072308,  0.04937457, -0.00154083, -0.00996138,
        0.04287573,  0.00154796,  0.00421002])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.00556754, -0.05531954, -0.04843805,  0.02280557, -0.0276282 ,
       -0.06565128, -0.0001939 , -0.00407117])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.00556754,  0.05531954,  0.04843805, -0.02280557,  0.0276282 ,
        0.06565128,  0.0001939 ,  0.00407117])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.0831615 ,  0.01864096,  0.10285785, -0.00660982,  0.01236072,
        0.11054563, -0.02277928, -0.01672847])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.0831615 , -0.01864096, -0.10285785,  0.00660982, -0.01236072,
       -0.11054563,  0.02277928,  0.01672847])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.06560282, -0.03694868,  0.02879591, -0.0028032 , -0.0680274 ,
       -0.07388852,  0.00227307, -0.00319501])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.06560282,  0.03694868, -0.02879591,  0.0028032 ,  0.0680274 ,
        0.07388852, -0.00227307,  0.00319501])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.01865454,  0.09308655,  0.05281879,  0.00074364,  0.02220351,
       -0.05267142,  0.05467443, -0.01385333])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.01865454, -0.09308655, -0.05281879, -0.00074364, -0.02220351,
        0.05267142, -0.05467443,  0.01385333])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.04048574,  0.06528417,  0.08410367, -0.00610816, -0.07666131,
       -0.06869214, -0.00452418,  0.03276814])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.04048574, -0.06528417, -0.08410367,  0.00610816,  0.07666131,
        0.06869214,  0.00452418, -0.03276814])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.02909203, -0.11934988, -0.03578954, -0.00476986,  0.01279539,
       -0.07361231,  0.00124535,  0.01249583])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.02909203,  0.11934988,  0.03578954,  0.00476986, -0.01279539,
        0.07361231, -0.00124535, -0.01249583])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.04841592,  0.03465958, -0.01810755,  0.02749139,  0.02336682,
       -0.01226358,  0.0248692 ,  0.00280166])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.04841592, -0.03465958,  0.01810755, -0.02749139, -0.02336682,
        0.01226358, -0.0248692 , -0.00280166])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.03274912, -0.06253924, -0.01659934, -0.00755229, -0.05081104,
       -0.04829096,  0.00051226, -0.00137035])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.03274912,  0.06253924,  0.01659934,  0.00755229,  0.05081104,
        0.04829096, -0.00051226,  0.00137035])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.01887249,  0.06987161,  0.03615489, -0.02221262,  0.01850347,
       -0.05462834,  0.01938209, -0.00881652])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.01887249, -0.06987161, -0.03615489,  0.02221262, -0.01850347,
        0.05462834, -0.01938209,  0.00881652])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.03482803,  0.02120342, -0.05066914,  0.00474035,  0.01679693,
       -0.02930046,  0.00437524, -0.00921942])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.03482803, -0.02120342,  0.05066914, -0.00474035, -0.01679693,
        0.02930046, -0.00437524,  0.00921942])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.07947451, -0.03564214, -0.0528013 ,  0.02079052,  0.01085358,
        0.18640026,  0.00627446, -0.01737236])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.07947451,  0.03564214,  0.0528013 , -0.02079052, -0.01085358,
       -0.18640026, -0.00627446,  0.01737236])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.06362995, -0.04400148, -0.0873673 , -0.01602674,  0.01045129,
        0.04077546,  0.00251752, -0.01148653])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.06362995,  0.04400148,  0.0873673 ,  0.01602674, -0.01045129,
       -0.04077546, -0.00251752,  0.01148653])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.05075982,  0.05102554,  0.05184839, -0.02336405,  0.05617842,
        0.11499198, -0.00298385, -0.0146694 ])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.05075982, -0.05102554, -0.05184839,  0.02336405, -0.05617842,
       -0.11499198,  0.00298385,  0.0146694 ])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.03581928,  0.03437298,  0.09942685, -0.02941277,  0.01444323,
        0.12157076, -0.01535113, -0.01577529])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.03581928, -0.03437298, -0.09942685,  0.02941277, -0.01444323,
       -0.12157076,  0.01535113,  0.01577529])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.00711467,  0.03803593,  0.07843386,  0.01982862,  0.0130509 ,
       -0.05197519,  0.00570475, -0.01336955])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.00711467, -0.03803593, -0.07843386, -0.01982862, -0.0130509 ,
        0.05197519, -0.00570475,  0.01336955])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.06852081,  0.00375812, -0.0581872 , -0.01512026,  0.01725609,
        0.02221071, -0.00436105, -0.01096627])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.06852081, -0.00375812,  0.0581872 ,  0.01512026, -0.01725609,
       -0.02221071,  0.00436105,  0.01096627])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.04176728, -0.08883885, -0.02602124, -0.00564918, -0.0058797 ,
       -0.05029183, -0.00017013, -0.00077799])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([0.04176728, 0.08883885, 0.02602124, 0.00564918, 0.0058797 ,
       0.05029183, 0.00017013, 0.00077799])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.04087225,  0.04888126, -0.02127499, -0.00204743,  0.00460513,
       -0.04752235,  0.01205067, -0.01274278])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.04087225, -0.04888126,  0.02127499,  0.00204743, -0.00460513,
        0.04752235, -0.01205067,  0.01274278])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.03672633,  0.05883466,  0.10362933,  0.00228239,  0.02472377,
        0.08202932, -0.04544029,  0.02564884])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.03672633, -0.05883466, -0.10362933, -0.00228239, -0.02472377,
       -0.08202932,  0.04544029, -0.02564884])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.04298922, -0.08201436, -0.03756663,  0.01197298,  0.01291414,
       -0.0451352 , -0.02671777, -0.00986102])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.04298922,  0.08201436,  0.03756663, -0.01197298, -0.01291414,
        0.0451352 ,  0.02671777,  0.00986102])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.1977871 , -0.00672514, -0.02239194, -0.01259296,  0.00367239,
        0.11104479,  0.01071682,  0.01332894])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.1977871 ,  0.00672514,  0.02239194,  0.01259296, -0.00367239,
       -0.11104479, -0.01071682, -0.01332894])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.00433095, -0.06990857, -0.04664109, -0.00286354,  0.01446592,
       -0.0749472 ,  0.00591406, -0.00999104])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.00433095,  0.06990857,  0.04664109,  0.00286354, -0.01446592,
        0.0749472 , -0.00591406,  0.00999104])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.03600283,  0.02609932, -0.02604127,  0.00283835,  0.01295749,
       -0.02876822, -0.02101148, -0.01195455])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([ 0.03600283, -0.02609932,  0.02604127, -0.00283835, -0.01295749,
        0.02876822,  0.02101148,  0.01195455])
2025-07-16 23:55:08 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:08 - shap - INFO - phi = array([-0.04210676, -0.04646878,  0.0001282 , -0.0142126 ,  0.02013297,
       -0.04741254,  0.00741195,  0.00167312])
2025-07-16 23:55:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:09 - shap - INFO - phi = array([ 0.04210676,  0.04646878, -0.0001282 ,  0.0142126 , -0.02013297,
        0.04741254, -0.00741195, -0.00167312])
2025-07-16 23:55:09 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:09 - shap - INFO - phi = array([-0.03556585, -0.03899162, -0.03955329, -0.01580765,  0.0043731 ,
       -0.04999493,  0.00342957, -0.00703729])
2025-07-16 23:55:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:09 - shap - INFO - phi = array([ 0.03556585,  0.03899162,  0.03955329,  0.01580765, -0.0043731 ,
        0.04999493, -0.00342957,  0.00703729])
2025-07-16 23:55:09 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:09 - shap - INFO - phi = array([0.15443446, 0.07852321, 0.00137329, 0.01050886, 0.07049389,
       0.04328513, 0.00785484, 0.08292912])
2025-07-16 23:55:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:09 - shap - INFO - phi = array([-0.15443446, -0.07852321, -0.00137329, -0.01050886, -0.07049389,
       -0.04328513, -0.00785484, -0.08292912])
2025-07-16 23:55:09 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:09 - shap - INFO - phi = array([ 0.07093426,  0.04375591, -0.00106317,  0.00465348, -0.15173009,
        0.07313736, -0.00812454, -0.01833442])
2025-07-16 23:55:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:09 - shap - INFO - phi = array([-0.07093426, -0.04375591,  0.00106317, -0.00465348,  0.15173009,
       -0.07313736,  0.00812454,  0.01833442])
2025-07-16 23:55:13 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-16 23:55:13 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 23:55:13 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 23:55:13 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 23:55:13 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 23:55:18 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 23:55:18 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-16 23:55:18 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 0.02231343,  0.00020174, -0.00100871,  0.        , -0.00181567,
        0.00020174,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-0.02231343, -0.00020174,  0.00100871,  0.        ,  0.00181567,
       -0.00020174,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 0.02231343,  0.00091107, -0.00206389,  0.00011369, -0.00149546,
        0.00011369,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-0.02231343, -0.00091107,  0.00206389, -0.00011369,  0.00149546,
       -0.00011369,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-0.06115669,  0.00223414,  0.00217315, -0.00011492, -0.004268  ,
        0.        , -0.00011492,  0.        ])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 0.06115669, -0.00223414, -0.00217315,  0.00011492,  0.004268  ,
        0.        ,  0.00011492,  0.        ])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 0.02240434, -0.00031257, -0.00172476, -0.00012104, -0.00035344,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-0.02240434,  0.00031257,  0.00172476,  0.00012104,  0.00035344,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 0.04118324, -0.00080697, -0.02081363,  0.        ,  0.00032989,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-0.04118324,  0.00080697,  0.02081363,  0.        , -0.00032989,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 2.23134318e-02, -8.06965656e-04, -8.06965656e-04, -8.72804875e-18,
       -8.06965656e-04, -1.39755873e-17, -2.36139233e-17, -1.38777878e-17])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-2.23134318e-02,  8.06965656e-04,  8.06965656e-04, -2.45108240e-17,
        8.06965656e-04, -2.63849449e-17, -1.77104425e-17, -2.08166817e-17])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 0.02231343,  0.        , -0.00121045,  0.        , -0.00121045,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-0.02231343,  0.        ,  0.00121045,  0.        ,  0.00121045,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-0.06115173,  0.00408437,  0.00113065,  0.        , -0.0053105 ,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 0.06115173, -0.00408437, -0.00113065,  0.        ,  0.0053105 ,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-6.13159771e-02,  1.63231081e-03,  9.73172510e-04,  0.00000000e+00,
       -2.73229761e-03,  6.51914522e-05,  6.51914522e-05,  6.51914522e-05])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 6.13159771e-02, -1.63231081e-03, -9.73172510e-04,  0.00000000e+00,
        2.73229761e-03, -6.51914522e-05, -6.51914522e-05, -6.51914522e-05])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 2.24916112e-02, -5.63733619e-04, -3.16354484e-03, -3.61992205e-04,
        1.40949766e-03,  8.06965656e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-2.24916112e-02,  5.63733619e-04,  3.16354484e-03,  3.61992205e-04,
       -1.40949766e-03, -8.06965656e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 0.02231343,  0.00040348, -0.00080697,  0.        , -0.00201741,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-0.02231343, -0.00040348,  0.00080697,  0.        ,  0.00201741,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([-0.06115957,  0.00369495, -0.00044879,  0.        , -0.00333381,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:18 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:18 - shap - INFO - phi = array([ 0.06115957, -0.00369495,  0.00044879,  0.        ,  0.00333381,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02241072, -0.00032135, -0.00312339, -0.00052358,  0.00145014,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02241072,  0.00032135,  0.00312339,  0.00052358, -0.00145014,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.06145129,  0.00051151,  0.00322945,  0.00019485, -0.00373173,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.06145129, -0.00051151, -0.00322945, -0.00019485,  0.00373173,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 2.24078317e-02,  2.60760408e-04,  9.28925898e-03,  4.96240754e-04,
       -1.25787899e-02,  1.72328582e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-2.24078317e-02, -2.60760408e-04, -9.28925898e-03, -4.96240754e-04,
        1.25787899e-02, -1.72328582e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.0224182 , -0.00011403, -0.00894246, -0.00070967,  0.00724049,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.0224182 ,  0.00011403,  0.00894246,  0.00070967, -0.00724049,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 2.24851871e-02, -1.93148868e-03, -3.25066553e-03, -2.36664608e-04,
        2.86661289e-03,  0.00000000e+00, -4.04463801e-05,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-2.24851871e-02,  1.93148868e-03,  3.25066553e-03,  2.36664608e-04,
       -2.86661289e-03,  0.00000000e+00,  4.04463801e-05,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.06157198,  0.0044701 ,  0.00310128,  0.00010944, -0.00724811,
       -0.00010794,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.06157198, -0.0044701 , -0.00310128, -0.00010944,  0.00724811,
        0.00010794,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02231343,  0.        , -0.00121045,  0.        , -0.00121045,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02231343,  0.        ,  0.00121045,  0.        ,  0.00121045,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 2.23134318e-02, -8.06965656e-04, -8.06965656e-04, -8.72804875e-18,
       -8.06965656e-04, -1.39755873e-17, -2.36139233e-17, -1.38777878e-17])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-2.23134318e-02,  8.06965656e-04,  8.06965656e-04, -2.45108240e-17,
        8.06965656e-04, -2.63849449e-17, -1.77104425e-17, -2.08166817e-17])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 2.23134318e-02,  1.09779541e-04,  1.67179351e-03,  2.92745443e-04,
       -4.42202910e-03, -7.31863608e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-2.23134318e-02, -1.09779541e-04, -1.67179351e-03, -2.92745443e-04,
        4.42202910e-03,  7.31863608e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02231343,  0.        ,  0.00119608,  0.        , -0.00361698,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02231343,  0.        , -0.00119608,  0.        ,  0.00361698,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02231343,  0.        , -0.00121045,  0.        , -0.00121045,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02231343,  0.        ,  0.00121045,  0.        ,  0.00121045,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-6.13012258e-02, -1.28552217e-03,  1.58162573e-03,  0.00000000e+00,
       -3.38113418e-04,  9.60185678e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 6.13012258e-02,  1.28552217e-03, -1.58162573e-03,  0.00000000e+00,
        3.38113418e-04, -9.60185678e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02252539,  0.00080999,  0.00559665,  0.0005981 , -0.00963759,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02252539, -0.00080999, -0.00559665, -0.0005981 ,  0.00963759,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 2.29545038e-02,  1.13319271e-03,  6.64020329e-03,  1.16527599e-03,
       -1.13418307e-02, -7.53064447e-04,  5.38927845e-05,  4.03613466e-05])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-2.29545038e-02, -1.13319271e-03, -6.64020329e-03, -1.16527599e-03,
        1.13418307e-02,  7.53064447e-04, -5.38927845e-05, -4.03613466e-05])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02233831, -0.00059261, -0.00981975, -0.00059804,  0.00856462,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02233831,  0.00059261,  0.00981975,  0.00059804, -0.00856462,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02231343,  0.        , -0.00121045,  0.        , -0.00121045,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02231343,  0.        ,  0.00121045,  0.        ,  0.00121045,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-6.22959302e-02, -3.86065449e-04,  2.31252744e-02,  1.41782738e-03,
       -2.34174233e-02,  7.01631783e-05,  2.38936999e-04,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 6.22959302e-02,  3.86065449e-04, -2.31252744e-02, -1.41782738e-03,
        2.34174233e-02, -7.01631783e-05, -2.38936999e-04,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02231605, -0.00082003,  0.00162309,  0.        , -0.00322657,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02231605,  0.00082003, -0.00162309,  0.        ,  0.00322657,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02231343,  0.        ,  0.00229388,  0.        , -0.00471477,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02231343,  0.        , -0.00229388,  0.        ,  0.00471477,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 2.23134318e-02, -8.06965656e-04, -8.06965656e-04, -8.72804875e-18,
       -8.06965656e-04, -1.39755873e-17, -2.36139233e-17, -1.38777878e-17])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-2.23134318e-02,  8.06965656e-04,  8.06965656e-04, -2.45108240e-17,
        8.06965656e-04, -2.63849449e-17, -1.77104425e-17, -2.08166817e-17])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 2.99971447e-02, -9.98226020e-03,  8.27152895e-03, -1.20050540e-03,
        8.96908804e-02,  1.88028290e-04, -1.68408872e-04, -6.79943505e-05])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-2.99971447e-02,  9.98226020e-03, -8.27152895e-03,  1.20050540e-03,
       -8.96908804e-02, -1.88028290e-04,  1.68408872e-04,  6.79943505e-05])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-6.11698067e-02,  1.90155567e-04,  3.10744468e-03,  7.52405516e-05,
       -3.45025110e-03,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 6.11698067e-02, -1.90155567e-04, -3.10744468e-03, -7.52405516e-05,
        3.45025110e-03,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 2.25323672e-02, -3.07502272e-03, -7.35847087e-03, -3.51133425e-04,
        8.09483959e-03,  4.99550168e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-2.25323672e-02,  3.07502272e-03,  7.35847087e-03,  3.51133425e-04,
       -8.09483959e-03, -4.99550168e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02231343,  0.        ,  0.00119608,  0.        , -0.00361698,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02231343,  0.        , -0.00119608,  0.        ,  0.00361698,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-6.16891071e-02,  1.10069936e-03, -5.24190468e-03, -7.97388111e-05,
        4.66283418e-03,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 6.16891071e-02, -1.10069936e-03,  5.24190468e-03,  7.97388111e-05,
       -4.66283418e-03,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-6.12469948e-02,  1.05358587e-03,  2.22685794e-04,  0.00000000e+00,
       -1.33828175e-03,  6.17878628e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 6.12469948e-02, -1.05358587e-03, -2.22685794e-04,  0.00000000e+00,
        1.33828175e-03, -6.17878628e-05,  0.00000000e+00,  0.00000000e+00])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02231343,  0.        ,  0.00119608,  0.        , -0.00361698,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02231343,  0.        , -0.00119608,  0.        ,  0.00361698,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - num_full_subsets = 4
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([ 0.02233213, -0.00020108, -0.00260154, -0.00020108,  0.0005641 ,
        0.        ,  0.        ,  0.        ])
2025-07-16 23:55:19 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 23:55:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 23:55:19 - shap - INFO - phi = array([-0.02233213,  0.00020108,  0.00260154,  0.00020108, -0.0005641 ,
        0.        ,  0.        ,  0.        ])
