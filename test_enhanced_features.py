#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强功能的脚本
验证最佳模型选择、完整分析流程等新功能
"""

import sys
import os
from pathlib import Path
import numpy as np
import pandas as pd
from sklearn.datasets import make_classification

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 生成二分类数据
    X, y = make_classification(
        n_samples=500,
        n_features=10,
        n_informative=8,
        n_redundant=2,
        n_clusters_per_class=1,
        class_sep=0.8,
        random_state=42
    )
    
    # 创建特征名称
    feature_names = [f'feature_{i+1}' for i in range(X.shape[1])]
    
    # 创建DataFrame
    df = pd.DataFrame(X, columns=feature_names)
    df['target'] = y
    
    # 保存数据
    test_data_path = 'test_binary_classification_data.csv'
    df.to_csv(test_data_path, index=False)
    
    print(f"测试数据已保存到: {test_data_path}")
    print(f"数据形状: {df.shape}")
    print(f"类别分布: {df['target'].value_counts().to_dict()}")
    
    return test_data_path

def test_model_training():
    """测试模型训练功能"""
    print("\n" + "="*60)
    print("测试模型训练功能")
    print("="*60)
    
    try:
        from data_preprocessing import load_and_preprocess_data
        from model_training import MODEL_TRAINERS
        
        # 创建测试数据
        test_data_path = create_test_data()
        
        # 数据预处理
        print("进行数据预处理...")
        X_train, X_test, y_train, y_test = load_and_preprocess_data(test_data_path)
        print(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
        
        # 训练几个快速的模型
        quick_models = ['DecisionTree', 'RandomForest', 'Logistic']
        
        for model_name in quick_models:
            print(f"训练模型: {model_name}")
            trainer = MODEL_TRAINERS[model_name]
            model = trainer.train_and_evaluate(X_train, y_train, X_test, y_test)
            print(f"  {model_name} 训练完成")
        
        print("✅ 模型训练测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模型训练测试失败: {e}")
        return False

def test_best_model_selection():
    """测试最佳模型选择功能"""
    print("\n" + "="*60)
    print("测试最佳模型选择功能")
    print("="*60)
    
    try:
        from best_model_selector import select_best_model_for_binary_classification
        
        # 运行最佳模型选择
        result = select_best_model_for_binary_classification(strategy='balanced')
        
        if result:
            print("✅ 最佳模型选择测试通过")
            print(f"推荐模型: {result['best_model']}")
            print(f"综合得分: {result['best_score']:.4f}")
            print(f"前三名模型:")
            for i, (model, score) in enumerate(result['top_models'][:3], 1):
                print(f"  {i}. {model}: {score:.4f}")
            return True
        else:
            print("❌ 最佳模型选择返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 最佳模型选择测试失败: {e}")
        return False

def test_complete_analysis_pipeline():
    """测试完整分析流程"""
    print("\n" + "="*60)
    print("测试完整分析流程")
    print("="*60)
    
    try:
        from binary_classification_pipeline import run_binary_classification_analysis
        
        # 创建测试数据
        test_data_path = create_test_data()
        
        # 运行完整分析（使用快速模型和禁用调优以节省时间）
        success = run_binary_classification_analysis(
            data_path=test_data_path,
            selected_models=['DecisionTree', 'RandomForest', 'Logistic'],
            strategy='balanced',
            enable_tuning=False,  # 禁用调优以节省时间
            enable_shap=True
        )
        
        if success:
            print("✅ 完整分析流程测试通过")
            return True
        else:
            print("❌ 完整分析流程测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 完整分析流程测试失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成功能"""
    print("\n" + "="*60)
    print("测试GUI集成功能")
    print("="*60)
    
    try:
        # 测试GUI功能模块导入
        from gui_functions import GUIFunctions
        print("✅ GUI功能模块导入成功")
        
        # 测试GUI主界面导入
        import gui_main
        print("✅ GUI主界面模块导入成功")
        
        print("✅ GUI集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def test_shap_analysis():
    """测试SHAP分析功能"""
    print("\n" + "="*60)
    print("测试SHAP分析功能")
    print("="*60)

    try:
        # 简单测试SHAP模块导入
        import shap
        print("✅ SHAP模块导入成功")

        # 检查是否有训练好的模型
        from config import CACHE_PATH

        model_files = list(CACHE_PATH.glob("*_results.joblib"))
        if model_files:
            print(f"✅ 找到 {len(model_files)} 个训练好的模型")
            return True
        else:
            print("⚠️  没有找到训练好的模型，但SHAP模块可用")
            return True

    except Exception as e:
        print(f"❌ SHAP分析测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n清理测试文件...")
    
    test_files = [
        'test_binary_classification_data.csv',
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"删除: {file_path}")

def main():
    """主测试函数"""
    print("🚀 开始测试增强功能")
    print("="*60)
    
    tests = [
        ("模型训练功能", test_model_training),
        ("最佳模型选择", test_best_model_selection),
        ("完整分析流程", test_complete_analysis_pipeline),
        ("GUI集成功能", test_gui_integration),
        ("SHAP分析功能", test_shap_analysis)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print("\n" + "="*60)
    print("测试结果摘要")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有增强功能测试通过！")
        success = True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        success = False
    
    # 清理测试文件
    cleanup_test_files()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
