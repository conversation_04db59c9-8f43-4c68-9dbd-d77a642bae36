#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志记录模块
提供统一的日志记录接口，支持控制台和文件输出
"""

import logging
import os
from pathlib import Path
import sys
from datetime import datetime

# 如果配置模块已经存在，则导入
try:
    from config import LOG_PATH, LOG_CONFIG
except ImportError:
    # 否则使用默认配置
    # 通过获取当前文件的路径确定项目根目录
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    LOG_PATH = PROJECT_ROOT / 'logs'
    LOG_PATH.mkdir(parents=True, exist_ok=True)
    LOG_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'date_format': '%Y-%m-%d %H:%M:%S'
    }

# 日志级别映射
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

def setup_logger(name, log_file=None, level=None, console_output=True):
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径，如果为None则只输出到控制台
        level: 日志级别，如果为None则使用配置文件中的级别
        console_output: 是否输出到控制台
        
    Returns:
        logging.Logger: 日志记录器对象
    """
    # 获取日志级别
    if level is None:
        level = LOG_CONFIG.get('level', 'INFO')
    
    # 将字符串转换为日志级别常量
    log_level = LOG_LEVELS.get(level.upper(), logging.INFO)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # 避免重复设置处理器
    if logger.handlers:
        return logger
    
    # 设置格式化器
    formatter = logging.Formatter(
        fmt=LOG_CONFIG.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
        datefmt=LOG_CONFIG.get('date_format', '%Y-%m-%d %H:%M:%S')
    )
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 如果需要控制台输出，添加控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger

def get_default_logger(name=None, console_output=True, include_timestamp=True):
    """
    获取默认日志记录器
    
    Args:
        name: 日志记录器名称，如果为None则使用调用模块的名称
        console_output: 是否输出到控制台
        include_timestamp: 是否在日志文件名中包含时间戳
        
    Returns:
        logging.Logger: 日志记录器对象
    """
    # 如果没有指定名称，使用调用者的模块名
    if name is None:
        import inspect
        frame = inspect.stack()[1]
        module = inspect.getmodule(frame[0])
        name = module.__name__ if module else 'root'
    
    # 确保日志目录存在
    LOG_PATH.mkdir(parents=True, exist_ok=True)
    
    # 创建日志文件名
    if include_timestamp:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = LOG_PATH / f"{name}_{timestamp}.log"
    else:
        log_file = LOG_PATH / f"{name}.log"
    
    # 设置日志记录器
    return setup_logger(name, log_file=str(log_file), console_output=console_output)

# 创建默认的根日志记录器
root_logger = get_default_logger('root')

def get_logger(name=None):
    """
    获取日志记录器的简便函数（兼容旧版本）

    Args:
        name: 日志记录器名称

    Returns:
        logging.Logger: 日志记录器对象
    """
    return get_default_logger(name, console_output=True, include_timestamp=False)

# 提供简便的日志记录函数
def debug(msg, *args, **kwargs):
    """记录DEBUG级别的日志"""
    root_logger.debug(msg, *args, **kwargs)

def info(msg, *args, **kwargs):
    """记录INFO级别的日志"""
    root_logger.info(msg, *args, **kwargs)

def warning(msg, *args, **kwargs):
    """记录WARNING级别的日志"""
    root_logger.warning(msg, *args, **kwargs)

def error(msg, *args, **kwargs):
    """记录ERROR级别的日志"""
    root_logger.error(msg, *args, **kwargs)

def critical(msg, *args, **kwargs):
    """记录CRITICAL级别的日志"""
    root_logger.critical(msg, *args, **kwargs)

def exception(msg, *args, **kwargs):
    """记录异常信息"""
    root_logger.exception(msg, *args, **kwargs)

# 测试日志功能
if __name__ == "__main__":
    # 测试默认日志记录器
    logger = get_default_logger("test_logger")
    logger.debug("这是一条调试日志")
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    logger.critical("这是一条严重错误日志")
    
    try:
        1 / 0
    except Exception as e:
        logger.exception("捕获到异常:")
    
    # 测试模块级别的日志记录函数
    debug("模块级别的调试日志")
    info("模块级别的信息日志")
    warning("模块级别的警告日志")
    error("模块级别的错误日志")
    critical("模块级别的严重错误日志") 