2025-07-15 21:29:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:29:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:29:13 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:29:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:29:13 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:29:13 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 21:29:13 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:29:13 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:29:13 - model_training - INFO - 模型名称: Random Forest
2025-07-15 21:29:13 - model_training - INFO - 准确率: 0.9000
2025-07-15 21:29:13 - model_training - INFO - AUC: 0.9756
2025-07-15 21:29:13 - model_training - INFO - 混淆矩阵:
2025-07-15 21:29:13 - model_training - INFO - 
[[24  6]
 [ 0 30]]
2025-07-15 21:29:13 - model_training - INFO - 
分类报告:
2025-07-15 21:29:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.80      0.89        30
           1       0.83      1.00      0.91        30

    accuracy                           0.90        60
   macro avg       0.92      0.90      0.90        60
weighted avg       0.92      0.90      0.90        60

2025-07-15 21:29:13 - model_training - INFO - 训练时间: 0.10 秒
2025-07-15 21:29:13 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 21:29:13 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 21:29:13 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:29:13 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:29:13 - model_training - INFO - 模型名称: XGBoost
2025-07-15 21:29:13 - model_training - INFO - 准确率: 0.9333
2025-07-15 21:29:13 - model_training - INFO - AUC: 0.9733
2025-07-15 21:29:13 - model_training - INFO - 混淆矩阵:
2025-07-15 21:29:13 - model_training - INFO - 
[[28  2]
 [ 2 28]]
2025-07-15 21:29:13 - model_training - INFO - 
分类报告:
2025-07-15 21:29:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.93      0.93      0.93        30
           1       0.93      0.93      0.93        30

    accuracy                           0.93        60
   macro avg       0.93      0.93      0.93        60
weighted avg       0.93      0.93      0.93        60

2025-07-15 21:29:13 - model_training - INFO - 训练时间: 0.10 秒
2025-07-15 21:29:13 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 21:29:13 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 21:29:13 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:29:13 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:29:13 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:29:13 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:29:13 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:29:13 - model_ensemble - INFO -     voting_soft - 准确率: 0.9500, F1: 0.9500
2025-07-15 21:29:13 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:29:13 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:29:13 - model_ensemble - INFO -     voting_hard - 准确率: 0.9333, F1: 0.9333
2025-07-15 21:29:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:29:13 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:29:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:29:13 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 21:29:13 - model_ensemble - INFO - 最佳F1分数: 0.9500
2025-07-15 21:29:13 - model_ensemble - INFO - 最佳准确率: 0.9500
2025-07-15 21:29:13 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:29:13 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9500, 精确率: 0.9505, 召回率: 0.9500, F1: 0.9500, AUC: 0.9778
2025-07-15 21:29:13 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9333, 精确率: 0.9333, 召回率: 0.9333, F1: 0.9333, AUC: 0.0000
2025-07-15 21:29:13 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:29:14 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_212913.joblib
2025-07-15 21:29:14 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 21:29:14 - safe_visualization - INFO - Summary report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_summary_report.txt
2025-07-15 21:29:14 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 21:29:14 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 21:29:14 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 21:29:19 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 21:29:19 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 21:29:26 - model_ensemble - INFO -   voting_hard SHAP分析完成
