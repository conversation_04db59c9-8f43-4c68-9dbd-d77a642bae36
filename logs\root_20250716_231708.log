2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:17:08 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 23:17:08 - model_training - INFO - 准确率: 0.7867
2025-07-16 23:17:08 - model_training - INFO - AUC: 0.8772
2025-07-16 23:17:08 - model_training - INFO - 混淆矩阵:
2025-07-16 23:17:08 - model_training - INFO - 
[[58 17]
 [15 60]]
2025-07-16 23:17:08 - model_training - INFO - 
分类报告:
2025-07-16 23:17:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.77      0.78        75
           1       0.78      0.80      0.79        75

    accuracy                           0.79       150
   macro avg       0.79      0.79      0.79       150
weighted avg       0.79      0.79      0.79       150

2025-07-16 23:17:08 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:17:08 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8093
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:17:08 - model_training - INFO - 模型名称: Random Forest
2025-07-16 23:17:08 - model_training - INFO - 准确率: 0.8933
2025-07-16 23:17:08 - model_training - INFO - AUC: 0.9476
2025-07-16 23:17:08 - model_training - INFO - 混淆矩阵:
2025-07-16 23:17:08 - model_training - INFO - 
[[65 10]
 [ 6 69]]
2025-07-16 23:17:08 - model_training - INFO - 
分类报告:
2025-07-16 23:17:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.92      0.87      0.89        75
           1       0.87      0.92      0.90        75

    accuracy                           0.89       150
   macro avg       0.89      0.89      0.89       150
weighted avg       0.89      0.89      0.89       150

2025-07-16 23:17:08 - model_training - INFO - 训练时间: 0.13 秒
2025-07-16 23:17:08 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9070
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:17:08 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 23:17:08 - model_training - INFO - 准确率: 0.8133
2025-07-16 23:17:08 - model_training - INFO - AUC: 0.8759
2025-07-16 23:17:08 - model_training - INFO - 混淆矩阵:
2025-07-16 23:17:08 - model_training - INFO - 
[[64 11]
 [17 58]]
2025-07-16 23:17:08 - model_training - INFO - 
分类报告:
2025-07-16 23:17:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.85      0.82        75
           1       0.84      0.77      0.81        75

    accuracy                           0.81       150
   macro avg       0.82      0.81      0.81       150
weighted avg       0.82      0.81      0.81       150

2025-07-16 23:17:08 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:17:08 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8292
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:17:08 - model_training - INFO - 模型名称: SVM
2025-07-16 23:17:08 - model_training - INFO - 准确率: 0.9000
2025-07-16 23:17:08 - model_training - INFO - AUC: 0.9633
2025-07-16 23:17:08 - model_training - INFO - 混淆矩阵:
2025-07-16 23:17:08 - model_training - INFO - 
[[65 10]
 [ 5 70]]
2025-07-16 23:17:08 - model_training - INFO - 
分类报告:
2025-07-16 23:17:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.93      0.87      0.90        75
           1       0.88      0.93      0.90        75

    accuracy                           0.90       150
   macro avg       0.90      0.90      0.90       150
weighted avg       0.90      0.90      0.90       150

2025-07-16 23:17:08 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 23:17:08 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9160
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:17:08 - model_training - INFO - 模型名称: XGBoost
2025-07-16 23:17:08 - model_training - INFO - 准确率: 0.8667
2025-07-16 23:17:08 - model_training - INFO - AUC: 0.9488
2025-07-16 23:17:08 - model_training - INFO - 混淆矩阵:
2025-07-16 23:17:08 - model_training - INFO - 
[[66  9]
 [11 64]]
2025-07-16 23:17:08 - model_training - INFO - 
分类报告:
2025-07-16 23:17:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.88      0.87        75
           1       0.88      0.85      0.86        75

    accuracy                           0.87       150
   macro avg       0.87      0.87      0.87       150
weighted avg       0.87      0.87      0.87       150

2025-07-16 23:17:08 - model_training - INFO - 训练时间: 0.10 秒
2025-07-16 23:17:08 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8872
2025-07-16 23:17:08 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 23:17:08 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.119
2025-07-16 23:17:08 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:17:08 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.259
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.119
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.259
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.828, 多样性=0.172
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.699, 多样性=0.301
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.762, 多样性=0.238
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.795, 多样性=0.205
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.850, 多样性=0.150
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.938, 多样性=0.062
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.918, 多样性=0.082
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.794, 多样性=0.206
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.780, 多样性=0.220
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.923, 多样性=0.077
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.379
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.300
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 3, 'linear': 2}
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 5 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.927 (多样性: 0.073)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.147
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.087 (多样性: 0.913)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.707 (多样性: 0.293)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.307
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.705 (多样性: 0.295)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.213
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.093 (多样性: 0.907)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.578 (多样性: 0.422)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.418
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.874 (多样性: 0.126)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.167
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.073 (多样性: 0.927)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.667 (多样性: 0.333)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.340
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.728 (多样性: 0.272)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.073 (多样性: 0.927)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.601 (多样性: 0.399)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.407
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.830 (多样性: 0.170)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.160
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.067 (多样性: 0.933)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.687 (多样性: 0.313)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.348
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.930 (多样性: 0.070)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.087
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.826 (多样性: 0.174)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.270
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.870 (多样性: 0.130)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.120
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.763 (多样性: 0.237)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.310
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.857 (多样性: 0.143)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.153
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.067 (多样性: 0.933)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.702 (多样性: 0.298)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.335
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.829 (多样性: 0.171)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.160
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.080 (多样性: 0.920)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.680 (多样性: 0.320)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.347
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   SVM_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.888 (多样性: 0.112)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.113
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.777 (多样性: 0.223)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.300
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.338
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.044
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.270
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.418
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.338
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.129
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   熵多样性: 0.261
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始评估 10 个模型组合...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.927 (多样性: 0.073)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.147
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.087 (多样性: 0.913)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.707 (多样性: 0.293)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.307
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.705 (多样性: 0.295)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.213
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.093 (多样性: 0.907)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.578 (多样性: 0.422)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.418
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.830 (多样性: 0.170)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.160
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.067 (多样性: 0.933)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.687 (多样性: 0.313)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.348
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.358
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.307
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.418
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'Logistic'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.848
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.358
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.603
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.927 (多样性: 0.073)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.147
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.087 (多样性: 0.913)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.707 (多样性: 0.293)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.307
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.874 (多样性: 0.126)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.167
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.073 (多样性: 0.927)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.667 (多样性: 0.333)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.340
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.930 (多样性: 0.070)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.087
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.826 (多样性: 0.174)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.270
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.306
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.029
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.270
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.340
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'SVM'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.877
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.306
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.592
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.927 (多样性: 0.073)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.147
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.087 (多样性: 0.913)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.707 (多样性: 0.293)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.307
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.728 (多样性: 0.272)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.073 (多样性: 0.927)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.601 (多样性: 0.399)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.407
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.870 (多样性: 0.130)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.120
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.763 (多样性: 0.237)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.310
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.342
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.046
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.307
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.407
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'XGBoost'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.868
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.342
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.605
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.705 (多样性: 0.295)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.213
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.093 (多样性: 0.907)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.578 (多样性: 0.422)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.418
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.874 (多样性: 0.126)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.167
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.073 (多样性: 0.927)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.667 (多样性: 0.333)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.340
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.857 (多样性: 0.143)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.153
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.067 (多样性: 0.933)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.702 (多样性: 0.298)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.335
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.364
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.038
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.335
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.418
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'SVM'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.852
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.364
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.608
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.705 (多样性: 0.295)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.213
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.093 (多样性: 0.907)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.578 (多样性: 0.422)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.418
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.728 (多样性: 0.272)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.073 (多样性: 0.927)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.601 (多样性: 0.399)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.407
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.829 (多样性: 0.171)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.160
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.080 (多样性: 0.920)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.680 (多样性: 0.320)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.347
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.391
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.031
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.347
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.418
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'XGBoost'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.842
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.391
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.616
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.874 (多样性: 0.126)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.167
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.073 (多样性: 0.927)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.667 (多样性: 0.333)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.340
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.728 (多样性: 0.272)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.073 (多样性: 0.927)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.601 (多样性: 0.399)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.407
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   SVM_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.888 (多样性: 0.112)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.113
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.777 (多样性: 0.223)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.300
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.349
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.044
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.300
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.407
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'SVM', 'XGBoost'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.871
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.349
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.610
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.830 (多样性: 0.170)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.160
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.067 (多样性: 0.933)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.687 (多样性: 0.313)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.348
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.930 (多样性: 0.070)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.087
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.826 (多样性: 0.174)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.270
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.857 (多样性: 0.143)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.153
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.067 (多样性: 0.933)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.702 (多样性: 0.298)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.335
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.318
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.034
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.270
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.348
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'SVM'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.884
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.318
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.601
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.830 (多样性: 0.170)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.160
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.067 (多样性: 0.933)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.687 (多样性: 0.313)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.348
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.870 (多样性: 0.130)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.120
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.763 (多样性: 0.237)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.310
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.829 (多样性: 0.171)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.160
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.080 (多样性: 0.920)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.680 (多样性: 0.320)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.347
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.335
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.018
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.310
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.348
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'XGBoost'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.874
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.335
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.605
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.930 (多样性: 0.070)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.087
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.826 (多样性: 0.174)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.270
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.870 (多样性: 0.130)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.120
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.763 (多样性: 0.237)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.310
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   SVM_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.888 (多样性: 0.112)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.113
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.777 (多样性: 0.223)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.300
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.293
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.017
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.270
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.310
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'SVM', 'XGBoost'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.903
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.293
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.598
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.857 (多样性: 0.143)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.153
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.067 (多样性: 0.933)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.702 (多样性: 0.298)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.335
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.829 (多样性: 0.171)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.160
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.080 (多样性: 0.920)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.680 (多样性: 0.320)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.347
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   SVM_vs_XGBoost:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.888 (多样性: 0.112)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.113
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.060 (多样性: 0.940)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.777 (多样性: 0.223)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.300
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.327
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.020
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.300
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.347
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'SVM', 'XGBoost'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.877
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.327
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.602
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 最优组合排序完成，共评估 10 个组合
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'Logistic', 'XGBoost']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   性能得分: 0.8419
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3908
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   综合得分: 0.6164
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:17:08 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 23:17:08 - model_training - INFO - 准确率: 0.7167
2025-07-16 23:17:08 - model_training - INFO - AUC: 0.7594
2025-07-16 23:17:08 - model_training - INFO - 混淆矩阵:
2025-07-16 23:17:08 - model_training - INFO - 
[[50 10]
 [24 36]]
2025-07-16 23:17:08 - model_training - INFO - 
分类报告:
2025-07-16 23:17:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.68      0.83      0.75        60
           1       0.78      0.60      0.68        60

    accuracy                           0.72       120
   macro avg       0.73      0.72      0.71       120
weighted avg       0.73      0.72      0.71       120

2025-07-16 23:17:08 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:17:08 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7279
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:17:08 - model_training - INFO - 模型名称: Random Forest
2025-07-16 23:17:08 - model_training - INFO - 准确率: 0.8417
2025-07-16 23:17:08 - model_training - INFO - AUC: 0.9265
2025-07-16 23:17:08 - model_training - INFO - 混淆矩阵:
2025-07-16 23:17:08 - model_training - INFO - 
[[52  8]
 [11 49]]
2025-07-16 23:17:08 - model_training - INFO - 
分类报告:
2025-07-16 23:17:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        60
           1       0.86      0.82      0.84        60

    accuracy                           0.84       120
   macro avg       0.84      0.84      0.84       120
weighted avg       0.84      0.84      0.84       120

2025-07-16 23:17:08 - model_training - INFO - 训练时间: 0.12 秒
2025-07-16 23:17:08 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8630
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:17:08 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 23:17:08 - model_training - INFO - 准确率: 0.7500
2025-07-16 23:17:08 - model_training - INFO - AUC: 0.8403
2025-07-16 23:17:08 - model_training - INFO - 混淆矩阵:
2025-07-16 23:17:08 - model_training - INFO - 
[[44 16]
 [14 46]]
2025-07-16 23:17:08 - model_training - INFO - 
分类报告:
2025-07-16 23:17:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.73      0.75        60
           1       0.74      0.77      0.75        60

    accuracy                           0.75       120
   macro avg       0.75      0.75      0.75       120
weighted avg       0.75      0.75      0.75       120

2025-07-16 23:17:08 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:17:08 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7726
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:17:08 - model_training - INFO - 模型名称: SVM
2025-07-16 23:17:08 - model_training - INFO - 准确率: 0.8750
2025-07-16 23:17:08 - model_training - INFO - AUC: 0.9378
2025-07-16 23:17:08 - model_training - INFO - 混淆矩阵:
2025-07-16 23:17:08 - model_training - INFO - 
[[56  4]
 [11 49]]
2025-07-16 23:17:08 - model_training - INFO - 
分类报告:
2025-07-16 23:17:08 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.93      0.88        60
           1       0.92      0.82      0.87        60

    accuracy                           0.88       120
   macro avg       0.88      0.88      0.87       120
weighted avg       0.88      0.88      0.87       120

2025-07-16 23:17:08 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 23:17:08 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8912
2025-07-16 23:17:08 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 23:17:08 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.133
2025-07-16 23:17:08 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:17:08 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.435
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.133
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.435
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.744, 多样性=0.256
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.520, 多样性=0.480
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.670, 多样性=0.330
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.761, 多样性=0.239
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.915, 多样性=0.085
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.723, 多样性=0.277
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.653
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 4 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.838 (多样性: 0.162)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.589 (多样性: 0.411)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.370
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.347 (多样性: 0.653)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.333
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.351 (多样性: 0.649)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.606
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.876 (多样性: 0.124)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.576 (多样性: 0.424)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.365
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.636 (多样性: 0.364)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.242
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.083 (多样性: 0.917)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.519 (多样性: 0.481)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.461
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.982 (多样性: 0.018)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.067
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.108 (多样性: 0.892)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.868 (多样性: 0.132)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.230
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.558 (多样性: 0.442)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.427
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.410
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.113
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.230
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.606
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.410
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.277
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   熵多样性: 0.340
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'Logistic', 'SVM']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合得分: 0.8423
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合得分: 0.4645
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合得分: 0.6206
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始评估 4 个模型组合...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.838 (多样性: 0.162)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.589 (多样性: 0.411)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.370
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.347 (多样性: 0.653)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.333
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.351 (多样性: 0.649)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.606
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.636 (多样性: 0.364)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.242
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.083 (多样性: 0.917)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.519 (多样性: 0.481)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.461
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.479
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.097
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.370
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.606
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'Logistic'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.788
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.479
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.633
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.838 (多样性: 0.162)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.117 (多样性: 0.883)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.589 (多样性: 0.411)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.370
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.876 (多样性: 0.124)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.576 (多样性: 0.424)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.365
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.982 (多样性: 0.018)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.067
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.108 (多样性: 0.892)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.868 (多样性: 0.132)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.230
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.322
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.065
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.230
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.370
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'RandomForest', 'SVM'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.827
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.322
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.575
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.347 (多样性: 0.653)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.333
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.351 (多样性: 0.649)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.606
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.876 (多样性: 0.124)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.208
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.576 (多样性: 0.424)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.365
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.558 (多样性: 0.442)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.427
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.466
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.102
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.365
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.606
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('DecisionTree', 'Logistic', 'SVM'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.797
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.466
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.631
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.636 (多样性: 0.364)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.242
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.083 (多样性: 0.917)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.519 (多样性: 0.481)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.461
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.982 (多样性: 0.018)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.067
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.108 (多样性: 0.892)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.868 (多样性: 0.132)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.230
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     相关系数: 0.558 (多样性: 0.442)
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合多样性: 0.427
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   平均多样性: 0.373
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.102
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最小多样性: 0.230
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   最大多样性: 0.461
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'SVM'):
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     性能得分: 0.842
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性得分: 0.373
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     综合得分: 0.607
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-07-16 23:17:08 - quantified_diversity_evaluator - INFO - 最优组合排序完成，共评估 4 个组合
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'RandomForest', 'Logistic']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   性能得分: 0.7878
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性得分: 0.4790
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   综合得分: 0.6334
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
