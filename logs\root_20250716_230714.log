2025-07-16 23:07:15 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 23:07:16 - GUI - INFO - GUI界面初始化完成
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:07:42 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 23:07:42 - model_training - INFO - 准确率: 0.8250
2025-07-16 23:07:42 - model_training - INFO - AUC: 0.9028
2025-07-16 23:07:42 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:42 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-07-16 23:07:42 - model_training - INFO - 
分类报告:
2025-07-16 23:07:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-07-16 23:07:42 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 23:07:42 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 23:07:42 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:07:42 - model_training - INFO - 模型名称: Random Forest
2025-07-16 23:07:42 - model_training - INFO - 准确率: 0.8250
2025-07-16 23:07:42 - model_training - INFO - AUC: 0.9015
2025-07-16 23:07:42 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:42 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-16 23:07:42 - model_training - INFO - 
分类报告:
2025-07-16 23:07:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 23:07:42 - model_training - INFO - 训练时间: 0.08 秒
2025-07-16 23:07:42 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 23:07:42 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:07:42 - model_training - INFO - 模型名称: XGBoost
2025-07-16 23:07:42 - model_training - INFO - 准确率: 0.7750
2025-07-16 23:07:42 - model_training - INFO - AUC: 0.8568
2025-07-16 23:07:42 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:42 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-16 23:07:42 - model_training - INFO - 
分类报告:
2025-07-16 23:07:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-16 23:07:42 - model_training - INFO - 训练时间: 0.04 秒
2025-07-16 23:07:42 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 23:07:42 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:07:44 - model_training - INFO - 模型名称: LightGBM
2025-07-16 23:07:44 - model_training - INFO - 准确率: 0.7500
2025-07-16 23:07:44 - model_training - INFO - AUC: 0.8568
2025-07-16 23:07:44 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:44 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-07-16 23:07:44 - model_training - INFO - 
分类报告:
2025-07-16 23:07:44 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-07-16 23:07:44 - model_training - INFO - 训练时间: 1.65 秒
2025-07-16 23:07:44 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-16 23:07:44 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-16 23:07:44 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:07:44 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:07:45 - model_training - INFO - 模型名称: CatBoost
2025-07-16 23:07:45 - model_training - INFO - 准确率: 0.8750
2025-07-16 23:07:45 - model_training - INFO - AUC: 0.9054
2025-07-16 23:07:45 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:45 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-07-16 23:07:45 - model_training - INFO - 
分类报告:
2025-07-16 23:07:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 23:07:45 - model_training - INFO - 训练时间: 1.15 秒
2025-07-16 23:07:45 - model_training - INFO - 模型 CatBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-07-16 23:07:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_feature_names.joblib
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:07:45 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 23:07:45 - model_training - INFO - 准确率: 0.8250
2025-07-16 23:07:45 - model_training - INFO - AUC: 0.9028
2025-07-16 23:07:45 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:45 - model_training - INFO - 
[[19  4]
 [ 3 14]]
2025-07-16 23:07:45 - model_training - INFO - 
分类报告:
2025-07-16 23:07:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.83      0.84        23
           1       0.78      0.82      0.80        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.83      0.82      0.83        40

2025-07-16 23:07:45 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:07:45 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 23:07:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:07:45 - model_training - INFO - 模型名称: SVM
2025-07-16 23:07:45 - model_training - INFO - 准确率: 0.8750
2025-07-16 23:07:45 - model_training - INFO - AUC: 0.9309
2025-07-16 23:07:45 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:45 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-07-16 23:07:45 - model_training - INFO - 
分类报告:
2025-07-16 23:07:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 23:07:45 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:07:45 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 23:07:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:07:45 - model_training - INFO - 模型名称: KNN
2025-07-16 23:07:45 - model_training - INFO - 准确率: 0.9000
2025-07-16 23:07:45 - model_training - INFO - AUC: 0.8913
2025-07-16 23:07:45 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:45 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-07-16 23:07:45 - model_training - INFO - 
分类报告:
2025-07-16 23:07:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-16 23:07:45 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:07:45 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-16 23:07:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:07:45 - model_training - INFO - 模型名称: Naive Bayes
2025-07-16 23:07:45 - model_training - INFO - 准确率: 0.8750
2025-07-16 23:07:45 - model_training - INFO - AUC: 0.9079
2025-07-16 23:07:45 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:45 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-16 23:07:45 - model_training - INFO - 
分类报告:
2025-07-16 23:07:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 23:07:45 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:07:45 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-07-16 23:07:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:07:46 - model_training - INFO - 模型名称: Neural Network
2025-07-16 23:07:46 - model_training - INFO - 准确率: 0.8500
2025-07-16 23:07:46 - model_training - INFO - AUC: 0.9156
2025-07-16 23:07:46 - model_training - INFO - 混淆矩阵:
2025-07-16 23:07:46 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-07-16 23:07:46 - model_training - INFO - 
分类报告:
2025-07-16 23:07:46 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-07-16 23:07:46 - model_training - INFO - 训练时间: 0.33 秒
2025-07-16 23:07:46 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-07-16 23:07:46 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:07:46 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 23:07:46 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.232
2025-07-16 23:07:46 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:07:46 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.402
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO - 开始计算 10 个模型间的量化多样性指标...
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.383 (多样性: 0.617)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.510 (多样性: 0.490)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.548
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.195 (多样性: 0.805)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.402 (多样性: 0.598)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.641
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.111 (多样性: 0.889)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.325
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.361 (多样性: 0.639)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.682
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.094 (多样性: 0.906)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.524 (多样性: 0.476)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.637
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: -0.143 (多样性: 0.857)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.402 (多样性: 0.598)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.662
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.094 (多样性: 0.906)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.524 (多样性: 0.476)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.637
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.250 (多样性: 0.750)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.556 (多样性: 0.444)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.576
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.094 (多样性: 0.906)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.510 (多样性: 0.490)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.640
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: -0.034 (多样性: 0.966)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.455 (多样性: 0.545)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.676
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.698 (多样性: 0.302)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.312
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.955 (多样性: 0.045)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.738 (多样性: 0.262)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.273
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.899 (多样性: 0.101)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.950 (多样性: 0.050)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.800 (多样性: 0.200)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.260
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.265
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.846 (多样性: 0.154)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.265
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.743 (多样性: 0.257)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.297
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.983 (多样性: 0.017)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.856 (多样性: 0.144)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.706 (多样性: 0.294)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.308
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.697 (多样性: 0.303)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.312
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.758 (多样性: 0.242)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.601 (多样性: 0.399)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.398
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.646 (多样性: 0.354)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.346
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.758 (多样性: 0.242)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.595 (多样性: 0.405)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.399
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.841 (多样性: 0.159)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.646 (多样性: 0.354)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.351
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.731 (多样性: 0.269)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.955 (多样性: 0.045)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.753 (多样性: 0.247)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.270
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.902 (多样性: 0.098)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.623 (多样性: 0.377)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.338
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.692 (多样性: 0.308)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.287
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.902 (多样性: 0.098)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.632 (多样性: 0.368)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.336
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.588 (多样性: 0.412)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.381
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.811 (多样性: 0.189)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.261
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.985 (多样性: 0.015)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.890 (多样性: 0.110)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.854 (多样性: 0.146)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.248
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.922 (多样性: 0.078)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.280
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.854 (多样性: 0.146)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.811 (多样性: 0.189)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.261
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.849 (多样性: 0.151)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.800 (多样性: 0.200)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.264
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.849 (多样性: 0.151)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.854 (多样性: 0.146)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.248
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.922 (多样性: 0.078)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.280
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.854 (多样性: 0.146)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.950 (多样性: 0.050)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.198
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.941 (多样性: 0.059)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.795 (多样性: 0.205)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.274
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     相关系数: 0.743 (多样性: 0.257)
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   平均多样性: 0.351
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.149
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   最小多样性: 0.198
2025-07-16 23:07:46 - quantified_diversity_evaluator - INFO -   最大多样性: 0.682
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:07:46 - enhanced_ensemble_selector - ERROR - 无法获取测试标签，回退到传统方法
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:09:17 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 23:09:17 - model_training - INFO - 准确率: 0.8250
2025-07-16 23:09:17 - model_training - INFO - AUC: 0.9028
2025-07-16 23:09:17 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:17 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-07-16 23:09:17 - model_training - INFO - 
分类报告:
2025-07-16 23:09:17 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-07-16 23:09:17 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:09:17 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 23:09:17 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:09:17 - model_training - INFO - 模型名称: Random Forest
2025-07-16 23:09:17 - model_training - INFO - 准确率: 0.8250
2025-07-16 23:09:17 - model_training - INFO - AUC: 0.9015
2025-07-16 23:09:17 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:17 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-16 23:09:17 - model_training - INFO - 
分类报告:
2025-07-16 23:09:17 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 23:09:17 - model_training - INFO - 训练时间: 0.08 秒
2025-07-16 23:09:17 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 23:09:17 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:09:17 - model_training - INFO - 模型名称: XGBoost
2025-07-16 23:09:17 - model_training - INFO - 准确率: 0.7750
2025-07-16 23:09:17 - model_training - INFO - AUC: 0.8568
2025-07-16 23:09:17 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:17 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-16 23:09:17 - model_training - INFO - 
分类报告:
2025-07-16 23:09:17 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-16 23:09:17 - model_training - INFO - 训练时间: 0.04 秒
2025-07-16 23:09:17 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 23:09:17 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:09:17 - model_training - INFO - 模型名称: LightGBM
2025-07-16 23:09:17 - model_training - INFO - 准确率: 0.7500
2025-07-16 23:09:17 - model_training - INFO - AUC: 0.8568
2025-07-16 23:09:17 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:17 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-07-16 23:09:17 - model_training - INFO - 
分类报告:
2025-07-16 23:09:17 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-07-16 23:09:17 - model_training - INFO - 训练时间: 0.22 秒
2025-07-16 23:09:17 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-16 23:09:17 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:09:18 - model_training - INFO - 模型名称: CatBoost
2025-07-16 23:09:18 - model_training - INFO - 准确率: 0.8750
2025-07-16 23:09:18 - model_training - INFO - AUC: 0.9054
2025-07-16 23:09:18 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:18 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-07-16 23:09:18 - model_training - INFO - 
分类报告:
2025-07-16 23:09:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 23:09:18 - model_training - INFO - 训练时间: 1.01 秒
2025-07-16 23:09:18 - model_training - INFO - 模型 CatBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-07-16 23:09:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_feature_names.joblib
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:09:18 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 23:09:18 - model_training - INFO - 准确率: 0.8250
2025-07-16 23:09:18 - model_training - INFO - AUC: 0.9028
2025-07-16 23:09:18 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:18 - model_training - INFO - 
[[19  4]
 [ 3 14]]
2025-07-16 23:09:18 - model_training - INFO - 
分类报告:
2025-07-16 23:09:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.83      0.84        23
           1       0.78      0.82      0.80        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.83      0.82      0.83        40

2025-07-16 23:09:18 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:09:18 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 23:09:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:09:18 - model_training - INFO - 模型名称: SVM
2025-07-16 23:09:18 - model_training - INFO - 准确率: 0.8750
2025-07-16 23:09:18 - model_training - INFO - AUC: 0.9309
2025-07-16 23:09:18 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:18 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-07-16 23:09:18 - model_training - INFO - 
分类报告:
2025-07-16 23:09:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 23:09:18 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:09:18 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 23:09:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:09:18 - model_training - INFO - 模型名称: KNN
2025-07-16 23:09:18 - model_training - INFO - 准确率: 0.9000
2025-07-16 23:09:18 - model_training - INFO - AUC: 0.8913
2025-07-16 23:09:18 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:18 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-07-16 23:09:18 - model_training - INFO - 
分类报告:
2025-07-16 23:09:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-16 23:09:18 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:09:18 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-16 23:09:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:09:18 - model_training - INFO - 模型名称: Naive Bayes
2025-07-16 23:09:18 - model_training - INFO - 准确率: 0.8750
2025-07-16 23:09:18 - model_training - INFO - AUC: 0.9079
2025-07-16 23:09:18 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:18 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-16 23:09:18 - model_training - INFO - 
分类报告:
2025-07-16 23:09:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 23:09:18 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 23:09:18 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-07-16 23:09:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:09:18 - model_training - INFO - 模型名称: Neural Network
2025-07-16 23:09:18 - model_training - INFO - 准确率: 0.8500
2025-07-16 23:09:18 - model_training - INFO - AUC: 0.9156
2025-07-16 23:09:18 - model_training - INFO - 混淆矩阵:
2025-07-16 23:09:18 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-07-16 23:09:18 - model_training - INFO - 
分类报告:
2025-07-16 23:09:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-07-16 23:09:18 - model_training - INFO - 训练时间: 0.32 秒
2025-07-16 23:09:18 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-07-16 23:09:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:09:18 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 23:09:18 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.232
2025-07-16 23:09:18 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:09:18 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.402
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO - 开始计算 10 个模型间的量化多样性指标...
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.383 (多样性: 0.617)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.510 (多样性: 0.490)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.548
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.195 (多样性: 0.805)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.402 (多样性: 0.598)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.641
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.111 (多样性: 0.889)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.325
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.361 (多样性: 0.639)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.682
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.094 (多样性: 0.906)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.524 (多样性: 0.476)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.637
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: -0.143 (多样性: 0.857)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.300
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.402 (多样性: 0.598)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.662
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.094 (多样性: 0.906)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.524 (多样性: 0.476)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.637
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.250 (多样性: 0.750)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.556 (多样性: 0.444)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.576
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.094 (多样性: 0.906)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.250
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.510 (多样性: 0.490)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.640
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: -0.034 (多样性: 0.966)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.455 (多样性: 0.545)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.676
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.698 (多样性: 0.302)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.312
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.955 (多样性: 0.045)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.738 (多样性: 0.262)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.273
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.899 (多样性: 0.101)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.950 (多样性: 0.050)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.800 (多样性: 0.200)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.260
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.265
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.846 (多样性: 0.154)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.265
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.743 (多样性: 0.257)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.297
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.983 (多样性: 0.017)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.200 (多样性: 0.800)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.856 (多样性: 0.144)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.216
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.706 (多样性: 0.294)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.308
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     相关系数: 0.697 (多样性: 0.303)
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -     综合多样性: 0.312
2025-07-16 23:09:18 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.758 (多样性: 0.242)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.601 (多样性: 0.399)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.398
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.646 (多样性: 0.354)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.346
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.758 (多样性: 0.242)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.595 (多样性: 0.405)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.399
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.841 (多样性: 0.159)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.646 (多样性: 0.354)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.351
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.731 (多样性: 0.269)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.266
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.955 (多样性: 0.045)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.753 (多样性: 0.247)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.270
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.902 (多样性: 0.098)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.623 (多样性: 0.377)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.338
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.692 (多样性: 0.308)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.287
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.902 (多样性: 0.098)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.632 (多样性: 0.368)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.336
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.588 (多样性: 0.412)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.381
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.811 (多样性: 0.189)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.261
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.985 (多样性: 0.015)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.890 (多样性: 0.110)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.221
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.854 (多样性: 0.146)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.248
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.922 (多样性: 0.078)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.280
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.854 (多样性: 0.146)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.811 (多样性: 0.189)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.261
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.849 (多样性: 0.151)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.954 (多样性: 0.046)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.800 (多样性: 0.200)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.264
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.849 (多样性: 0.151)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.854 (多样性: 0.146)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.248
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.922 (多样性: 0.078)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.280
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.854 (多样性: 0.146)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.950 (多样性: 0.050)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.198
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.941 (多样性: 0.059)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.795 (多样性: 0.205)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.274
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     相关系数: 0.743 (多样性: 0.257)
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   平均多样性: 0.351
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.149
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   最小多样性: 0.198
2025-07-16 23:09:19 - quantified_diversity_evaluator - INFO -   最大多样性: 0.682
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:09:19 - enhanced_ensemble_selector - ERROR - 无法获取测试标签，回退到传统方法
