#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化多样性评估器
基于文档中的多样性度量方法实现科学的量化评估
"""

import numpy as np
import pandas as pd
from sklearn.metrics import accuracy_score
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

from logger import get_logger

logger = get_logger(__name__)

class QuantifiedDiversityEvaluator:
    """
    量化多样性评估器
    实现Q统计量、不一致性度量、双错度量等科学的多样性量化方法
    """
    
    def __init__(self):
        """初始化评估器"""
        self.diversity_metrics = {}
        self.pairwise_results = {}
    
    def calculate_q_statistic(self, y_true, pred1, pred2):
        """
        计算Q统计量（Q-statistic）
        用于衡量两个分类器预测结果的一致性
        
        Args:
            y_true: 真实标签
            pred1: 模型1的预测结果
            pred2: 模型2的预测结果
            
        Returns:
            float: Q统计量值，越小表示多样性越大
        """
        # 计算混淆矩阵的四个元素
        N_11 = np.sum((pred1 == y_true) & (pred2 == y_true))  # 都预测正确
        N_00 = np.sum((pred1 != y_true) & (pred2 != y_true))  # 都预测错误
        N_01 = np.sum((pred1 == y_true) & (pred2 != y_true))  # 模型1对，模型2错
        N_10 = np.sum((pred1 != y_true) & (pred2 == y_true))  # 模型1错，模型2对
        
        # 计算Q统计量
        numerator = N_11 * N_00 - N_01 * N_10
        denominator = N_11 * N_00 + N_01 * N_10
        
        if denominator == 0:
            return 0.0  # 避免除零错误
        
        q_statistic = numerator / denominator
        
        return q_statistic
    
    def calculate_disagreement_measure(self, pred1, pred2):
        """
        计算不一致性度量（Disagreement Measure）
        统计两个模型预测不一致的样本比例
        
        Args:
            pred1: 模型1的预测结果
            pred2: 模型2的预测结果
            
        Returns:
            float: 不一致性比例，越高表示多样性越大
        """
        disagreement = np.sum(pred1 != pred2) / len(pred1)
        return disagreement
    
    def calculate_double_fault_measure(self, y_true, pred1, pred2):
        """
        计算双错度量（Double-fault Measure）
        统计两个模型都预测错误的样本比例
        
        Args:
            y_true: 真实标签
            pred1: 模型1的预测结果
            pred2: 模型2的预测结果
            
        Returns:
            float: 双错比例，越低表示多样性越大
        """
        double_fault = np.sum((pred1 != y_true) & (pred2 != y_true)) / len(y_true)
        return double_fault
    
    def calculate_correlation_coefficient(self, pred1, pred2):
        """
        计算相关系数（Correlation Coefficient）
        
        Args:
            pred1: 模型1的预测结果
            pred2: 模型2的预测结果
            
        Returns:
            float: 相关系数，越低表示多样性越大
        """
        try:
            correlation, _ = pearsonr(pred1.astype(float), pred2.astype(float))
            return correlation if not np.isnan(correlation) else 0.0
        except:
            return 0.0
    
    def calculate_pairwise_diversity(self, y_true, predictions_dict):
        """
        计算所有模型对之间的多样性指标
        
        Args:
            y_true: 真实标签
            predictions_dict: 模型预测结果字典 {model_name: predictions}
            
        Returns:
            dict: 包含所有多样性指标的详细结果
        """
        model_names = list(predictions_dict.keys())
        n_models = len(model_names)
        
        if n_models < 2:
            logger.warning("至少需要2个模型才能计算多样性")
            return {}
        
        # 初始化结果存储
        results = {
            'q_statistics': {},
            'disagreement_measures': {},
            'double_fault_measures': {},
            'correlation_coefficients': {},
            'diversity_summary': {}
        }
        
        logger.info(f"开始计算 {n_models} 个模型间的量化多样性指标...")
        
        # 计算所有模型对的多样性指标
        for i, model1 in enumerate(model_names):
            for j, model2 in enumerate(model_names):
                if i < j:  # 避免重复计算
                    pair_name = f"{model1}_vs_{model2}"
                    
                    pred1 = predictions_dict[model1]
                    pred2 = predictions_dict[model2]
                    
                    # 计算各种多样性指标
                    q_stat = self.calculate_q_statistic(y_true, pred1, pred2)
                    disagreement = self.calculate_disagreement_measure(pred1, pred2)
                    double_fault = self.calculate_double_fault_measure(y_true, pred1, pred2)
                    correlation = self.calculate_correlation_coefficient(pred1, pred2)
                    
                    # 存储结果
                    results['q_statistics'][pair_name] = q_stat
                    results['disagreement_measures'][pair_name] = disagreement
                    results['double_fault_measures'][pair_name] = double_fault
                    results['correlation_coefficients'][pair_name] = correlation
                    
                    # 计算综合多样性得分
                    # Q统计量：越小越好（多样性大），转换为正向指标
                    q_diversity = 1 - abs(q_stat)
                    # 不一致性：越大越好（多样性大）
                    disagreement_diversity = disagreement
                    # 双错度量：越小越好（多样性大），转换为正向指标
                    double_fault_diversity = 1 - double_fault
                    # 相关系数：越小越好（多样性大），转换为正向指标
                    correlation_diversity = 1 - abs(correlation)
                    
                    # 综合多样性得分（加权平均）
                    comprehensive_diversity = (
                        0.3 * q_diversity +
                        0.3 * disagreement_diversity +
                        0.2 * double_fault_diversity +
                        0.2 * correlation_diversity
                    )
                    
                    results['diversity_summary'][pair_name] = {
                        'q_statistic': q_stat,
                        'disagreement': disagreement,
                        'double_fault': double_fault,
                        'correlation': correlation,
                        'q_diversity': q_diversity,
                        'disagreement_diversity': disagreement_diversity,
                        'double_fault_diversity': double_fault_diversity,
                        'correlation_diversity': correlation_diversity,
                        'comprehensive_diversity': comprehensive_diversity
                    }
                    
                    logger.info(f"  {pair_name}:")
                    logger.info(f"    Q统计量: {q_stat:.3f} (多样性: {q_diversity:.3f})")
                    logger.info(f"    不一致性: {disagreement:.3f}")
                    logger.info(f"    双错度量: {double_fault:.3f} (多样性: {double_fault_diversity:.3f})")
                    logger.info(f"    相关系数: {correlation:.3f} (多样性: {correlation_diversity:.3f})")
                    logger.info(f"    综合多样性: {comprehensive_diversity:.3f}")
        
        # 计算整体多样性统计
        all_diversities = [summary['comprehensive_diversity'] 
                          for summary in results['diversity_summary'].values()]
        
        results['overall_statistics'] = {
            'mean_diversity': np.mean(all_diversities),
            'std_diversity': np.std(all_diversities),
            'min_diversity': np.min(all_diversities),
            'max_diversity': np.max(all_diversities),
            'n_pairs': len(all_diversities)
        }
        
        logger.info(f"整体多样性统计:")
        logger.info(f"  平均多样性: {results['overall_statistics']['mean_diversity']:.3f}")
        logger.info(f"  多样性标准差: {results['overall_statistics']['std_diversity']:.3f}")
        logger.info(f"  最小多样性: {results['overall_statistics']['min_diversity']:.3f}")
        logger.info(f"  最大多样性: {results['overall_statistics']['max_diversity']:.3f}")
        
        self.pairwise_results = results
        return results
    
    def evaluate_model_combination_diversity(self, y_true, predictions_dict, combination):
        """
        评估特定模型组合的多样性
        
        Args:
            y_true: 真实标签
            predictions_dict: 所有模型预测结果字典
            combination: 要评估的模型组合列表
            
        Returns:
            dict: 组合多样性评估结果
        """
        if len(combination) < 2:
            return {'error': '至少需要2个模型'}
        
        # 提取组合中模型的预测结果
        combo_predictions = {name: predictions_dict[name] for name in combination}
        
        # 计算组合内的多样性
        combo_results = self.calculate_pairwise_diversity(y_true, combo_predictions)
        
        if not combo_results:
            return {'error': '多样性计算失败'}
        
        # 计算组合的综合评估
        diversities = [summary['comprehensive_diversity'] 
                      for summary in combo_results['diversity_summary'].values()]
        
        evaluation = {
            'combination': combination,
            'n_models': len(combination),
            'n_pairs': len(diversities),
            'mean_diversity': np.mean(diversities),
            'min_diversity': np.min(diversities),
            'max_diversity': np.max(diversities),
            'std_diversity': np.std(diversities),
            'detailed_results': combo_results,
            'diversity_score': np.mean(diversities)  # 用于排序比较
        }
        
        # 多样性质量评估
        if evaluation['mean_diversity'] >= 0.7:
            evaluation['diversity_level'] = 'excellent'
            evaluation['diversity_description'] = '优秀的多样性'
        elif evaluation['mean_diversity'] >= 0.5:
            evaluation['diversity_level'] = 'good'
            evaluation['diversity_description'] = '良好的多样性'
        elif evaluation['mean_diversity'] >= 0.3:
            evaluation['diversity_level'] = 'moderate'
            evaluation['diversity_description'] = '中等的多样性'
        else:
            evaluation['diversity_level'] = 'poor'
            evaluation['diversity_description'] = '较差的多样性'
        
        return evaluation
    
    def rank_model_combinations(self, y_true, predictions_dict, performance_scores, 
                               combinations, diversity_weight=0.5):
        """
        基于性能和多样性对模型组合进行排序
        
        Args:
            y_true: 真实标签
            predictions_dict: 模型预测结果字典
            performance_scores: 模型性能得分字典
            combinations: 要评估的模型组合列表
            diversity_weight: 多样性权重（0-1）
            
        Returns:
            list: 排序后的组合评估结果
        """
        logger.info(f"开始评估 {len(combinations)} 个模型组合...")
        
        combination_evaluations = []
        
        for combination in combinations:
            # 计算组合的多样性
            diversity_eval = self.evaluate_model_combination_diversity(
                y_true, predictions_dict, combination
            )
            
            if 'error' in diversity_eval:
                continue
            
            # 计算组合的平均性能和标准差
            combo_performances = [performance_scores[model] for model in combination]
            combo_performance = np.mean(combo_performances)
            combo_performance_std = np.std(combo_performances) if len(combo_performances) > 1 else 0.0

            # 计算综合得分
            performance_weight = 1 - diversity_weight
            comprehensive_score = (
                performance_weight * combo_performance +
                diversity_weight * diversity_eval['diversity_score']
            )

            evaluation = {
                'combination': combination,
                'performance_score': combo_performance,
                'performance_std': combo_performance_std,
                'diversity_score': diversity_eval['diversity_score'],
                'comprehensive_score': comprehensive_score,
                'diversity_level': diversity_eval['diversity_level'],
                'diversity_description': diversity_eval['diversity_description'],
                'detailed_diversity': diversity_eval
            }
            
            combination_evaluations.append(evaluation)
            
            logger.info(f"  组合 {combination}:")
            logger.info(f"    性能得分: {combo_performance:.3f}")
            logger.info(f"    多样性得分: {diversity_eval['diversity_score']:.3f}")
            logger.info(f"    综合得分: {comprehensive_score:.3f}")
            logger.info(f"    多样性等级: {diversity_eval['diversity_description']}")
        
        # 按综合得分排序
        combination_evaluations.sort(key=lambda x: x['comprehensive_score'], reverse=True)
        
        logger.info(f"最优组合排序完成，共评估 {len(combination_evaluations)} 个组合")

        return combination_evaluations

    def calculate_kappa_statistic(self, y_true, pred1, pred2):
        """
        计算Kappa统计量
        衡量两个分类器一致性，考虑偶然一致的情况

        Args:
            y_true: 真实标签
            pred1: 模型1的预测结果
            pred2: 模型2的预测结果

        Returns:
            float: Kappa值，越小表示多样性越大
        """
        # 计算观察到的一致性
        observed_agreement = np.sum(pred1 == pred2) / len(pred1)

        # 计算期望的一致性（偶然一致）
        acc1 = accuracy_score(y_true, pred1)
        acc2 = accuracy_score(y_true, pred2)

        # 假设二分类，计算各类别的期望一致性
        p_pos_1 = np.sum(pred1 == 1) / len(pred1)
        p_neg_1 = 1 - p_pos_1
        p_pos_2 = np.sum(pred2 == 1) / len(pred2)
        p_neg_2 = 1 - p_pos_2

        expected_agreement = p_pos_1 * p_pos_2 + p_neg_1 * p_neg_2

        # 计算Kappa
        if expected_agreement == 1:
            return 0.0

        kappa = (observed_agreement - expected_agreement) / (1 - expected_agreement)
        return kappa

    def calculate_entropy_measure(self, predictions_dict):
        """
        计算预测结果的熵多样性
        基于信息论的多样性度量

        Args:
            predictions_dict: 模型预测结果字典

        Returns:
            float: 熵多样性得分，越高表示多样性越大
        """
        model_names = list(predictions_dict.keys())
        n_models = len(model_names)
        n_samples = len(predictions_dict[model_names[0]])

        if n_models < 2:
            return 0.0

        # 计算每个样本的预测分布熵
        sample_entropies = []

        for i in range(n_samples):
            # 获取所有模型对样本i的预测
            sample_predictions = [predictions_dict[model][i] for model in model_names]

            # 计算预测分布
            unique_preds, counts = np.unique(sample_predictions, return_counts=True)
            probabilities = counts / n_models

            # 计算熵
            entropy = -np.sum(probabilities * np.log2(probabilities + 1e-8))
            sample_entropies.append(entropy)

        # 返回平均熵
        return np.mean(sample_entropies)

    def calculate_yule_q_statistic(self, y_true, pred1, pred2):
        """
        计算Yule's Q统计量（另一种Q统计量的变体）

        Args:
            y_true: 真实标签
            pred1: 模型1的预测结果
            pred2: 模型2的预测结果

        Returns:
            float: Yule's Q值
        """
        # 构建2x2列联表
        correct1 = (pred1 == y_true)
        correct2 = (pred2 == y_true)

        a = np.sum(correct1 & correct2)    # 都正确
        b = np.sum(correct1 & ~correct2)   # 1正确，2错误
        c = np.sum(~correct1 & correct2)   # 1错误，2正确
        d = np.sum(~correct1 & ~correct2)  # 都错误

        # 计算Yule's Q
        if (a * d + b * c) == 0:
            return 0.0

        yule_q = (a * d - b * c) / (a * d + b * c)
        return yule_q

    def generate_comprehensive_diversity_report(self, y_true, predictions_dict,
                                              performance_scores=None):
        """
        生成综合的多样性分析报告

        Args:
            y_true: 真实标签
            predictions_dict: 模型预测结果字典
            performance_scores: 模型性能得分字典（可选）

        Returns:
            str: 格式化的多样性分析报告
        """
        # 计算所有多样性指标
        pairwise_results = self.calculate_pairwise_diversity(y_true, predictions_dict)

        if not pairwise_results:
            return "无法生成多样性报告：数据不足"

        # 计算额外的多样性指标
        entropy_diversity = self.calculate_entropy_measure(predictions_dict)

        model_names = list(predictions_dict.keys())

        # 生成报告
        report = []
        report.append("=" * 80)
        report.append("量化多样性分析报告")
        report.append("=" * 80)

        # 基本信息
        report.append(f"\n模型数量: {len(model_names)}")
        report.append(f"模型列表: {', '.join(model_names)}")
        report.append(f"样本数量: {len(y_true)}")

        # 整体多样性统计
        overall_stats = pairwise_results['overall_statistics']
        report.append(f"\n整体多样性统计:")
        report.append(f"  平均多样性得分: {overall_stats['mean_diversity']:.4f}")
        report.append(f"  多样性标准差: {overall_stats['std_diversity']:.4f}")
        report.append(f"  最小多样性: {overall_stats['min_diversity']:.4f}")
        report.append(f"  最大多样性: {overall_stats['max_diversity']:.4f}")
        report.append(f"  熵多样性: {entropy_diversity:.4f}")

        # 详细的成对分析
        report.append(f"\n详细的成对多样性分析:")
        report.append("-" * 60)

        for pair_name, summary in pairwise_results['diversity_summary'].items():
            report.append(f"\n{pair_name}:")
            report.append(f"  Q统计量: {summary['q_statistic']:.4f}")
            report.append(f"  不一致性度量: {summary['disagreement']:.4f}")
            report.append(f"  双错度量: {summary['double_fault']:.4f}")
            report.append(f"  相关系数: {summary['correlation']:.4f}")
            report.append(f"  综合多样性得分: {summary['comprehensive_diversity']:.4f}")

            # 多样性等级评估
            diversity_score = summary['comprehensive_diversity']
            if diversity_score >= 0.7:
                level = "优秀"
            elif diversity_score >= 0.5:
                level = "良好"
            elif diversity_score >= 0.3:
                level = "中等"
            else:
                level = "较差"
            report.append(f"  多样性等级: {level}")

        # 性能与多样性对比（如果提供了性能得分）
        if performance_scores:
            report.append(f"\n性能与多样性对比:")
            report.append("-" * 60)

            for model in model_names:
                perf = performance_scores.get(model, 0)
                report.append(f"  {model}: 性能={perf:.4f}")

            # 计算性能-多样性权衡
            avg_performance = np.mean([performance_scores.get(model, 0) for model in model_names])
            avg_diversity = overall_stats['mean_diversity']

            report.append(f"\n权衡分析:")
            report.append(f"  平均性能: {avg_performance:.4f}")
            report.append(f"  平均多样性: {avg_diversity:.4f}")
            report.append(f"  性能-多样性比: {avg_performance/avg_diversity:.4f}")

        # 建议
        report.append(f"\n优化建议:")
        report.append("-" * 60)

        if overall_stats['mean_diversity'] < 0.3:
            report.append("  • 整体多样性较低，建议:")
            report.append("    - 选择不同类型的算法（线性、树模型、神经网络等）")
            report.append("    - 使用不同的特征子集训练模型")
            report.append("    - 调整模型参数增加差异性")
        elif overall_stats['mean_diversity'] > 0.7:
            report.append("  • 多样性很好，建议:")
            report.append("    - 使用加权融合策略")
            report.append("    - 重点关注性能优化")
        else:
            report.append("  • 多样性适中，建议:")
            report.append("    - 平衡性能和多样性")
            report.append("    - 考虑堆叠法融合")

        # 低多样性模型对警告
        low_diversity_pairs = []
        for pair_name, summary in pairwise_results['diversity_summary'].items():
            if summary['comprehensive_diversity'] < 0.3:
                low_diversity_pairs.append(pair_name)

        if low_diversity_pairs:
            report.append(f"\n⚠️  低多样性模型对:")
            for pair in low_diversity_pairs:
                report.append(f"    - {pair}")
            report.append("  建议考虑替换其中一个模型")

        return "\n".join(report)
