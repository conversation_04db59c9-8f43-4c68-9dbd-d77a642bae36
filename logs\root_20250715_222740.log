2025-07-15 22:27:42 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:27:42 - GUI - INFO - GUI界面初始化完成
2025-07-15 22:28:20 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-07-15 22:28:20 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 22:28:20 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-15 22:28:30 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 80, 'max_depth': 29, 'min_samples_split': 9, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-07-15 22:28:30 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9835
2025-07-15 22:28:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:31 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250715_222831.html
2025-07-15 22:28:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:31 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250715_222831.html
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 10.95 秒
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 20
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 212, 'max_depth': 7, 'learning_rate': 0.10312727703132145, 'subsample': 0.6779021185855993, 'colsample_bytree': 0.525587286744166}
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9869
2025-07-15 22:28:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250715_222835.html
2025-07-15 22:28:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250715_222835.html
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.51 秒
2025-07-15 22:28:35 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 20
2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 56, 'max_depth': 10, 'learning_rate': 0.2070769447095247, 'feature_fraction': 0.5184131039958577, 'bagging_fraction': 0.8988346967121249}
2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9854
2025-07-15 22:28:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250715_222838.html
2025-07-15 22:28:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250715_222838.html
2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.28 秒
2025-07-15 22:28:38 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 20
2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 4.922693618620555, 'solver': 'lbfgs'}
2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-07-15 22:28:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250715_222839.html
2025-07-15 22:28:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250715_222839.html
2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.66 秒
2025-07-15 22:28:39 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 20
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 8.842081287127442, 'kernel': 'rbf'}
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9919
2025-07-15 22:28:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250715_222840.html
2025-07-15 22:28:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250715_222840.html
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.73 秒
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 20
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 6}
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9753
2025-07-15 22:28:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250715_222840.html
2025-07-15 22:28:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250715_222840.html
2025-07-15 22:28:40 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.56 秒
2025-07-15 22:30:53 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-07-15 22:30:53 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 22:30:53 - model_training - INFO - 模型名称: Random Forest
2025-07-15 22:30:53 - model_training - INFO - 准确率: 0.9000
2025-07-15 22:30:53 - model_training - INFO - AUC: 0.9386
2025-07-15 22:30:53 - model_training - INFO - 混淆矩阵:
2025-07-15 22:30:53 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-15 22:30:53 - model_training - INFO - 
分类报告:
2025-07-15 22:30:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-15 22:30:53 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 22:30:53 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 22:30:53 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 22:30:53 - model_training - INFO - 模型名称: XGBoost
2025-07-15 22:30:53 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:30:53 - model_training - INFO - AUC: 0.9668
2025-07-15 22:30:53 - model_training - INFO - 混淆矩阵:
2025-07-15 22:30:53 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:30:53 - model_training - INFO - 
分类报告:
2025-07-15 22:30:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:30:53 - model_training - INFO - 训练时间: 0.05 秒
2025-07-15 22:30:53 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 22:30:53 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 22:30:54 - model_training - INFO - 模型名称: LightGBM
2025-07-15 22:30:54 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:30:54 - model_training - INFO - AUC: 0.9463
2025-07-15 22:30:54 - model_training - INFO - 混淆矩阵:
2025-07-15 22:30:54 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:30:54 - model_training - INFO - 
分类报告:
2025-07-15 22:30:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:30:54 - model_training - INFO - 训练时间: 0.22 秒
2025-07-15 22:30:54 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 22:30:54 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 22:30:54 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 22:30:54 - model_training - INFO - 准确率: 0.8500
2025-07-15 22:30:54 - model_training - INFO - AUC: 0.9284
2025-07-15 22:30:54 - model_training - INFO - 混淆矩阵:
2025-07-15 22:30:54 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-07-15 22:30:54 - model_training - INFO - 
分类报告:
2025-07-15 22:30:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-07-15 22:30:54 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:30:54 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 22:30:54 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 22:30:54 - model_training - INFO - 模型名称: SVM
2025-07-15 22:30:54 - model_training - INFO - 准确率: 0.8000
2025-07-15 22:30:54 - model_training - INFO - AUC: 0.9207
2025-07-15 22:30:54 - model_training - INFO - 混淆矩阵:
2025-07-15 22:30:54 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-07-15 22:30:54 - model_training - INFO - 
分类报告:
2025-07-15 22:30:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-07-15 22:30:54 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:30:54 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-15 22:30:54 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-15 22:30:54 - model_training - INFO - 模型名称: KNN
2025-07-15 22:30:54 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:30:54 - model_training - INFO - AUC: 0.9322
2025-07-15 22:30:54 - model_training - INFO - 混淆矩阵:
2025-07-15 22:30:54 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:30:54 - model_training - INFO - 
分类报告:
2025-07-15 22:30:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:30:54 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:30:54 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-15 22:30:54 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-15 22:31:33 - model_ensemble - INFO - ============================================================
2025-07-15 22:31:33 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:31:33 - model_ensemble - INFO - ============================================================
2025-07-15 22:31:33 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic', 'SVM', 'KNN']
2025-07-15 22:31:33 - model_ensemble - INFO - 集成方法: ['voting', 'bagging', 'boosting', 'stacking']
2025-07-15 22:31:33 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:31:33 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:31:34 - model_training - INFO - 模型名称: Random Forest
2025-07-15 22:31:34 - model_training - INFO - 准确率: 0.9000
2025-07-15 22:31:34 - model_training - INFO - AUC: 0.9386
2025-07-15 22:31:34 - model_training - INFO - 混淆矩阵:
2025-07-15 22:31:34 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-15 22:31:34 - model_training - INFO - 
分类报告:
2025-07-15 22:31:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-15 22:31:34 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 22:31:34 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 22:31:34 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 22:31:34 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:31:34 - model_training - INFO - 模型名称: XGBoost
2025-07-15 22:31:34 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:31:34 - model_training - INFO - AUC: 0.9668
2025-07-15 22:31:34 - model_training - INFO - 混淆矩阵:
2025-07-15 22:31:34 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:31:34 - model_training - INFO - 
分类报告:
2025-07-15 22:31:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:31:34 - model_training - INFO - 训练时间: 0.04 秒
2025-07-15 22:31:34 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 22:31:34 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 22:31:34 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 22:31:34 - model_training - INFO - 模型名称: LightGBM
2025-07-15 22:31:34 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:31:34 - model_training - INFO - AUC: 0.9463
2025-07-15 22:31:34 - model_training - INFO - 混淆矩阵:
2025-07-15 22:31:34 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:31:34 - model_training - INFO - 
分类报告:
2025-07-15 22:31:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:31:34 - model_training - INFO - 训练时间: 0.22 秒
2025-07-15 22:31:34 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 22:31:34 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 22:31:34 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 22:31:34 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 22:31:34 - model_training - INFO - 准确率: 0.8500
2025-07-15 22:31:34 - model_training - INFO - AUC: 0.9284
2025-07-15 22:31:34 - model_training - INFO - 混淆矩阵:
2025-07-15 22:31:34 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-07-15 22:31:34 - model_training - INFO - 
分类报告:
2025-07-15 22:31:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-07-15 22:31:34 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:31:34 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 22:31:34 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 22:31:34 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:31:34 - model_training - INFO - 模型名称: SVM
2025-07-15 22:31:34 - model_training - INFO - 准确率: 0.8000
2025-07-15 22:31:34 - model_training - INFO - AUC: 0.9207
2025-07-15 22:31:34 - model_training - INFO - 混淆矩阵:
2025-07-15 22:31:34 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-07-15 22:31:34 - model_training - INFO - 
分类报告:
2025-07-15 22:31:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-07-15 22:31:34 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:31:34 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-15 22:31:34 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-15 22:31:34 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-15 22:31:34 - model_training - INFO - 模型名称: KNN
2025-07-15 22:31:34 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:31:34 - model_training - INFO - AUC: 0.9322
2025-07-15 22:31:34 - model_training - INFO - 混淆矩阵:
2025-07-15 22:31:34 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:31:34 - model_training - INFO - 
分类报告:
2025-07-15 22:31:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:31:34 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:31:34 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-15 22:31:34 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-15 22:31:34 - model_ensemble - INFO -   KNN 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 成功训练了 6 个基础模型
2025-07-15 22:31:34 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:31:34 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:31:34 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:31:34 - model_ensemble - INFO -     voting_soft - 准确率: 0.8500, F1: 0.8500
2025-07-15 22:31:34 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:31:34 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:31:35 - model_ensemble - INFO -     voting_hard - 准确率: 0.8500, F1: 0.8484
2025-07-15 22:31:35 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-07-15 22:31:35 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-07-15 22:31:35 - model_ensemble - ERROR - 集成方法 bagging 失败: __init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 22:31:35 - model_ensemble - INFO - 步骤2: 运行集成方法 - boosting
2025-07-15 22:31:35 - model_ensemble - INFO - 开始训练集成模型，方法: boosting
2025-07-15 22:31:35 - model_ensemble - ERROR - 集成方法 boosting 失败: __init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 22:31:35 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:31:35 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:31:36 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:31:36 - model_ensemble - INFO - ============================================================
2025-07-15 22:31:36 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:31:36 - model_ensemble - INFO - ============================================================
2025-07-15 22:31:36 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 22:31:36 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 22:31:36 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 22:31:36 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:31:36 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8500, 精确率: 0.8500, 召回率: 0.8500, F1: 0.8500, AUC: 0.9591
2025-07-15 22:31:36 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8500, 精确率: 0.8513, 召回率: 0.8500, F1: 0.8484, AUC: 0.0000
2025-07-15 22:31:36 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9540
2025-07-15 22:31:36 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:31:37 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_223136.joblib
2025-07-15 22:31:37 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:31:37 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-15 22:31:37 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:31:37 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:31:37 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:31:37 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:31:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([ 0.1077149 ,  0.12592475, -0.01294599, -0.02135643,  0.00097586,
        0.11331763,  0.02101725,  0.00704157])
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([-0.1077149 , -0.12592475,  0.01294599,  0.02135643, -0.00097586,
       -0.11331763, -0.02101725, -0.00704157])
2025-07-15 22:31:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([ 0.11279681, -0.08884771, -0.031101  , -0.07805242,  0.02008731,
        0.01524405, -0.0314305 , -0.0231513 ])
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([-0.11279681,  0.08884771,  0.031101  ,  0.07805242, -0.02008731,
       -0.01524405,  0.0314305 ,  0.0231513 ])
2025-07-15 22:31:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([-0.0954303 , -0.09702581, -0.01682425, -0.02761012, -0.0439616 ,
       -0.30255584,  0.00090825, -0.01583146])
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([ 0.0954303 ,  0.09702581,  0.01682425,  0.02761012,  0.0439616 ,
        0.30255584, -0.00090825,  0.01583146])
2025-07-15 22:31:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([-0.16673367, -0.02258822,  0.01348127, -0.04463017, -0.04911601,
       -0.29385423,  0.00561559, -0.01918483])
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([ 0.16673367,  0.02258822, -0.01348127,  0.04463017,  0.04911601,
        0.29385423, -0.00561559,  0.01918483])
2025-07-15 22:31:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([ 0.11316882,  0.13457292, -0.0097554 , -0.02384711, -0.05936559,
       -0.03323029,  0.08932662,  0.03084891])
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([-0.11316882, -0.13457293,  0.00975541,  0.02384711,  0.05936559,
        0.03323029, -0.08932662, -0.03084891])
2025-07-15 22:31:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([ 0.07871614,  0.13362809,  0.00463032,  0.08822635,  0.06864803,
       -0.00626652, -0.01112925,  0.02730317])
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([-0.07871614, -0.13362809, -0.00463032, -0.08822635, -0.06864803,
        0.00626652,  0.01112925, -0.02730317])
2025-07-15 22:31:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([ 0.1544761 , -0.05736398, -0.00092502, -0.03492738,  0.00383034,
        0.24596461, -0.01241657, -0.01060954])
2025-07-15 22:31:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:37 - shap - INFO - phi = array([-0.1544761 ,  0.05736398,  0.00092502,  0.03492738, -0.00383034,
       -0.24596461,  0.01241657,  0.01060954])
2025-07-15 22:31:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.14193988, -0.07832072, -0.01017671, -0.04569841, -0.05004464,
        0.07946547,  0.04320784,  0.00220154])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.14193988,  0.07832072,  0.01017671,  0.04569841,  0.05004464,
       -0.07946547, -0.04320784, -0.00220154])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.18839226, -0.22952706,  0.01621134, -0.05247148, -0.04607668,
       -0.00870341,  0.0047954 , -0.02051125])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.18839226,  0.22952706, -0.01621134,  0.05247148,  0.04607668,
        0.00870341, -0.0047954 ,  0.02051125])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.06408485,  0.19547134, -0.00329256, -0.02849226,  0.03134693,
       -0.37420462, -0.02959984, -0.01939791])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.06408485, -0.19547134,  0.00329256,  0.02849226, -0.03134693,
        0.37420462,  0.02959984,  0.01939791])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.06075206,  0.19175997,  0.00757974, -0.03677675, -0.00435031,
        0.17169242,  0.00454898, -0.01271317])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.06075206, -0.19175997, -0.00757974,  0.03677675,  0.00435031,
       -0.17169242, -0.00454898,  0.01271317])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.09339006,  0.14833589,  0.00094568, -0.0356317 , -0.00371225,
        0.20379567, -0.00673777, -0.01027582])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.09339006, -0.1483359 , -0.00094568,  0.0356317 ,  0.00371225,
       -0.20379567,  0.00673777,  0.01027583])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.12593768, -0.27856917, -0.03291918, -0.04522582, -0.06104538,
        0.00211091,  0.03525434, -0.03330172])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.12593768,  0.27856917,  0.03291918,  0.04522582,  0.06104538,
       -0.00211091, -0.03525434,  0.03330171])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.08880232, -0.15859857, -0.01807558, -0.03265092, -0.05186503,
       -0.24858371,  0.01542665, -0.01595142])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.08880232,  0.15859857,  0.01807558,  0.03265092,  0.05186503,
        0.24858371, -0.01542665,  0.01595142])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.19005673, -0.19860009,  0.00106322, -0.04440146, -0.02496481,
        0.06915909,  0.01139211, -0.01829617])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.19005673,  0.19860009, -0.00106322,  0.04440146,  0.02496481,
       -0.06915909, -0.01139211,  0.01829617])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.13309954, -0.04895196,  0.01239153,  0.09515142,  0.0851543 ,
        0.01425941, -0.01088336,  0.02180491])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.13309954,  0.04895196, -0.01239153, -0.09515142, -0.0851543 ,
       -0.01425941,  0.01088336, -0.02180491])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([ 0.10633389,  0.16246993, -0.0080498 , -0.02534691, -0.01986729,
        0.14597602,  0.01532443, -0.00055412])
2025-07-15 22:31:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:38 - shap - INFO - phi = array([-0.10633389, -0.16246993,  0.0080498 ,  0.02534691,  0.01986729,
       -0.14597602, -0.01532443,  0.00055412])
2025-07-15 22:31:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([ 0.10523683,  0.20984249, -0.01445548, -0.00590727,  0.02114743,
        0.07928079,  0.00501471, -0.01144772])
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([-0.10523683, -0.20984249,  0.01445548,  0.00590727, -0.02114743,
       -0.07928079, -0.00501471,  0.01144772])
2025-07-15 22:31:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([ 0.1463103 , -0.10257378,  0.00686362,  0.04104952,  0.07291263,
        0.02229393, -0.02596869, -0.02824841])
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([-0.1463103 ,  0.10257378, -0.00686362, -0.04104952, -0.07291262,
       -0.02229393,  0.02596869,  0.02824841])
2025-07-15 22:31:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([-0.07338929, -0.17754989,  0.02317445,  0.06841507, -0.02576587,
       -0.00156487, -0.02161744,  0.05289586])
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([ 0.07338929,  0.17754989, -0.02317445, -0.06841507,  0.02576587,
        0.00156487,  0.02161744, -0.05289586])
2025-07-15 22:31:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([-0.09301574, -0.10970144,  0.01764004,  0.13880316,  0.04041763,
       -0.15220307, -0.02227602,  0.01443611])
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([ 0.09301574,  0.10970144, -0.01764004, -0.13880316, -0.04041763,
        0.15220307,  0.02227602, -0.01443611])
2025-07-15 22:31:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([-0.02964984,  0.10378637,  0.02015593,  0.13955562,  0.08056038,
       -0.03840405, -0.0239042 ,  0.01710177])
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([ 0.02964984, -0.10378636, -0.02015593, -0.13955562, -0.08056038,
        0.03840405,  0.0239042 , -0.01710177])
2025-07-15 22:31:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([ 0.08850585,  0.16327464,  0.00372961,  0.08276416,  0.06725401,
        0.0028971 , -0.01381277, -0.00903578])
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([-0.08850585, -0.16327464, -0.00372961, -0.08276416, -0.06725401,
       -0.0028971 ,  0.01381277,  0.00903578])
2025-07-15 22:31:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([ 1.00937666e-01,  1.89315728e-01, -5.16770148e-03, -2.97419901e-02,
       -8.30986513e-05,  1.14532862e-01,  1.39635245e-02, -3.02229654e-04])
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([-1.00937666e-01, -1.89315728e-01,  5.16770160e-03,  2.97419903e-02,
        8.30986074e-05, -1.14532862e-01, -1.39635237e-02,  3.02230469e-04])
2025-07-15 22:31:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([-0.11273996, -0.11233782, -0.01437725, -0.03568005, -0.05351152,
       -0.2764868 ,  0.01652832, -0.01183537])
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([ 0.11273996,  0.11233782,  0.01437725,  0.03568005,  0.05351152,
        0.2764868 , -0.01652832,  0.01183537])
2025-07-15 22:31:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([ 0.08202566,  0.12853161,  0.00382746,  0.07489366,  0.07212601,
        0.01667284, -0.01109909,  0.02005753])
2025-07-15 22:31:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:39 - shap - INFO - phi = array([-0.08202566, -0.12853161, -0.00382746, -0.07489366, -0.07212601,
       -0.01667284,  0.01109909, -0.02005753])
2025-07-15 22:31:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.16223362, -0.15174922,  0.01907064, -0.05304711, -0.06159686,
       -0.1550568 ,  0.00074134, -0.01578367])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.16223362,  0.15174922, -0.01907064,  0.05304711,  0.06159686,
        0.1550568 , -0.00074134,  0.01578367])
2025-07-15 22:31:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.12196629,  0.13570194, -0.01811055, -0.00454181,  0.03466695,
        0.10065019,  0.00258352, -0.00775109])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.12196629, -0.13570194,  0.01811055,  0.00454181, -0.03466695,
       -0.10065019, -0.00258352,  0.00775109])
2025-07-15 22:31:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.04120332, -0.11472378,  0.        , -0.00123353, -0.05475946,
       -0.4134322 ,  0.00577667, -0.01638702])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.04120332,  0.11472378,  0.        ,  0.00123353,  0.05475946,
        0.4134322 , -0.00577667,  0.01638702])
2025-07-15 22:31:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.26916046, -0.2196781 ,  0.01589514, -0.05390892, -0.03626628,
        0.16285667,  0.00625384, -0.02773774])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.26916046,  0.2196781 , -0.01589514,  0.05390892,  0.03626628,
       -0.16285667, -0.00625384,  0.02773774])
2025-07-15 22:31:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.09678204,  0.15924932, -0.00423174, -0.03044008,  0.00080373,
        0.18210639, -0.00638702, -0.00380706])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.09678204, -0.15924932,  0.00423174,  0.03044008, -0.00080373,
       -0.18210639,  0.00638702,  0.00380706])
2025-07-15 22:31:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.12794482, -0.28137951, -0.02006786, -0.05243749, -0.04628591,
        0.12365226,  0.00499854, -0.02177754])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.12794482,  0.28137951,  0.02006786,  0.05243749,  0.04628591,
       -0.12365226, -0.00499854,  0.02177754])
2025-07-15 22:31:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.04877929, -0.06371739,  0.03062394,  0.18242334,  0.04959293,
       -0.05487494, -0.01496017,  0.09199568])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.04877929,  0.06371739, -0.03062394, -0.18242334, -0.04959293,
        0.05487494,  0.01496017, -0.09199568])
2025-07-15 22:31:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.03850987,  0.23429796,  0.01441765,  0.00123438,  0.04741385,
        0.05965396, -0.01650288, -0.01508563])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.03850987, -0.23429796, -0.01441765, -0.00123439, -0.04741385,
       -0.05965396,  0.01650288,  0.01508563])
2025-07-15 22:31:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.20819295, -0.13048259,  0.00068997, -0.04601439, -0.03557167,
        0.2411421 , -0.01493859,  0.02406958])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.20819295,  0.13048259, -0.00068997,  0.04601439,  0.03557167,
       -0.2411421 ,  0.01493859, -0.02406958])
2025-07-15 22:31:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([ 0.07649182,  0.08754515,  0.00609677, -0.04005412, -0.00561684,
        0.24998563, -0.00576197,  0.00702626])
2025-07-15 22:31:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:40 - shap - INFO - phi = array([-0.07649182, -0.08754514, -0.00609677,  0.04005412,  0.00561684,
       -0.24998563,  0.00576197, -0.00702626])
2025-07-15 22:31:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:41 - shap - INFO - phi = array([-0.27531444,  0.15639571,  0.00758019, -0.03879551, -0.0052574 ,
        0.14783913, -0.00607126,  0.02923391])
2025-07-15 22:31:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:41 - shap - INFO - phi = array([ 0.27531444, -0.15639571, -0.00758019,  0.03879551,  0.0052574 ,
       -0.14783913,  0.00607126, -0.02923391])
2025-07-15 22:31:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:41 - shap - INFO - phi = array([ 0.08905118,  0.07071227,  0.00549172,  0.0881539 ,  0.08200294,
        0.01789701, -0.01049479,  0.03401543])
2025-07-15 22:31:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:41 - shap - INFO - phi = array([-0.08905118, -0.07071227, -0.00549173, -0.0881539 , -0.08200294,
       -0.01789701,  0.01049479, -0.03401543])
2025-07-15 22:31:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:41 - shap - INFO - phi = array([ 0.05787371, -0.12069318,  0.00460223, -0.00933067, -0.05577496,
       -0.3980154 ,  0.01106688, -0.01235555])
2025-07-15 22:31:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:41 - shap - INFO - phi = array([-0.05787371,  0.12069318, -0.00460223,  0.00933067,  0.05577496,
        0.3980154 , -0.01106688,  0.01235555])
2025-07-15 22:31:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:41 - shap - INFO - phi = array([ 0.12142327,  0.11216165, -0.01567055, -0.02242057,  0.01591593,
        0.17498833, -0.01775488,  0.00129903])
2025-07-15 22:31:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:41 - shap - INFO - phi = array([-0.12142327, -0.11216165,  0.01567055,  0.02242057, -0.01591593,
       -0.17498833,  0.01775488, -0.00129904])
2025-07-15 22:31:45 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_soft
2025-07-15 22:31:45 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:31:45 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:31:45 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:31:45 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:31:45 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:31:45 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:31:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:46 - shap - INFO - phi = array([-0.1235119 , -0.11857143,  0.01553571,  0.0235119 , -0.00083333,
       -0.14863095, -0.01720238, -0.00529762])
2025-07-15 22:31:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:46 - shap - INFO - phi = array([-0.12455357,  0.10247024,  0.09544643,  0.23425595,  0.02580357,
        0.02330357,  0.14151786,  0.12675595])
2025-07-15 22:31:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:46 - shap - INFO - phi = array([ 0.08654762,  0.06553571,  0.01565476,  0.035     ,  0.03607143,
        0.38422619, -0.00886905,  0.01083333])
2025-07-15 22:31:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:46 - shap - INFO - phi = array([ 0.18065476,  0.03880952, -0.0102381 ,  0.03619048,  0.02833333,
        0.35125   , -0.01428571,  0.01428571])
2025-07-15 22:31:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:47 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:47 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:47 - shap - INFO - phi = array([-0.15157738, -0.16032738,  0.01627976,  0.0371131 ,  0.03139881,
       -0.02270833, -0.07877976, -0.04639881])
2025-07-15 22:31:47 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:47 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:47 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:47 - shap - INFO - phi = array([-0.07863095, -0.11994048, -0.01029762, -0.06714286, -0.05267857,
       -0.01541667,  0.00166667, -0.03255952])
2025-07-15 22:31:47 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:47 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:47 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:47 - shap - INFO - phi = array([-0.18919643,  0.00782738, -0.01002976,  0.02943452,  0.00354167,
       -0.2203869 ,  0.00693452, -0.003125  ])
2025-07-15 22:31:47 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:47 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:47 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:47 - shap - INFO - phi = array([-0.25184524,  0.0010119 , -0.00619048,  0.03666667,  0.0297619 ,
       -0.16142857, -0.01690476, -0.00607143])
2025-07-15 22:31:47 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:48 - shap - INFO - phi = array([ 0.28345238,  0.29166667, -0.01291667,  0.02708333,  0.0289881 ,
       -0.00154762, -0.00904762,  0.01732143])
2025-07-15 22:31:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:48 - shap - INFO - phi = array([-0.01002976, -0.19806548,  0.07883929,  0.02675595, -0.036875  ,
        0.45354167,  0.15508929,  0.15574405])
2025-07-15 22:31:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:49 - shap - INFO - phi = array([-0.059375  , -0.154375  , -0.006875  ,  0.01991071, -0.00449405,
       -0.16622024, -0.01157738,  0.00800595])
2025-07-15 22:31:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:49 - shap - INFO - phi = array([-0.09863095, -0.10809524, -0.0075    ,  0.01636905,  0.00684524,
       -0.18613095,  0.0027381 , -0.00059524])
2025-07-15 22:31:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:49 - shap - INFO - phi = array([ 0.21505952,  0.3222619 ,  0.02458333,  0.0510119 ,  0.0389881 ,
       -0.02232143, -0.0264881 ,  0.02190476])
2025-07-15 22:31:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:50 - shap - INFO - phi = array([ 0.10294643,  0.13842262,  0.01282738,  0.03122024,  0.045625  ,
        0.30794643, -0.018125  ,  0.0041369 ])
2025-07-15 22:31:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:50 - shap - INFO - phi = array([ 0.36752976,  0.28008929, -0.00592262,  0.03026786,  0.0166369 ,
       -0.06824405, -0.00991071,  0.01455357])
2025-07-15 22:31:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:51 - shap - INFO - phi = array([-0.18916667,  0.01369048, -0.01654762, -0.04464286, -0.04589286,
       -0.07904762,  0.00107143, -0.01446429])
2025-07-15 22:31:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:51 - shap - INFO - phi = array([-0.11395833, -0.11705357, -0.00080357,  0.01758929,  0.01306548,
       -0.15479167, -0.01383929, -0.00520833])
2025-07-15 22:31:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:51 - shap - INFO - phi = array([-0.11642857, -0.1439881 ,  0.00559524,  0.00470238, -0.01565476,
       -0.09755952, -0.01678571,  0.00511905])
2025-07-15 22:31:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:52 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:52 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:52 - shap - INFO - phi = array([-0.25922619,  0.03232143, -0.01345238, -0.01095238, -0.0360119 ,
       -0.11380952,  0.00988095,  0.01625   ])
2025-07-15 22:31:52 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:52 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:52 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:52 - shap - INFO - phi = array([ 0.20068452,  0.22294643,  0.01050595, -0.00883929,  0.08473214,
        0.06044643,  0.08181548, -0.02729167])
2025-07-15 22:31:52 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:53 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:53 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:53 - shap - INFO - phi = array([ 0.02196429,  0.0272619 , -0.04208333, -0.24547619, -0.15142857,
        0.1289881 , -0.04047619, -0.07375   ])
2025-07-15 22:31:53 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:53 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:53 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:53 - shap - INFO - phi = array([ 0.00880952, -0.13386905, -0.01589286, -0.11422619, -0.08767857,
       -0.01928571,  0.01005952, -0.02291667])
2025-07-15 22:31:53 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:53 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:53 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:53 - shap - INFO - phi = array([-0.08857143, -0.13577381, -0.00678571, -0.05916667, -0.06005952,
       -0.01791667, -0.00446429, -0.0022619 ])
2025-07-15 22:31:53 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:54 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:54 - shap - INFO - phi = array([-0.11613095, -0.12279762, -0.00255952,  0.0210119 ,  0.00642857,
       -0.14178571, -0.01184524, -0.00732143])
2025-07-15 22:31:54 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:54 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:54 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:54 - shap - INFO - phi = array([ 0.11785714,  0.10255952,  0.00988095,  0.03238095,  0.03386905,
        0.33761905, -0.01815476,  0.0089881 ])
2025-07-15 22:31:54 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:55 - shap - INFO - phi = array([-0.09419643, -0.10443452, -0.00866071, -0.04794643, -0.04604167,
       -0.04598214, -0.00068452, -0.02705357])
2025-07-15 22:31:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:55 - shap - INFO - phi = array([ 0.19380952,  0.15869048, -0.01279762,  0.03547619,  0.03821429,
        0.22      , -0.01291667,  0.00452381])
2025-07-15 22:31:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:55 - shap - INFO - phi = array([-0.14041667, -0.09791667,  0.0135119 ,  0.01327381, -0.00678571,
       -0.1435119 , -0.01208333, -0.00107143])
2025-07-15 22:31:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:56 - shap - INFO - phi = array([-0.05041667,  0.06666667, -0.00392857, -0.00345238,  0.04958333,
        0.5702381 , -0.01821429,  0.01452381])
2025-07-15 22:31:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:56 - shap - INFO - phi = array([ 0.40395833,  0.28973214, -0.009375  ,  0.03455357,  0.02580357,
       -0.12401786, -0.01139881,  0.01574405])
2025-07-15 22:31:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:56 - shap - INFO - phi = array([-0.10568452, -0.1158631 , -0.00735119,  0.02193452,  0.00675595,
       -0.16395833, -0.00491071, -0.00592262])
2025-07-15 22:31:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:57 - shap - INFO - phi = array([ 0.23633929,  0.37342262,  0.00514881,  0.04633929,  0.06169643,
       -0.10270833, -0.0158631 ,  0.020625  ])
2025-07-15 22:31:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:57 - shap - INFO - phi = array([ 0.01044643,  0.02973214, -0.02776786, -0.27818452, -0.06002976,
        0.03383929,  0.00383929, -0.086875  ])
2025-07-15 22:31:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:58 - shap - INFO - phi = array([ 0.02970238, -0.26440476, -0.0085119 ,  0.00440476, -0.0347619 ,
       -0.11154762,  0.00958333,  0.00053571])
2025-07-15 22:31:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:58 - shap - INFO - phi = array([ 0.11785714,  0.0160119 , -0.02934524,  0.01059524,  0.02      ,
       -0.48696429,  0.0027381 , -0.02589286])
2025-07-15 22:31:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:58 - shap - INFO - phi = array([-0.05675595, -0.07705357, -0.01425595,  0.01395833,  0.00508929,
       -0.22622024, -0.0083631 , -0.01139881])
2025-07-15 22:31:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:59 - shap - INFO - phi = array([ 0.13666667, -0.29345238, -0.00702381,  0.0172619 ,  0.00625   ,
       -0.20261905, -0.00261905, -0.02946429])
2025-07-15 22:31:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:31:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:31:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:31:59 - shap - INFO - phi = array([-0.09895833, -0.07401786, -0.00854167, -0.05449405, -0.05110119,
       -0.05127976, -0.00300595, -0.03360119])
2025-07-15 22:31:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:00 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:00 - shap - INFO - phi = array([-0.07291667,  0.06672619, -0.00845238,  0.01285714,  0.05071429,
        0.5925    , -0.0272619 ,  0.01083333])
2025-07-15 22:32:00 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:00 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:00 - shap - INFO - phi = array([-0.12410714, -0.10785714,  0.01029762,  0.01339286, -0.00386905,
       -0.16785714,  0.00714286, -0.00214286])
2025-07-15 22:32:05 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_hard
2025-07-15 22:32:05 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:32:05 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:32:05 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:32:05 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:32:05 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:32:05 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 22:32:05 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 22:32:05 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:05 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:05 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:05 - shap - INFO - phi = array([ 0.10655816,  0.1385767 , -0.01430297, -0.02427109,  0.0003371 ,
        0.11901677,  0.01376656,  0.00372461])
2025-07-15 22:32:05 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:05 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:05 - shap - INFO - phi = array([-0.10655816, -0.1385767 ,  0.01430297,  0.02427109, -0.0003371 ,
       -0.11901677, -0.01376656, -0.00372461])
2025-07-15 22:32:05 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:05 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:05 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:05 - shap - INFO - phi = array([ 0.12585436, -0.07026963, -0.03970729, -0.08601488,  0.0294808 ,
        0.01937712, -0.03548646, -0.03261465])
2025-07-15 22:32:05 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:05 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:05 - shap - INFO - phi = array([-0.12585436,  0.07026963,  0.03970729,  0.08601488, -0.0294808 ,
       -0.01937712,  0.03548646,  0.03261465])
2025-07-15 22:32:05 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([-0.08944492, -0.08901438, -0.01293833, -0.02770324, -0.04294857,
       -0.29942513,  0.00108786, -0.01681947])
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([ 0.08944492,  0.08901438,  0.01293833,  0.02770324,  0.04294857,
        0.29942513, -0.00108786,  0.01681947])
2025-07-15 22:32:06 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([-0.16770715, -0.02110286,  0.01312736, -0.03723211, -0.04613879,
       -0.29753468,  0.00661847, -0.0197315 ])
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([ 0.16770715,  0.02110286, -0.01312736,  0.03723211,  0.04613879,
        0.29753468, -0.00661847,  0.0197315 ])
2025-07-15 22:32:06 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([ 0.12454941,  0.16075182, -0.01298557, -0.02744294, -0.05397908,
       -0.00507284,  0.08063321,  0.04091462])
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([-0.12454941, -0.16075182,  0.01298557,  0.02744294,  0.05397908,
        0.00507284, -0.08063321, -0.04091462])
2025-07-15 22:32:06 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([ 0.07309082,  0.12060945,  0.00511314,  0.06967789,  0.05798335,
        0.00856725, -0.00561805,  0.0248483 ])
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([-0.07309082, -0.12060945, -0.00511314, -0.06967789, -0.05798335,
       -0.00856725,  0.00561805, -0.0248483 ])
2025-07-15 22:32:06 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([ 0.16695054, -0.0336381 , -0.00178633, -0.03485225,  0.00195661,
        0.24711528, -0.01032348, -0.01174859])
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([-0.16695054,  0.0336381 ,  0.00178633,  0.03485225, -0.00195661,
       -0.24711528,  0.01032348,  0.01174859])
2025-07-15 22:32:06 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([ 0.16722871, -0.04544942, -0.01108736, -0.04473574, -0.0514804 ,
        0.10048382,  0.04230212,  0.01140817])
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([-0.16722871,  0.04544942,  0.01108736,  0.04473574,  0.0514804 ,
       -0.10048382, -0.04230212, -0.01140817])
2025-07-15 22:32:06 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([-0.19559513, -0.25419534,  0.01365808, -0.04405074, -0.04761878,
       -0.00958368,  0.00452362, -0.02043047])
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([ 0.19559513,  0.25419534, -0.01365808,  0.04405074,  0.04761878,
        0.00958368, -0.00452362,  0.02043047])
2025-07-15 22:32:06 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([ 0.06685729,  0.20076691, -0.00417941, -0.03042248,  0.04172865,
       -0.37712807, -0.03818367, -0.03293092])
2025-07-15 22:32:06 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:06 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:06 - shap - INFO - phi = array([-0.06685729, -0.20076691,  0.00417941,  0.03042248, -0.04172865,
        0.37712807,  0.03818367,  0.03293092])
2025-07-15 22:32:06 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([ 0.05978038,  0.17117898,  0.00583068, -0.02854667, -0.00474558,
        0.15838431,  0.00352722, -0.01196678])
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([-0.05978038, -0.17117898, -0.00583068,  0.02854667,  0.00474558,
       -0.15838431, -0.00352722,  0.01196678])
2025-07-15 22:32:07 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([ 8.58937448e-02,  1.32433762e-01,  8.82644830e-05, -2.77585704e-02,
       -3.78780770e-03,  1.83736245e-01, -5.83741882e-03, -9.37900225e-03])
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([-8.58937448e-02, -1.32433762e-01, -8.82644830e-05,  2.77585704e-02,
        3.78780770e-03, -1.83736245e-01,  5.83741882e-03,  9.37900225e-03])
2025-07-15 22:32:07 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([-1.27253024e-01, -3.00667015e-01, -2.80074233e-02, -4.56849124e-02,
       -5.81310036e-02, -1.35180475e-04,  3.04241814e-02, -2.94072603e-02])
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([ 1.27253024e-01,  3.00667015e-01,  2.80074233e-02,  4.56849124e-02,
        5.81310036e-02,  1.35180475e-04, -3.04241814e-02,  2.94072603e-02])
2025-07-15 22:32:07 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([-0.08209596, -0.1537581 , -0.01496169, -0.0310011 , -0.0517587 ,
       -0.24554912,  0.01458027, -0.01255899])
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([ 0.08209596,  0.1537581 ,  0.01496169,  0.0310011 ,  0.0517587 ,
        0.24554912, -0.01458027,  0.01255899])
2025-07-15 22:32:07 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([-2.02517252e-01, -2.30114738e-01,  1.85257656e-04, -4.45203721e-02,
       -2.86003421e-02,  6.72030731e-02,  9.68106864e-03, -2.20027039e-02])
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([ 2.02517252e-01,  2.30114738e-01, -1.85257656e-04,  4.45203721e-02,
        2.86003421e-02, -6.72030731e-02, -9.68106864e-03,  2.20027039e-02])
2025-07-15 22:32:07 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([ 0.14287416, -0.0321101 ,  0.01180362,  0.08271726,  0.08634844,
        0.03136883, -0.00876253,  0.01749004])
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([-0.14287416,  0.0321101 , -0.01180362, -0.08271726, -0.08634844,
       -0.03136883,  0.00876253, -0.01749004])
2025-07-15 22:32:07 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([ 0.0982177 ,  0.15296708, -0.00867688, -0.02306988, -0.0163402 ,
        0.13939297,  0.00927413,  0.0009056 ])
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([-0.0982177 , -0.15296708,  0.00867688,  0.02306988,  0.0163402 ,
       -0.13939297, -0.00927413, -0.0009056 ])
2025-07-15 22:32:07 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([ 0.09560954,  0.19385024, -0.0137921 , -0.01105284,  0.0184822 ,
        0.07921713,  0.00385119, -0.01099951])
2025-07-15 22:32:07 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:07 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:07 - shap - INFO - phi = array([-0.09560954, -0.19385024,  0.0137921 ,  0.01105284, -0.0184822 ,
       -0.07921713, -0.00385119,  0.01099951])
2025-07-15 22:32:07 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([ 0.1706597 , -0.073709  ,  0.00774034,  0.04464726,  0.09166433,
        0.04036858, -0.02180732, -0.0277912 ])
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([-0.1706597 ,  0.073709  , -0.00774034, -0.04464726, -0.09166433,
       -0.04036858,  0.02180732,  0.0277912 ])
2025-07-15 22:32:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([-0.08904843, -0.18822383,  0.02759506,  0.06849835, -0.0315081 ,
       -0.01363841, -0.01828863,  0.05860521])
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([ 0.08904843,  0.18822383, -0.02759506, -0.06849835,  0.0315081 ,
        0.01363841,  0.01828863, -0.05860521])
2025-07-15 22:32:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([-0.09947528, -0.11117028,  0.02271731,  0.15064583,  0.05022434,
       -0.17285216, -0.02316333,  0.0154219 ])
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([ 0.09947528,  0.11117028, -0.02271731, -0.15064583, -0.05022434,
        0.17285216,  0.02316333, -0.0154219 ])
2025-07-15 22:32:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([-0.01803507,  0.11436722,  0.01828899,  0.13390728,  0.08589824,
       -0.01497156, -0.01839071,  0.01584488])
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([ 0.01803507, -0.11436722, -0.01828899, -0.13390728, -0.08589824,
        0.01497156,  0.01839071, -0.01584488])
2025-07-15 22:32:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([ 0.08211415,  0.1432682 ,  0.00411622,  0.0633836 ,  0.06153391,
        0.01327843, -0.00420766, -0.00888435])
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([-0.08211415, -0.1432682 , -0.00411622, -0.0633836 , -0.06153391,
       -0.01327843,  0.00420766,  0.00888435])
2025-07-15 22:32:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([ 0.09225971,  0.1720475 , -0.00632947, -0.02565367,  0.00029223,
        0.11175789,  0.00825247,  0.00181978])
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([-0.09225971, -0.1720475 ,  0.00632947,  0.02565367, -0.00029223,
       -0.11175789, -0.00825247, -0.00181978])
2025-07-15 22:32:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([-0.10525655, -0.10850082, -0.01160852, -0.03281105, -0.05194494,
       -0.27326384,  0.01512981, -0.0092782 ])
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([ 0.10525655,  0.10850082,  0.01160852,  0.03281105,  0.05194494,
        0.27326384, -0.01512981,  0.0092782 ])
2025-07-15 22:32:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([ 0.07641273,  0.11379532,  0.00448015,  0.05807614,  0.06067705,
        0.02814499, -0.00482894,  0.01830938])
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:08 - shap - INFO - phi = array([-0.07641273, -0.11379532, -0.00448015, -0.05807614, -0.06067705,
       -0.02814499,  0.00482894, -0.01830938])
2025-07-15 22:32:08 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:08 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:08 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([-0.16324711, -0.15613668,  0.01803944, -0.04258523, -0.05961462,
       -0.15392129,  0.00198434, -0.01528339])
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([ 0.16324711,  0.15613668, -0.01803944,  0.04258523,  0.05961462,
        0.15392129, -0.00198434,  0.01528339])
2025-07-15 22:32:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([ 0.1171948 ,  0.13939682, -0.01704399, -0.01279853,  0.02725771,
        0.10425373,  0.00093972, -0.00938957])
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([-0.1171948 , -0.13939682,  0.01704399,  0.01279853, -0.02725771,
       -0.10425373, -0.00093972,  0.00938957])
2025-07-15 22:32:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([ 0.04409233, -0.09849516,  0.00103982, -0.00128693, -0.05621816,
       -0.43899038,  0.00440521, -0.01825618])
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([-0.04409233,  0.09849516, -0.00103982,  0.00128693,  0.05621816,
        0.43899038, -0.00440521,  0.01825618])
2025-07-15 22:32:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([-0.30569882, -0.2404696 ,  0.01527144, -0.04630753, -0.03595695,
        0.15135769,  0.00455714, -0.03078134])
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([ 0.30569882,  0.2404696 , -0.01527144,  0.04630753,  0.03595695,
       -0.15135769, -0.00455714,  0.03078134])
2025-07-15 22:32:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([ 0.08788589,  0.14254874, -0.00492252, -0.02525816,  0.00073057,
        0.16607853, -0.00584652, -0.00480872])
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([-0.08788589, -0.14254874,  0.00492252,  0.02525816, -0.00073057,
       -0.16607853,  0.00584652,  0.00480872])
2025-07-15 22:32:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([-0.14966782, -0.30807632, -0.01987451, -0.05355003, -0.05261168,
        0.10501959,  0.00205374, -0.02536405])
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([ 0.14966782,  0.30807632,  0.01987451,  0.05355003,  0.05261168,
       -0.10501959, -0.00205374,  0.02536405])
2025-07-15 22:32:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([-0.03484543, -0.05272634,  0.03238622,  0.20889782,  0.06212295,
       -0.04237614, -0.01016148,  0.10210638])
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([ 0.03484543,  0.05272634, -0.03238622, -0.20889782, -0.06212295,
        0.04237614,  0.01016148, -0.10210638])
2025-07-15 22:32:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([-0.02375562,  0.25458047,  0.00923224, -0.0051106 ,  0.04482899,
        0.07240974, -0.01267824, -0.01525389])
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([ 0.02375562, -0.25458047, -0.00923224,  0.0051106 , -0.04482899,
       -0.07240974,  0.01267824,  0.01525389])
2025-07-15 22:32:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([-0.23152568, -0.1346432 , -0.00214419, -0.04154517, -0.04118622,
        0.25280782, -0.01600894,  0.02879662])
2025-07-15 22:32:09 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:09 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:09 - shap - INFO - phi = array([ 0.23152568,  0.1346432 ,  0.00214419,  0.04154517,  0.04118622,
       -0.25280782,  0.01600894, -0.02879662])
2025-07-15 22:32:09 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([ 0.06996615,  0.08496004,  0.00834603, -0.03065232, -0.00564998,
        0.22297481, -0.00496345,  0.00656106])
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([-0.06996615, -0.08496004, -0.00834603,  0.03065232,  0.00564998,
       -0.22297481,  0.00496345, -0.00656106])
2025-07-15 22:32:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([-0.22535057,  0.19222747,  0.01107708, -0.02841924, -0.00387509,
        0.1557713 , -0.0024226 ,  0.03350207])
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([ 0.22535057, -0.19222747, -0.01107708,  0.02841924,  0.00387509,
       -0.1557713 ,  0.0024226 , -0.03350207])
2025-07-15 22:32:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([ 0.084313  ,  0.07175352,  0.00527766,  0.06803328,  0.06863349,
        0.03130103, -0.00481592,  0.02880754])
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([-0.084313  , -0.07175352, -0.00527766, -0.06803328, -0.06863349,
       -0.03130103,  0.00481592, -0.02880754])
2025-07-15 22:32:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([ 0.05708978, -0.10702998,  0.0035977 , -0.01075141, -0.05906875,
       -0.4288608 ,  0.00797379, -0.01419516])
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([-0.05708978,  0.10702998, -0.0035977 ,  0.01075141,  0.05906875,
        0.4288608 , -0.00797379,  0.01419516])
2025-07-15 22:32:10 - shap - INFO - num_full_subsets = 4
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([ 0.11506678,  0.10942064, -0.01466355, -0.02339503,  0.01298279,
        0.16391636, -0.01377078,  0.00080972])
2025-07-15 22:32:10 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:32:10 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:32:10 - shap - INFO - phi = array([-0.11506678, -0.10942064,  0.01466355,  0.02339503, -0.01298279,
       -0.16391636,  0.01377078, -0.00080972])
2025-07-15 22:32:15 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-15 22:32:15 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:32:15 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:32:15 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:32:15 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:32:36 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:32:36 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-15 22:32:36 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
2025-07-15 22:34:08 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:34:09 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-15 22:34:09 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
2025-07-15 22:34:22 - model_ensemble - INFO - ============================================================
2025-07-15 22:34:22 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:34:22 - model_ensemble - INFO - ============================================================
2025-07-15 22:34:22 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic', 'SVM', 'KNN']
2025-07-15 22:34:22 - model_ensemble - INFO - 集成方法: ['voting', 'bagging', 'boosting', 'stacking']
2025-07-15 22:34:22 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:34:22 - model_training - INFO - 模型名称: Random Forest
2025-07-15 22:34:22 - model_training - INFO - 准确率: 0.9000
2025-07-15 22:34:22 - model_training - INFO - AUC: 0.9386
2025-07-15 22:34:22 - model_training - INFO - 混淆矩阵:
2025-07-15 22:34:22 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-15 22:34:22 - model_training - INFO - 
分类报告:
2025-07-15 22:34:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-15 22:34:22 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 22:34:22 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 22:34:22 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 22:34:22 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:34:22 - model_training - INFO - 模型名称: XGBoost
2025-07-15 22:34:22 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:34:22 - model_training - INFO - AUC: 0.9668
2025-07-15 22:34:22 - model_training - INFO - 混淆矩阵:
2025-07-15 22:34:22 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:34:22 - model_training - INFO - 
分类报告:
2025-07-15 22:34:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:34:22 - model_training - INFO - 训练时间: 0.04 秒
2025-07-15 22:34:22 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 22:34:22 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 22:34:22 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 22:34:22 - model_training - INFO - 模型名称: LightGBM
2025-07-15 22:34:22 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:34:22 - model_training - INFO - AUC: 0.9463
2025-07-15 22:34:22 - model_training - INFO - 混淆矩阵:
2025-07-15 22:34:22 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:34:22 - model_training - INFO - 
分类报告:
2025-07-15 22:34:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:34:22 - model_training - INFO - 训练时间: 0.23 秒
2025-07-15 22:34:22 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 22:34:22 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 22:34:22 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 22:34:22 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 22:34:22 - model_training - INFO - 准确率: 0.8500
2025-07-15 22:34:22 - model_training - INFO - AUC: 0.9284
2025-07-15 22:34:22 - model_training - INFO - 混淆矩阵:
2025-07-15 22:34:22 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-07-15 22:34:22 - model_training - INFO - 
分类报告:
2025-07-15 22:34:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-07-15 22:34:22 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:34:22 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 22:34:22 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 22:34:22 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:34:22 - model_training - INFO - 模型名称: SVM
2025-07-15 22:34:22 - model_training - INFO - 准确率: 0.8000
2025-07-15 22:34:22 - model_training - INFO - AUC: 0.9207
2025-07-15 22:34:22 - model_training - INFO - 混淆矩阵:
2025-07-15 22:34:22 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-07-15 22:34:22 - model_training - INFO - 
分类报告:
2025-07-15 22:34:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-07-15 22:34:22 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:34:22 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-15 22:34:22 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-15 22:34:22 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-15 22:34:22 - model_training - INFO - 模型名称: KNN
2025-07-15 22:34:22 - model_training - INFO - 准确率: 0.8750
2025-07-15 22:34:22 - model_training - INFO - AUC: 0.9322
2025-07-15 22:34:22 - model_training - INFO - 混淆矩阵:
2025-07-15 22:34:22 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 22:34:22 - model_training - INFO - 
分类报告:
2025-07-15 22:34:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 22:34:22 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 22:34:22 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-15 22:34:22 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-15 22:34:22 - model_ensemble - INFO -   KNN 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 成功训练了 6 个基础模型
2025-07-15 22:34:22 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:34:22 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:34:22 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:34:23 - model_ensemble - INFO -     voting_soft - 准确率: 0.8500, F1: 0.8500
2025-07-15 22:34:23 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:34:23 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:34:23 - model_ensemble - INFO -     voting_hard - 准确率: 0.8500, F1: 0.8484
2025-07-15 22:34:23 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-07-15 22:34:23 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-07-15 22:34:23 - model_ensemble - ERROR - 集成方法 bagging 失败: __init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 22:34:23 - model_ensemble - INFO - 步骤2: 运行集成方法 - boosting
2025-07-15 22:34:23 - model_ensemble - INFO - 开始训练集成模型，方法: boosting
2025-07-15 22:34:23 - model_ensemble - ERROR - 集成方法 boosting 失败: __init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 22:34:23 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:34:23 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:34:25 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:34:25 - model_ensemble - INFO - ============================================================
2025-07-15 22:34:25 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:34:25 - model_ensemble - INFO - ============================================================
2025-07-15 22:34:25 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 22:34:25 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 22:34:25 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 22:34:25 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:34:25 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8500, 精确率: 0.8500, 召回率: 0.8500, F1: 0.8500, AUC: 0.9591
2025-07-15 22:34:25 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8500, 精确率: 0.8513, 召回率: 0.8500, F1: 0.8484, AUC: 0.0000
2025-07-15 22:34:25 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9540
2025-07-15 22:34:25 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:34:25 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_223425.joblib
2025-07-15 22:34:25 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-15 22:34:25 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:34:25 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:34:25 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:34:25 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:34:25 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:25 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:25 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:25 - shap - INFO - phi = array([ 0.1077149 ,  0.12592475, -0.01294599, -0.02135643,  0.00097586,
        0.11331763,  0.02101725,  0.00704157])
2025-07-15 22:34:25 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:25 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:25 - shap - INFO - phi = array([-0.1077149 , -0.12592475,  0.01294599,  0.02135643, -0.00097586,
       -0.11331763, -0.02101725, -0.00704157])
2025-07-15 22:34:25 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:25 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:25 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:25 - shap - INFO - phi = array([ 0.11279681, -0.08884771, -0.031101  , -0.07805242,  0.02008731,
        0.01524405, -0.0314305 , -0.0231513 ])
2025-07-15 22:34:25 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:25 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:25 - shap - INFO - phi = array([-0.11279681,  0.08884771,  0.031101  ,  0.07805242, -0.02008731,
       -0.01524405,  0.0314305 ,  0.0231513 ])
2025-07-15 22:34:25 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:25 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:25 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:25 - shap - INFO - phi = array([-0.0954303 , -0.09702581, -0.01682425, -0.02761012, -0.0439616 ,
       -0.30255584,  0.00090825, -0.01583146])
2025-07-15 22:34:25 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:25 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:25 - shap - INFO - phi = array([ 0.0954303 ,  0.09702581,  0.01682425,  0.02761012,  0.0439616 ,
        0.30255584, -0.00090825,  0.01583146])
2025-07-15 22:34:25 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:25 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:25 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.16673367, -0.02258822,  0.01348127, -0.04463017, -0.04911601,
       -0.29385423,  0.00561559, -0.01918483])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.16673367,  0.02258822, -0.01348127,  0.04463017,  0.04911601,
        0.29385423, -0.00561559,  0.01918483])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.11316882,  0.13457292, -0.0097554 , -0.02384711, -0.05936559,
       -0.03323029,  0.08932662,  0.03084891])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.11316882, -0.13457293,  0.00975541,  0.02384711,  0.05936559,
        0.03323029, -0.08932662, -0.03084891])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.07871614,  0.13362809,  0.00463032,  0.08822635,  0.06864803,
       -0.00626652, -0.01112925,  0.02730317])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.07871614, -0.13362809, -0.00463032, -0.08822635, -0.06864803,
        0.00626652,  0.01112925, -0.02730317])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.1544761 , -0.05736398, -0.00092502, -0.03492738,  0.00383034,
        0.24596461, -0.01241657, -0.01060954])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.1544761 ,  0.05736398,  0.00092502,  0.03492738, -0.00383034,
       -0.24596461,  0.01241657,  0.01060954])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.14193988, -0.07832072, -0.01017671, -0.04569841, -0.05004464,
        0.07946547,  0.04320784,  0.00220154])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.14193988,  0.07832072,  0.01017671,  0.04569841,  0.05004464,
       -0.07946547, -0.04320784, -0.00220154])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.18839226, -0.22952706,  0.01621134, -0.05247148, -0.04607668,
       -0.00870341,  0.0047954 , -0.02051125])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.18839226,  0.22952706, -0.01621134,  0.05247148,  0.04607668,
        0.00870341, -0.0047954 ,  0.02051125])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.06408485,  0.19547134, -0.00329256, -0.02849226,  0.03134693,
       -0.37420462, -0.02959984, -0.01939791])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.06408485, -0.19547134,  0.00329256,  0.02849226, -0.03134693,
        0.37420462,  0.02959984,  0.01939791])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.06075206,  0.19175997,  0.00757974, -0.03677675, -0.00435031,
        0.17169242,  0.00454898, -0.01271317])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.06075206, -0.19175997, -0.00757974,  0.03677675,  0.00435031,
       -0.17169242, -0.00454898,  0.01271317])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.09339006,  0.14833589,  0.00094568, -0.0356317 , -0.00371225,
        0.20379567, -0.00673777, -0.01027582])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.09339006, -0.1483359 , -0.00094568,  0.0356317 ,  0.00371225,
       -0.20379567,  0.00673777,  0.01027583])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([-0.12593768, -0.27856917, -0.03291918, -0.04522582, -0.06104538,
        0.00211091,  0.03525434, -0.03330172])
2025-07-15 22:34:26 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:26 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:26 - shap - INFO - phi = array([ 0.12593768,  0.27856917,  0.03291918,  0.04522582,  0.06104538,
       -0.00211091, -0.03525434,  0.03330171])
2025-07-15 22:34:26 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([-0.08880232, -0.15859857, -0.01807558, -0.03265092, -0.05186503,
       -0.24858371,  0.01542665, -0.01595142])
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([ 0.08880232,  0.15859857,  0.01807558,  0.03265092,  0.05186503,
        0.24858371, -0.01542665,  0.01595142])
2025-07-15 22:34:27 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([-0.19005673, -0.19860009,  0.00106322, -0.04440146, -0.02496481,
        0.06915909,  0.01139211, -0.01829617])
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([ 0.19005673,  0.19860009, -0.00106322,  0.04440146,  0.02496481,
       -0.06915909, -0.01139211,  0.01829617])
2025-07-15 22:34:27 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([ 0.13309954, -0.04895196,  0.01239153,  0.09515142,  0.0851543 ,
        0.01425941, -0.01088336,  0.02180491])
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([-0.13309954,  0.04895196, -0.01239153, -0.09515142, -0.0851543 ,
       -0.01425941,  0.01088336, -0.02180491])
2025-07-15 22:34:27 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([ 0.10633389,  0.16246993, -0.0080498 , -0.02534691, -0.01986729,
        0.14597602,  0.01532443, -0.00055412])
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([-0.10633389, -0.16246993,  0.0080498 ,  0.02534691,  0.01986729,
       -0.14597602, -0.01532443,  0.00055412])
2025-07-15 22:34:27 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([ 0.10523683,  0.20984249, -0.01445548, -0.00590727,  0.02114743,
        0.07928079,  0.00501471, -0.01144772])
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([-0.10523683, -0.20984249,  0.01445548,  0.00590727, -0.02114743,
       -0.07928079, -0.00501471,  0.01144772])
2025-07-15 22:34:27 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([ 0.1463103 , -0.10257378,  0.00686362,  0.04104952,  0.07291263,
        0.02229393, -0.02596869, -0.02824841])
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([-0.1463103 ,  0.10257378, -0.00686362, -0.04104952, -0.07291262,
       -0.02229393,  0.02596869,  0.02824841])
2025-07-15 22:34:27 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([-0.07338929, -0.17754989,  0.02317445,  0.06841507, -0.02576587,
       -0.00156487, -0.02161744,  0.05289586])
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([ 0.07338929,  0.17754989, -0.02317445, -0.06841507,  0.02576587,
        0.00156487,  0.02161744, -0.05289586])
2025-07-15 22:34:27 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([-0.09301574, -0.10970144,  0.01764004,  0.13880316,  0.04041763,
       -0.15220307, -0.02227602,  0.01443611])
2025-07-15 22:34:27 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:27 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:27 - shap - INFO - phi = array([ 0.09301574,  0.10970144, -0.01764004, -0.13880316, -0.04041763,
        0.15220307,  0.02227602, -0.01443611])
2025-07-15 22:34:27 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([-0.02964984,  0.10378637,  0.02015593,  0.13955562,  0.08056038,
       -0.03840405, -0.0239042 ,  0.01710177])
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([ 0.02964984, -0.10378636, -0.02015593, -0.13955562, -0.08056038,
        0.03840405,  0.0239042 , -0.01710177])
2025-07-15 22:34:28 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([ 0.08850585,  0.16327464,  0.00372961,  0.08276416,  0.06725401,
        0.0028971 , -0.01381277, -0.00903578])
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([-0.08850585, -0.16327464, -0.00372961, -0.08276416, -0.06725401,
       -0.0028971 ,  0.01381277,  0.00903578])
2025-07-15 22:34:28 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([ 1.00937666e-01,  1.89315728e-01, -5.16770148e-03, -2.97419901e-02,
       -8.30986513e-05,  1.14532862e-01,  1.39635245e-02, -3.02229654e-04])
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([-1.00937666e-01, -1.89315728e-01,  5.16770160e-03,  2.97419903e-02,
        8.30986074e-05, -1.14532862e-01, -1.39635237e-02,  3.02230469e-04])
2025-07-15 22:34:28 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([-0.11273996, -0.11233782, -0.01437725, -0.03568005, -0.05351152,
       -0.2764868 ,  0.01652832, -0.01183537])
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([ 0.11273996,  0.11233782,  0.01437725,  0.03568005,  0.05351152,
        0.2764868 , -0.01652832,  0.01183537])
2025-07-15 22:34:28 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([ 0.08202566,  0.12853161,  0.00382746,  0.07489366,  0.07212601,
        0.01667284, -0.01109909,  0.02005753])
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([-0.08202566, -0.12853161, -0.00382746, -0.07489366, -0.07212601,
       -0.01667284,  0.01109909, -0.02005753])
2025-07-15 22:34:28 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([-0.16223362, -0.15174922,  0.01907064, -0.05304711, -0.06159686,
       -0.1550568 ,  0.00074134, -0.01578367])
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([ 0.16223362,  0.15174922, -0.01907064,  0.05304711,  0.06159686,
        0.1550568 , -0.00074134,  0.01578367])
2025-07-15 22:34:28 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([ 0.12196629,  0.13570194, -0.01811055, -0.00454181,  0.03466695,
        0.10065019,  0.00258352, -0.00775109])
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([-0.12196629, -0.13570194,  0.01811055,  0.00454181, -0.03466695,
       -0.10065019, -0.00258352,  0.00775109])
2025-07-15 22:34:28 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([ 0.04120332, -0.11472378,  0.        , -0.00123353, -0.05475946,
       -0.4134322 ,  0.00577667, -0.01638702])
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([-0.04120332,  0.11472378,  0.        ,  0.00123353,  0.05475946,
        0.4134322 , -0.00577667,  0.01638702])
2025-07-15 22:34:28 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([-0.26916046, -0.2196781 ,  0.01589514, -0.05390892, -0.03626628,
        0.16285667,  0.00625384, -0.02773774])
2025-07-15 22:34:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:28 - shap - INFO - phi = array([ 0.26916046,  0.2196781 , -0.01589514,  0.05390892,  0.03626628,
       -0.16285667, -0.00625384,  0.02773774])
2025-07-15 22:34:28 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.09678204,  0.15924932, -0.00423174, -0.03044008,  0.00080373,
        0.18210639, -0.00638702, -0.00380706])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.09678204, -0.15924932,  0.00423174,  0.03044008, -0.00080373,
       -0.18210639,  0.00638702,  0.00380706])
2025-07-15 22:34:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.12794482, -0.28137951, -0.02006786, -0.05243749, -0.04628591,
        0.12365226,  0.00499854, -0.02177754])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.12794482,  0.28137951,  0.02006786,  0.05243749,  0.04628591,
       -0.12365226, -0.00499854,  0.02177754])
2025-07-15 22:34:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.04877929, -0.06371739,  0.03062394,  0.18242334,  0.04959293,
       -0.05487494, -0.01496017,  0.09199568])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.04877929,  0.06371739, -0.03062394, -0.18242334, -0.04959293,
        0.05487494,  0.01496017, -0.09199568])
2025-07-15 22:34:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.03850987,  0.23429796,  0.01441765,  0.00123438,  0.04741385,
        0.05965396, -0.01650288, -0.01508563])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.03850987, -0.23429796, -0.01441765, -0.00123439, -0.04741385,
       -0.05965396,  0.01650288,  0.01508563])
2025-07-15 22:34:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.20819295, -0.13048259,  0.00068997, -0.04601439, -0.03557167,
        0.2411421 , -0.01493859,  0.02406958])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.20819295,  0.13048259, -0.00068997,  0.04601439,  0.03557167,
       -0.2411421 ,  0.01493859, -0.02406958])
2025-07-15 22:34:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.07649182,  0.08754515,  0.00609677, -0.04005412, -0.00561684,
        0.24998563, -0.00576197,  0.00702626])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.07649182, -0.08754514, -0.00609677,  0.04005412,  0.00561684,
       -0.24998563,  0.00576197, -0.00702626])
2025-07-15 22:34:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.27531444,  0.15639571,  0.00758019, -0.03879551, -0.0052574 ,
        0.14783913, -0.00607126,  0.02923391])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.27531444, -0.15639571, -0.00758019,  0.03879551,  0.0052574 ,
       -0.14783913,  0.00607126, -0.02923391])
2025-07-15 22:34:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.08905118,  0.07071227,  0.00549172,  0.0881539 ,  0.08200294,
        0.01789701, -0.01049479,  0.03401543])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.08905118, -0.07071227, -0.00549173, -0.0881539 , -0.08200294,
       -0.01789701,  0.01049479, -0.03401543])
2025-07-15 22:34:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.05787371, -0.12069318,  0.00460223, -0.00933067, -0.05577496,
       -0.3980154 ,  0.01106688, -0.01235555])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.05787371,  0.12069318, -0.00460223,  0.00933067,  0.05577496,
        0.3980154 , -0.01106688,  0.01235555])
2025-07-15 22:34:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([ 0.12142327,  0.11216165, -0.01567055, -0.02242057,  0.01591593,
        0.17498833, -0.01775488,  0.00129903])
2025-07-15 22:34:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:29 - shap - INFO - phi = array([-0.12142327, -0.11216165,  0.01567055,  0.02242057, -0.01591593,
       -0.17498833,  0.01775488, -0.00129904])
2025-07-15 22:34:34 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_soft
2025-07-15 22:34:34 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:34:34 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:34:34 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:34:34 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:34:34 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:34:34 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:34:34 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:34 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:34 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:34 - shap - INFO - phi = array([-0.1235119 , -0.11857143,  0.01553571,  0.0235119 , -0.00083333,
       -0.14863095, -0.01720238, -0.00529762])
2025-07-15 22:34:35 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:35 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:35 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:35 - shap - INFO - phi = array([-0.12455357,  0.10247024,  0.09544643,  0.23425595,  0.02580357,
        0.02330357,  0.14151786,  0.12675595])
2025-07-15 22:34:35 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:35 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:35 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:35 - shap - INFO - phi = array([ 0.08654762,  0.06553571,  0.01565476,  0.035     ,  0.03607143,
        0.38422619, -0.00886905,  0.01083333])
2025-07-15 22:34:35 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:36 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:36 - shap - INFO - phi = array([ 0.18065476,  0.03880952, -0.0102381 ,  0.03619048,  0.02833333,
        0.35125   , -0.01428571,  0.01428571])
2025-07-15 22:34:36 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:36 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:36 - shap - INFO - phi = array([-0.15157738, -0.16032738,  0.01627976,  0.0371131 ,  0.03139881,
       -0.02270833, -0.07877976, -0.04639881])
2025-07-15 22:34:36 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:36 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:36 - shap - INFO - phi = array([-0.07863095, -0.11994048, -0.01029762, -0.06714286, -0.05267857,
       -0.01541667,  0.00166667, -0.03255952])
2025-07-15 22:34:36 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:37 - shap - INFO - phi = array([-0.18919643,  0.00782738, -0.01002976,  0.02943452,  0.00354167,
       -0.2203869 ,  0.00693452, -0.003125  ])
2025-07-15 22:34:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:37 - shap - INFO - phi = array([-0.25184524,  0.0010119 , -0.00619048,  0.03666667,  0.0297619 ,
       -0.16142857, -0.01690476, -0.00607143])
2025-07-15 22:34:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:38 - shap - INFO - phi = array([ 0.28345238,  0.29166667, -0.01291667,  0.02708333,  0.0289881 ,
       -0.00154762, -0.00904762,  0.01732143])
2025-07-15 22:34:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:38 - shap - INFO - phi = array([-0.01002976, -0.19806548,  0.07883929,  0.02675595, -0.036875  ,
        0.45354167,  0.15508929,  0.15574405])
2025-07-15 22:34:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:38 - shap - INFO - phi = array([-0.059375  , -0.154375  , -0.006875  ,  0.01991071, -0.00449405,
       -0.16622024, -0.01157738,  0.00800595])
2025-07-15 22:34:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:39 - shap - INFO - phi = array([-0.09863095, -0.10809524, -0.0075    ,  0.01636905,  0.00684524,
       -0.18613095,  0.0027381 , -0.00059524])
2025-07-15 22:34:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:39 - shap - INFO - phi = array([ 0.21505952,  0.3222619 ,  0.02458333,  0.0510119 ,  0.0389881 ,
       -0.02232143, -0.0264881 ,  0.02190476])
2025-07-15 22:34:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:40 - shap - INFO - phi = array([ 0.10294643,  0.13842262,  0.01282738,  0.03122024,  0.045625  ,
        0.30794643, -0.018125  ,  0.0041369 ])
2025-07-15 22:34:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:40 - shap - INFO - phi = array([ 0.36752976,  0.28008929, -0.00592262,  0.03026786,  0.0166369 ,
       -0.06824405, -0.00991071,  0.01455357])
2025-07-15 22:34:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:40 - shap - INFO - phi = array([-0.18916667,  0.01369048, -0.01654762, -0.04464286, -0.04589286,
       -0.07904762,  0.00107143, -0.01446429])
2025-07-15 22:34:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:41 - shap - INFO - phi = array([-0.11395833, -0.11705357, -0.00080357,  0.01758929,  0.01306548,
       -0.15479167, -0.01383929, -0.00520833])
2025-07-15 22:34:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:41 - shap - INFO - phi = array([-0.11642857, -0.1439881 ,  0.00559524,  0.00470238, -0.01565476,
       -0.09755952, -0.01678571,  0.00511905])
2025-07-15 22:34:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:41 - shap - INFO - phi = array([-0.25922619,  0.03232143, -0.01345238, -0.01095238, -0.0360119 ,
       -0.11380952,  0.00988095,  0.01625   ])
2025-07-15 22:34:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:42 - shap - INFO - phi = array([ 0.20068452,  0.22294643,  0.01050595, -0.00883929,  0.08473214,
        0.06044643,  0.08181548, -0.02729167])
2025-07-15 22:34:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:42 - shap - INFO - phi = array([ 0.02196429,  0.0272619 , -0.04208333, -0.24547619, -0.15142857,
        0.1289881 , -0.04047619, -0.07375   ])
2025-07-15 22:34:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:43 - shap - INFO - phi = array([ 0.00880952, -0.13386905, -0.01589286, -0.11422619, -0.08767857,
       -0.01928571,  0.01005952, -0.02291667])
2025-07-15 22:34:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:43 - shap - INFO - phi = array([-0.08857143, -0.13577381, -0.00678571, -0.05916667, -0.06005952,
       -0.01791667, -0.00446429, -0.0022619 ])
2025-07-15 22:34:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:43 - shap - INFO - phi = array([-0.11613095, -0.12279762, -0.00255952,  0.0210119 ,  0.00642857,
       -0.14178571, -0.01184524, -0.00732143])
2025-07-15 22:34:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:44 - shap - INFO - phi = array([ 0.11785714,  0.10255952,  0.00988095,  0.03238095,  0.03386905,
        0.33761905, -0.01815476,  0.0089881 ])
2025-07-15 22:34:44 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:44 - shap - INFO - phi = array([-0.09419643, -0.10443452, -0.00866071, -0.04794643, -0.04604167,
       -0.04598214, -0.00068452, -0.02705357])
2025-07-15 22:34:44 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:45 - shap - INFO - phi = array([ 0.19380952,  0.15869048, -0.01279762,  0.03547619,  0.03821429,
        0.22      , -0.01291667,  0.00452381])
2025-07-15 22:34:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:45 - shap - INFO - phi = array([-0.14041667, -0.09791667,  0.0135119 ,  0.01327381, -0.00678571,
       -0.1435119 , -0.01208333, -0.00107143])
2025-07-15 22:34:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:45 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:45 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:45 - shap - INFO - phi = array([-0.05041667,  0.06666667, -0.00392857, -0.00345238,  0.04958333,
        0.5702381 , -0.01821429,  0.01452381])
2025-07-15 22:34:45 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:46 - shap - INFO - phi = array([ 0.40395833,  0.28973214, -0.009375  ,  0.03455357,  0.02580357,
       -0.12401786, -0.01139881,  0.01574405])
2025-07-15 22:34:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:46 - shap - INFO - phi = array([-0.10568452, -0.1158631 , -0.00735119,  0.02193452,  0.00675595,
       -0.16395833, -0.00491071, -0.00592262])
2025-07-15 22:34:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:46 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:46 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:46 - shap - INFO - phi = array([ 0.23633929,  0.37342262,  0.00514881,  0.04633929,  0.06169643,
       -0.10270833, -0.0158631 ,  0.020625  ])
2025-07-15 22:34:46 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:47 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:47 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:47 - shap - INFO - phi = array([ 0.01044643,  0.02973214, -0.02776786, -0.27818452, -0.06002976,
        0.03383929,  0.00383929, -0.086875  ])
2025-07-15 22:34:47 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:47 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:47 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:47 - shap - INFO - phi = array([ 0.02970238, -0.26440476, -0.0085119 ,  0.00440476, -0.0347619 ,
       -0.11154762,  0.00958333,  0.00053571])
2025-07-15 22:34:47 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:48 - shap - INFO - phi = array([ 0.11785714,  0.0160119 , -0.02934524,  0.01059524,  0.02      ,
       -0.48696429,  0.0027381 , -0.02589286])
2025-07-15 22:34:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:48 - shap - INFO - phi = array([-0.05675595, -0.07705357, -0.01425595,  0.01395833,  0.00508929,
       -0.22622024, -0.0083631 , -0.01139881])
2025-07-15 22:34:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:48 - shap - INFO - phi = array([ 0.13666667, -0.29345238, -0.00702381,  0.0172619 ,  0.00625   ,
       -0.20261905, -0.00261905, -0.02946429])
2025-07-15 22:34:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:49 - shap - INFO - phi = array([-0.09895833, -0.07401786, -0.00854167, -0.05449405, -0.05110119,
       -0.05127976, -0.00300595, -0.03360119])
2025-07-15 22:34:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:49 - shap - INFO - phi = array([-0.07291667,  0.06672619, -0.00845238,  0.01285714,  0.05071429,
        0.5925    , -0.0272619 ,  0.01083333])
2025-07-15 22:34:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:49 - shap - INFO - phi = array([-0.12410714, -0.10785714,  0.01029762,  0.01339286, -0.00386905,
       -0.16785714,  0.00714286, -0.00214286])
2025-07-15 22:34:55 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_hard
2025-07-15 22:34:55 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:34:55 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:34:55 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:34:55 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:34:55 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:34:55 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 22:34:55 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 22:34:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([ 0.10655816,  0.1385767 , -0.01430297, -0.02427109,  0.0003371 ,
        0.11901677,  0.01376656,  0.00372461])
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([-0.10655816, -0.1385767 ,  0.01430297,  0.02427109, -0.0003371 ,
       -0.11901677, -0.01376656, -0.00372461])
2025-07-15 22:34:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([ 0.12585436, -0.07026963, -0.03970729, -0.08601488,  0.0294808 ,
        0.01937712, -0.03548646, -0.03261465])
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([-0.12585436,  0.07026963,  0.03970729,  0.08601488, -0.0294808 ,
       -0.01937712,  0.03548646,  0.03261465])
2025-07-15 22:34:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([-0.08944492, -0.08901438, -0.01293833, -0.02770324, -0.04294857,
       -0.29942513,  0.00108786, -0.01681947])
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([ 0.08944492,  0.08901438,  0.01293833,  0.02770324,  0.04294857,
        0.29942513, -0.00108786,  0.01681947])
2025-07-15 22:34:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([-0.16770715, -0.02110286,  0.01312736, -0.03723211, -0.04613879,
       -0.29753468,  0.00661847, -0.0197315 ])
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([ 0.16770715,  0.02110286, -0.01312736,  0.03723211,  0.04613879,
        0.29753468, -0.00661847,  0.0197315 ])
2025-07-15 22:34:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([ 0.12454941,  0.16075182, -0.01298557, -0.02744294, -0.05397908,
       -0.00507284,  0.08063321,  0.04091462])
2025-07-15 22:34:55 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:55 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:55 - shap - INFO - phi = array([-0.12454941, -0.16075182,  0.01298557,  0.02744294,  0.05397908,
        0.00507284, -0.08063321, -0.04091462])
2025-07-15 22:34:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([ 0.07309082,  0.12060945,  0.00511314,  0.06967789,  0.05798335,
        0.00856725, -0.00561805,  0.0248483 ])
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([-0.07309082, -0.12060945, -0.00511314, -0.06967789, -0.05798335,
       -0.00856725,  0.00561805, -0.0248483 ])
2025-07-15 22:34:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([ 0.16695054, -0.0336381 , -0.00178633, -0.03485225,  0.00195661,
        0.24711528, -0.01032348, -0.01174859])
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([-0.16695054,  0.0336381 ,  0.00178633,  0.03485225, -0.00195661,
       -0.24711528,  0.01032348,  0.01174859])
2025-07-15 22:34:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([ 0.16722871, -0.04544942, -0.01108736, -0.04473574, -0.0514804 ,
        0.10048382,  0.04230212,  0.01140817])
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([-0.16722871,  0.04544942,  0.01108736,  0.04473574,  0.0514804 ,
       -0.10048382, -0.04230212, -0.01140817])
2025-07-15 22:34:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([-0.19559513, -0.25419534,  0.01365808, -0.04405074, -0.04761878,
       -0.00958368,  0.00452362, -0.02043047])
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([ 0.19559513,  0.25419534, -0.01365808,  0.04405074,  0.04761878,
        0.00958368, -0.00452362,  0.02043047])
2025-07-15 22:34:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([ 0.06685729,  0.20076691, -0.00417941, -0.03042248,  0.04172865,
       -0.37712807, -0.03818367, -0.03293092])
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([-0.06685729, -0.20076691,  0.00417941,  0.03042248, -0.04172865,
        0.37712807,  0.03818367,  0.03293092])
2025-07-15 22:34:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([ 0.05978038,  0.17117898,  0.00583068, -0.02854667, -0.00474558,
        0.15838431,  0.00352722, -0.01196678])
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([-0.05978038, -0.17117898, -0.00583068,  0.02854667,  0.00474558,
       -0.15838431, -0.00352722,  0.01196678])
2025-07-15 22:34:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([ 8.58937448e-02,  1.32433762e-01,  8.82644830e-05, -2.77585704e-02,
       -3.78780770e-03,  1.83736245e-01, -5.83741882e-03, -9.37900225e-03])
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([-8.58937448e-02, -1.32433762e-01, -8.82644830e-05,  2.77585704e-02,
        3.78780770e-03, -1.83736245e-01,  5.83741882e-03,  9.37900225e-03])
2025-07-15 22:34:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([-1.27253024e-01, -3.00667015e-01, -2.80074233e-02, -4.56849124e-02,
       -5.81310036e-02, -1.35180475e-04,  3.04241814e-02, -2.94072603e-02])
2025-07-15 22:34:56 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:56 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:56 - shap - INFO - phi = array([ 1.27253024e-01,  3.00667015e-01,  2.80074233e-02,  4.56849124e-02,
        5.81310036e-02,  1.35180475e-04, -3.04241814e-02,  2.94072603e-02])
2025-07-15 22:34:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([-0.08209596, -0.1537581 , -0.01496169, -0.0310011 , -0.0517587 ,
       -0.24554912,  0.01458027, -0.01255899])
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([ 0.08209596,  0.1537581 ,  0.01496169,  0.0310011 ,  0.0517587 ,
        0.24554912, -0.01458027,  0.01255899])
2025-07-15 22:34:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([-2.02517252e-01, -2.30114738e-01,  1.85257656e-04, -4.45203721e-02,
       -2.86003421e-02,  6.72030731e-02,  9.68106864e-03, -2.20027039e-02])
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([ 2.02517252e-01,  2.30114738e-01, -1.85257656e-04,  4.45203721e-02,
        2.86003421e-02, -6.72030731e-02, -9.68106864e-03,  2.20027039e-02])
2025-07-15 22:34:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([ 0.14287416, -0.0321101 ,  0.01180362,  0.08271726,  0.08634844,
        0.03136883, -0.00876253,  0.01749004])
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([-0.14287416,  0.0321101 , -0.01180362, -0.08271726, -0.08634844,
       -0.03136883,  0.00876253, -0.01749004])
2025-07-15 22:34:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([ 0.0982177 ,  0.15296708, -0.00867688, -0.02306988, -0.0163402 ,
        0.13939297,  0.00927413,  0.0009056 ])
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([-0.0982177 , -0.15296708,  0.00867688,  0.02306988,  0.0163402 ,
       -0.13939297, -0.00927413, -0.0009056 ])
2025-07-15 22:34:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([ 0.09560954,  0.19385024, -0.0137921 , -0.01105284,  0.0184822 ,
        0.07921713,  0.00385119, -0.01099951])
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([-0.09560954, -0.19385024,  0.0137921 ,  0.01105284, -0.0184822 ,
       -0.07921713, -0.00385119,  0.01099951])
2025-07-15 22:34:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([ 0.1706597 , -0.073709  ,  0.00774034,  0.04464726,  0.09166433,
        0.04036858, -0.02180732, -0.0277912 ])
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([-0.1706597 ,  0.073709  , -0.00774034, -0.04464726, -0.09166433,
       -0.04036858,  0.02180732,  0.0277912 ])
2025-07-15 22:34:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([-0.08904843, -0.18822383,  0.02759506,  0.06849835, -0.0315081 ,
       -0.01363841, -0.01828863,  0.05860521])
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([ 0.08904843,  0.18822383, -0.02759506, -0.06849835,  0.0315081 ,
        0.01363841,  0.01828863, -0.05860521])
2025-07-15 22:34:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([-0.09947528, -0.11117028,  0.02271731,  0.15064583,  0.05022434,
       -0.17285216, -0.02316333,  0.0154219 ])
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([ 0.09947528,  0.11117028, -0.02271731, -0.15064583, -0.05022434,
        0.17285216,  0.02316333, -0.0154219 ])
2025-07-15 22:34:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([-0.01803507,  0.11436722,  0.01828899,  0.13390728,  0.08589824,
       -0.01497156, -0.01839071,  0.01584488])
2025-07-15 22:34:57 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:57 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:57 - shap - INFO - phi = array([ 0.01803507, -0.11436722, -0.01828899, -0.13390728, -0.08589824,
        0.01497156,  0.01839071, -0.01584488])
2025-07-15 22:34:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([ 0.08211415,  0.1432682 ,  0.00411622,  0.0633836 ,  0.06153391,
        0.01327843, -0.00420766, -0.00888435])
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([-0.08211415, -0.1432682 , -0.00411622, -0.0633836 , -0.06153391,
       -0.01327843,  0.00420766,  0.00888435])
2025-07-15 22:34:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([ 0.09225971,  0.1720475 , -0.00632947, -0.02565367,  0.00029223,
        0.11175789,  0.00825247,  0.00181978])
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([-0.09225971, -0.1720475 ,  0.00632947,  0.02565367, -0.00029223,
       -0.11175789, -0.00825247, -0.00181978])
2025-07-15 22:34:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([-0.10525655, -0.10850082, -0.01160852, -0.03281105, -0.05194494,
       -0.27326384,  0.01512981, -0.0092782 ])
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([ 0.10525655,  0.10850082,  0.01160852,  0.03281105,  0.05194494,
        0.27326384, -0.01512981,  0.0092782 ])
2025-07-15 22:34:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([ 0.07641273,  0.11379532,  0.00448015,  0.05807614,  0.06067705,
        0.02814499, -0.00482894,  0.01830938])
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([-0.07641273, -0.11379532, -0.00448015, -0.05807614, -0.06067705,
       -0.02814499,  0.00482894, -0.01830938])
2025-07-15 22:34:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([-0.16324711, -0.15613668,  0.01803944, -0.04258523, -0.05961462,
       -0.15392129,  0.00198434, -0.01528339])
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([ 0.16324711,  0.15613668, -0.01803944,  0.04258523,  0.05961462,
        0.15392129, -0.00198434,  0.01528339])
2025-07-15 22:34:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([ 0.1171948 ,  0.13939682, -0.01704399, -0.01279853,  0.02725771,
        0.10425373,  0.00093972, -0.00938957])
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([-0.1171948 , -0.13939682,  0.01704399,  0.01279853, -0.02725771,
       -0.10425373, -0.00093972,  0.00938957])
2025-07-15 22:34:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([ 0.04409233, -0.09849516,  0.00103982, -0.00128693, -0.05621816,
       -0.43899038,  0.00440521, -0.01825618])
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([-0.04409233,  0.09849516, -0.00103982,  0.00128693,  0.05621816,
        0.43899038, -0.00440521,  0.01825618])
2025-07-15 22:34:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([-0.30569882, -0.2404696 ,  0.01527144, -0.04630753, -0.03595695,
        0.15135769,  0.00455714, -0.03078134])
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([ 0.30569882,  0.2404696 , -0.01527144,  0.04630753,  0.03595695,
       -0.15135769, -0.00455714,  0.03078134])
2025-07-15 22:34:58 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([ 0.08788589,  0.14254874, -0.00492252, -0.02525816,  0.00073057,
        0.16607853, -0.00584652, -0.00480872])
2025-07-15 22:34:58 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:58 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:58 - shap - INFO - phi = array([-0.08788589, -0.14254874,  0.00492252,  0.02525816, -0.00073057,
       -0.16607853,  0.00584652,  0.00480872])
2025-07-15 22:34:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([-0.14966782, -0.30807632, -0.01987451, -0.05355003, -0.05261168,
        0.10501959,  0.00205374, -0.02536405])
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([ 0.14966782,  0.30807632,  0.01987451,  0.05355003,  0.05261168,
       -0.10501959, -0.00205374,  0.02536405])
2025-07-15 22:34:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([-0.03484543, -0.05272634,  0.03238622,  0.20889782,  0.06212295,
       -0.04237614, -0.01016148,  0.10210638])
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([ 0.03484543,  0.05272634, -0.03238622, -0.20889782, -0.06212295,
        0.04237614,  0.01016148, -0.10210638])
2025-07-15 22:34:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([-0.02375562,  0.25458047,  0.00923224, -0.0051106 ,  0.04482899,
        0.07240974, -0.01267824, -0.01525389])
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([ 0.02375562, -0.25458047, -0.00923224,  0.0051106 , -0.04482899,
       -0.07240974,  0.01267824,  0.01525389])
2025-07-15 22:34:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([-0.23152568, -0.1346432 , -0.00214419, -0.04154517, -0.04118622,
        0.25280782, -0.01600894,  0.02879662])
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([ 0.23152568,  0.1346432 ,  0.00214419,  0.04154517,  0.04118622,
       -0.25280782,  0.01600894, -0.02879662])
2025-07-15 22:34:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([ 0.06996615,  0.08496004,  0.00834603, -0.03065232, -0.00564998,
        0.22297481, -0.00496345,  0.00656106])
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([-0.06996615, -0.08496004, -0.00834603,  0.03065232,  0.00564998,
       -0.22297481,  0.00496345, -0.00656106])
2025-07-15 22:34:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([-0.22535057,  0.19222747,  0.01107708, -0.02841924, -0.00387509,
        0.1557713 , -0.0024226 ,  0.03350207])
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([ 0.22535057, -0.19222747, -0.01107708,  0.02841924,  0.00387509,
       -0.1557713 ,  0.0024226 , -0.03350207])
2025-07-15 22:34:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([ 0.084313  ,  0.07175352,  0.00527766,  0.06803328,  0.06863349,
        0.03130103, -0.00481592,  0.02880754])
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([-0.084313  , -0.07175352, -0.00527766, -0.06803328, -0.06863349,
       -0.03130103,  0.00481592, -0.02880754])
2025-07-15 22:34:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([ 0.05708978, -0.10702998,  0.0035977 , -0.01075141, -0.05906875,
       -0.4288608 ,  0.00797379, -0.01419516])
2025-07-15 22:34:59 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:34:59 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:34:59 - shap - INFO - phi = array([-0.05708978,  0.10702998, -0.0035977 ,  0.01075141,  0.05906875,
        0.4288608 , -0.00797379,  0.01419516])
2025-07-15 22:34:59 - shap - INFO - num_full_subsets = 4
2025-07-15 22:35:00 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:35:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:35:00 - shap - INFO - phi = array([ 0.11506678,  0.10942064, -0.01466355, -0.02339503,  0.01298279,
        0.16391636, -0.01377078,  0.00080972])
2025-07-15 22:35:00 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:35:00 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:35:00 - shap - INFO - phi = array([-0.11506678, -0.10942064,  0.01466355,  0.02339503, -0.01298279,
       -0.16391636,  0.01377078, -0.00080972])
2025-07-15 22:35:05 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-15 22:35:05 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:35:05 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:35:05 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:35:05 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -   stacking SHAP分析完成
