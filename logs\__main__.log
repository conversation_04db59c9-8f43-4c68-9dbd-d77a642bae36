2025-07-15 19:58:00 - __main__ - INFO - 🚀 开始集成学习功能测试
2025-07-15 19:58:00 - __main__ - INFO - ==================================================
2025-07-15 19:58:00 - __main__ - INFO - 测试EnsembleClassifier类
2025-07-15 19:58:00 - __main__ - INFO - ==================================================
2025-07-15 19:58:00 - __main__ - INFO - 创建测试数据...
2025-07-15 19:58:00 - __main__ - INFO - 训练集大小: (700, 20)
2025-07-15 19:58:00 - __main__ - INFO - 测试集大小: (300, 20)
2025-07-15 19:58:00 - __main__ - INFO - 类别分布 - 训练集: [350 350]
2025-07-15 19:58:00 - __main__ - INFO - 类别分布 - 测试集: [150 150]
2025-07-15 19:58:01 - __main__ - INFO - 成功创建 3 个基础模型
2025-07-15 19:58:01 - __main__ - INFO - 测试集成方法: voting
2025-07-15 19:58:01 - __main__ - INFO -   voting 准确率: 0.9733
2025-07-15 19:58:01 - __main__ - INFO - 测试集成方法: bagging
2025-07-15 19:58:01 - __main__ - ERROR -   bagging 测试失败: BaggingClassifier.__init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 19:58:01 - __main__ - INFO - 测试集成方法: boosting
2025-07-15 19:58:01 - __main__ - ERROR -   boosting 测试失败: AdaBoostClassifier.__init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 19:58:01 - __main__ - INFO - 测试集成方法: stacking
2025-07-15 19:58:03 - __main__ - INFO -   stacking 准确率: 0.9733
2025-07-15 19:58:03 - __main__ - INFO - ==================================================
2025-07-15 19:58:03 - __main__ - INFO - 测试集成学习管道
2025-07-15 19:58:03 - __main__ - INFO - ==================================================
2025-07-15 19:58:03 - __main__ - INFO - 创建测试数据...
2025-07-15 19:58:03 - __main__ - INFO - 训练集大小: (700, 20)
2025-07-15 19:58:03 - __main__ - INFO - 测试集大小: (300, 20)
2025-07-15 19:58:03 - __main__ - INFO - 类别分布 - 训练集: [350 350]
2025-07-15 19:58:03 - __main__ - INFO - 类别分布 - 测试集: [150 150]
2025-07-15 19:58:07 - __main__ - INFO - 集成学习管道测试成功
2025-07-15 19:58:07 - __main__ - INFO - 生成了 2 个集成模型
2025-07-15 19:58:07 - __main__ - INFO -   voting_soft: 准确率=0.9733, F1=0.9733
2025-07-15 19:58:07 - __main__ - INFO -   stacking: 准确率=0.9733, F1=0.9733
2025-07-15 19:58:07 - __main__ - INFO - ==================================================
2025-07-15 19:58:07 - __main__ - INFO - 测试可视化功能
2025-07-15 19:58:07 - __main__ - INFO - ==================================================
2025-07-15 19:58:07 - __main__ - INFO - 创建测试数据...
2025-07-15 19:58:07 - __main__ - INFO - 训练集大小: (700, 20)
2025-07-15 19:58:07 - __main__ - INFO - 测试集大小: (300, 20)
2025-07-15 19:58:07 - __main__ - INFO - 类别分布 - 训练集: [350 350]
2025-07-15 19:58:07 - __main__ - INFO - 类别分布 - 测试集: [150 150]
2025-07-15 19:58:15 - __main__ - INFO - 可视化功能测试成功
2025-07-15 19:58:15 - __main__ - INFO - ==================================================
2025-07-15 19:58:15 - __main__ - INFO - 测试结果总结
2025-07-15 19:58:15 - __main__ - INFO - ==================================================
2025-07-15 19:58:15 - __main__ - INFO - EnsembleClassifier类: ✅ 通过
2025-07-15 19:58:15 - __main__ - INFO - 集成学习管道: ✅ 通过
2025-07-15 19:58:15 - __main__ - INFO - 可视化功能: ✅ 通过
2025-07-15 19:58:15 - __main__ - INFO - 
总体结果: 3/3 测试通过
2025-07-15 19:58:15 - __main__ - INFO - 🎉 所有测试通过！集成学习功能正常工作
2025-07-15 20:06:12 - __main__ - INFO - 🚀 集成学习功能完整使用示例
2025-07-15 20:06:12 - __main__ - INFO - 
开始执行: 基础集成学习
2025-07-15 20:06:12 - __main__ - INFO - ============================================================
2025-07-15 20:06:12 - __main__ - INFO - 示例1: 基础集成学习
2025-07-15 20:06:12 - __main__ - INFO - ============================================================
2025-07-15 20:06:12 - __main__ - INFO - 创建示例数据...
2025-07-15 20:06:12 - __main__ - INFO - 示例数据已保存到: sample_data.csv
2025-07-15 20:06:12 - __main__ - INFO - 数据形状: (1000, 21)
2025-07-15 20:06:12 - __main__ - INFO - 类别分布: [500 500]
2025-07-15 20:06:15 - __main__ - ERROR - ❌ 基础集成学习 执行出错: ' font-family'
2025-07-15 20:06:15 - __main__ - INFO - 
开始执行: 自定义集成分类器
2025-07-15 20:06:15 - __main__ - INFO - ============================================================
2025-07-15 20:06:15 - __main__ - INFO - 示例2: 自定义集成分类器
2025-07-15 20:06:15 - __main__ - INFO - ============================================================
2025-07-15 20:06:15 - __main__ - INFO - 创建示例数据...
2025-07-15 20:06:15 - __main__ - INFO - 示例数据已保存到: sample_data.csv
2025-07-15 20:06:15 - __main__ - INFO - 数据形状: (1000, 21)
2025-07-15 20:06:15 - __main__ - INFO - 类别分布: [500 500]
2025-07-15 20:06:15 - __main__ - INFO - 测试集成方法: voting
2025-07-15 20:06:16 - __main__ - INFO -   voting 准确率: 0.9700
2025-07-15 20:06:16 - __main__ - INFO - 测试集成方法: bagging
2025-07-15 20:06:16 - __main__ - ERROR -   bagging 失败: BaggingClassifier.__init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 20:06:16 - __main__ - INFO - 测试集成方法: stacking
2025-07-15 20:06:17 - __main__ - INFO -   stacking 准确率: 0.9700
2025-07-15 20:06:17 - __main__ - INFO - ✅ 自定义集成分类器 执行成功
2025-07-15 20:06:17 - __main__ - INFO - 
开始执行: 完整分析管道
2025-07-15 20:06:17 - __main__ - INFO - ============================================================
2025-07-15 20:06:17 - __main__ - INFO - 示例3: 完整的二分类分析管道
2025-07-15 20:06:17 - __main__ - INFO - ============================================================
2025-07-15 20:06:17 - __main__ - INFO - 创建示例数据...
2025-07-15 20:06:17 - __main__ - INFO - 示例数据已保存到: sample_data.csv
2025-07-15 20:06:17 - __main__ - INFO - 数据形状: (1000, 21)
2025-07-15 20:06:17 - __main__ - INFO - 类别分布: [500 500]
2025-07-15 20:06:21 - __main__ - ERROR - 完整分析管道执行失败
2025-07-15 20:06:21 - __main__ - ERROR - ❌ 完整分析管道 执行失败
2025-07-15 20:06:21 - __main__ - INFO - 
开始执行: 命令行使用方式
2025-07-15 20:06:21 - __main__ - INFO - ============================================================
2025-07-15 20:06:21 - __main__ - INFO - 示例4: 命令行使用方式
2025-07-15 20:06:21 - __main__ - INFO - ============================================================
2025-07-15 20:06:21 - __main__ - INFO - 创建示例数据...
2025-07-15 20:06:21 - __main__ - INFO - 示例数据已保存到: sample_data.csv
2025-07-15 20:06:21 - __main__ - INFO - 数据形状: (1000, 21)
2025-07-15 20:06:21 - __main__ - INFO - 类别分布: [500 500]
2025-07-15 20:06:21 - __main__ - INFO - 命令行使用示例:
2025-07-15 20:06:21 - __main__ - INFO - 1. 单数据源集成学习:
2025-07-15 20:06:21 - __main__ - INFO -    python code/main.py --mode ensemble --model "RandomForest,XGBoost,LightGBM" --data sample_data.csv
2025-07-15 20:06:21 - __main__ - INFO - 
2. 多数据源集成学习:
2025-07-15 20:06:21 - __main__ - INFO -    python code/main.py --mode multi_data_ensemble --model_data_config config.json
2025-07-15 20:06:21 - __main__ - INFO - 
3. 完整二分类分析（包含集成学习）:
2025-07-15 20:06:21 - __main__ - INFO -    python code/binary_classification_pipeline.py --data sample_data.csv --models "RandomForest,XGBoost" --enable_ensemble
2025-07-15 20:06:21 - __main__ - INFO - 
4. GUI界面使用:
2025-07-15 20:06:21 - __main__ - INFO -    python gui_main.py
2025-07-15 20:06:21 - __main__ - INFO -    然后在"集成学习"选项卡中进行操作
2025-07-15 20:06:21 - __main__ - INFO - ✅ 命令行使用方式 执行成功
2025-07-15 20:06:21 - __main__ - INFO - ============================================================
2025-07-15 20:06:21 - __main__ - INFO - 示例执行结果总结
2025-07-15 20:06:21 - __main__ - INFO - ============================================================
2025-07-15 20:06:21 - __main__ - INFO - 基础集成学习: ❌ 失败
2025-07-15 20:06:21 - __main__ - INFO - 自定义集成分类器: ✅ 成功
2025-07-15 20:06:21 - __main__ - INFO - 完整分析管道: ❌ 失败
2025-07-15 20:06:21 - __main__ - INFO - 命令行使用方式: ✅ 成功
2025-07-15 20:06:21 - __main__ - INFO - 
总体结果: 2/4 个示例成功
2025-07-15 20:06:21 - __main__ - WARNING - ⚠️ 部分示例执行失败，请检查相关功能
2025-07-15 20:06:21 - __main__ - INFO - 临时文件已清理
