2025-07-16 00:40:47 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:40:48 - GUI - INFO - GUI界面初始化完成
2025-07-16 00:41:06 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 9)
2025-07-16 00:41:06 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-16 00:41:06 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 221, 'max_depth': 12, 'min_samples_split': 15, 'min_samples_leaf': 5, 'max_features': 'sqrt'}
2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9725
2025-07-16 00:41:20 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250716_004120.html
2025-07-16 00:41:20 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250716_004120.html
2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.50 秒
2025-07-16 00:41:24 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 9)
2025-07-16 00:41:24 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-16 00:41:24 - model_training - INFO - 模型名称: Random Forest
2025-07-16 00:41:24 - model_training - INFO - 准确率: 0.8250
2025-07-16 00:41:24 - model_training - INFO - AUC: 0.9015
2025-07-16 00:41:24 - model_training - INFO - 混淆矩阵:
2025-07-16 00:41:24 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-16 00:41:24 - model_training - INFO - 
分类报告:
2025-07-16 00:41:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 00:41:24 - model_training - INFO - 训练时间: 0.08 秒
2025-07-16 00:41:24 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 00:41:24 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
