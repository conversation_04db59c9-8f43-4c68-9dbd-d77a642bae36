#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多数据源集成学习可视化工具

此脚本用于绘制多数据源集成学习模型的可视化图表，包括：
1. 性能比较图
2. ROC曲线比较
3. 混淆矩阵
4. 性能指标雷达图
5. HTML报告生成
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from joblib import load
from datetime import datetime
from sklearn.metrics import (
    confusion_matrix, roc_curve, auc
)

# 动态添加当前目录到 Python 路径，确保模块可以被找到
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入项目模块
try:
    from config import OUTPUT_PATH, ENSEMBLE_PATH, MULTI_DATA_CACHE_PATH
    from plot_utils import setup_matplotlib_for_chinese, plot_roc_comparison, save_plot
except ImportError:
    # 如果导入失败，使用基本配置
    from pathlib import Path
    OUTPUT_PATH = Path("../output")
    ENSEMBLE_PATH = Path("../ensemble")
    MULTI_DATA_CACHE_PATH = Path("../multi_data_cache")

# 初始化matplotlib设置
setup_matplotlib_for_chinese()

# 配置字典
CONFIG = {
    'output_path': OUTPUT_PATH,
    'ensemble_path': MULTI_DATA_CACHE_PATH,
    'dpi': 150,
    'figsize': (10, 8)
}

# 配置matplotlib
import matplotlib as mpl
# 添加更多中文字体支持，特别是微软雅黑和宋体（Windows系统常用）
mpl.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
# 修复负号显示问题
mpl.rcParams['axes.unicode_minus'] = False
# 设置绘图风格
plt.style.use('seaborn-v0_8-whitegrid')

# 使用统一字体管理器
from font_manager import initialize_fonts, get_safe_title, get_safe_label

# 初始化字体
initialize_fonts()

# 字体属性已由font_manager统一管理

def load_ensemble_models(ensemble_path=None):
    """
    加载已训练的多数据源集成模型
    
    Args:
        ensemble_path: 集成模型保存路径
        
    Returns:
        list: 包含模型信息的字典列表
    """
    if ensemble_path is None:
        ensemble_path = CONFIG['ensemble_path']
    else:
        ensemble_path = Path(ensemble_path)
    
    print(f"正在从 {ensemble_path} 加载集成模型...")
    
    results = []
    
    # 直接在 ensemble_path 中查找 joblib 文件
    for model_file in ensemble_path.glob('*_multi_data_results.joblib'):
        try:
            # 加载包含元组列表的结果
            ensemble_results_list = load(model_file)
            
            # 检查加载的是否为列表
            if isinstance(ensemble_results_list, list):
                # 每个 res 是一个元组: (strategy, method, ensemble_model, ...)
                for res in ensemble_results_list:
                    if isinstance(res, tuple) and len(res) >= 3:
                        results.append({
                            'strategy': res[0],
                            'method': res[1],
                            'ensemble': res[2], # 模型在第三个位置
                            'status': 'success',
                            'path': model_file
                        })
                print(f"成功加载并解析文件: {model_file.name}")
            else:
                print(f"警告: 文件 {model_file.name} 的内容不是预期的列表格式。")

        except Exception as e:
            print(f"加载或解析模型 {model_file.name} 失败: {e}")
    
    print(f"共加载了 {len(results)} 个集成模型")
    return results

def evaluate_ensemble_models(results):
    """
    评估多数据源集成模型性能
    
    Args:
        results: 包含模型信息的元组列表
        
    Returns:
        pd.DataFrame: 包含评估结果的数据框
    """
    print("评估集成模型性能...")
    
    # 准备数据
    model_names = []
    strategies = []
    methods = []
    accuracies = []
    precisions = []
    recalls = []
    f1_scores = []
    aucs = []
    
    for result in results:
        # result is a tuple: (strategy, method, ensemble_model, metrics)
        if isinstance(result, tuple) and len(result) >= 4 and isinstance(result[3], dict):
            strategy = result[0]
            method = result[1]
            metrics = result[3]
            
            model_names.append(f"{strategy}_{method}")
            strategies.append(strategy)
            methods.append(method)
            accuracies.append(metrics.get('accuracy', 0))
            precisions.append(metrics.get('precision', 0))
            recalls.append(metrics.get('recall', 0))
            f1_scores.append(metrics.get('f1', 0))
            aucs.append(metrics.get('roc_auc', 0))
            
            print(f"已解析评估结果: {strategy}_{method}")
            
        else:
            print(f"警告: 跳过一个格式不正确的评估结果。结果: {result}")
    
    if not model_names:
        print("无法从任何模型中解析评估结果")
        return None
    
    # 创建DataFrame
    df = pd.DataFrame({
        'model_name': model_names,
        'strategy': strategies,
        'method': methods,
        'accuracy': accuracies,
        'precision': precisions,
        'recall': recalls,
        'f1_score': f1_scores,
        'auc': aucs
    })
    
    return df

def plot_performance_comparison(df, save_path):
    """
    绘制性能比较图
    
    Args:
        df: 包含评估结果的数据框
        save_path: 保存路径
    """
    print("绘制性能比较图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    metrics = ['accuracy', 'precision', 'recall', 'f1_score']
    titles = ['准确率', '精确率', '召回率', 'F1分数']
    colors = sns.color_palette('viridis', len(df))
    
    for i, (metric, title) in enumerate(zip(metrics, titles)):
        ax = axes[i//2, i%2]
        bars = ax.bar(df['model_name'], df[metric], color=colors)
        ax.set_title(f'{title} 比较', fontsize=14, fontweight='bold', fontproperties=FONT_PROPERTIES)
        ax.set_ylabel(title, fontproperties=FONT_PROPERTIES)
        ax.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars, df[metric]):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    save_plot(fig, model_name='all', plot_type='multi_ensemble_performance', file_name='performance_comparison.png')

def plot_roc_curves(results, save_path):
    """
    为多个集成模型绘制ROC曲线对比图
    
    Args:
        results: 包含模型信息的字典列表
        save_path: 结果保存路径
    """
    print("绘制ROC曲线对比图...")
    
    roc_results = {}
    for result in results:
        if result['status'] != 'success':
            continue
        
        ensemble = result['ensemble']
        model_name = f"{result['strategy']}_{result['method']}"
        
        try:
            # 假设使用第一个数据集进行评估
            first_dataset = list(ensemble.model_datasets.values())[0]
            X_test = first_dataset['X_test']
            y_test = first_dataset['y_test']
            
            y_pred_proba = ensemble.predict_proba(X_test)[:, 1]
            
            roc_results[model_name] = {
                'y_true': y_test,
                'y_pred_proba': y_pred_proba
            }
        except Exception as e:
            print(f"为模型 {model_name} 生成ROC数据失败: {e}")

    if not roc_results:
        print("没有可用于绘制ROC曲线的数据")
        return

    fig, ax = plot_roc_comparison(roc_results, title='多数据源集成ROC对比')
    save_plot(fig, model_name='all', plot_type='multi_ensemble_roc', file_name='roc_comparison.png')

def plot_confusion_matrices(results, save_path):
    """
    绘制混淆矩阵
    
    Args:
        results: 包含模型信息的字典列表
        save_path: 保存路径
    """
    print("绘制混淆矩阵...")
    
    for result in results:
        if result['status'] != 'success':
            continue
            
        try:
            ensemble = result['ensemble']
            strategy = result['strategy']
            method = result['method']
            
            # 获取第一个数据集用于评估
            first_dataset = list(ensemble.model_datasets.values())[0]
            X_test = first_dataset['X_test']
            y_test = first_dataset['y_test']
            
            y_pred = ensemble.predict(X_test)
            cm = confusion_matrix(y_test, y_pred)
            
            plt.figure(figsize=(8, 6))
            
            # 不使用ConfusionMatrixDisplay，而是直接使用heatmap并手动设置标签
            ax = sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
            
            # 手动设置坐标轴标签，确保使用字体属性
            plt.xlabel('预测标签', fontproperties=FONT_PROPERTIES, fontsize=12)
            plt.ylabel('真实标签', fontproperties=FONT_PROPERTIES, fontsize=12)
            
            # 设置刻度标签
            plt.xticks([0.5, 1.5], ['阴性', '阳性'], fontproperties=FONT_PROPERTIES)
            plt.yticks([0.5, 1.5], ['阴性', '阳性'], fontproperties=FONT_PROPERTIES, rotation=0)
            
            plt.title(f'{strategy}_{method} 混淆矩阵', fontsize=14, fontweight='bold', fontproperties=FONT_PROPERTIES)
            plt.tight_layout()
            
            plt.savefig(save_path / f'confusion_matrix_{strategy}_{method}.png', dpi=CONFIG['dpi'], bbox_inches='tight')
            plt.close()
            print(f"{strategy}_{method} 混淆矩阵已保存至: {save_path / f'confusion_matrix_{strategy}_{method}.png'}")
        except Exception as e:
            print(f"绘制 {strategy}_{method} 的混淆矩阵失败: {e}")

def plot_radar_chart(df, save_path):
    """
    绘制性能指标雷达图
    
    Args:
        df: 包含评估结果的数据框
        save_path: 保存路径
    """
    print("绘制性能指标雷达图...")
    
    # 准备雷达图数据
    categories = ['准确率', '精确率', '召回率', 'F1分数', 'AUC']
    N = len(categories)
    angles = np.linspace(0, 2 * np.pi, N, endpoint=False).tolist()
    angles += angles[:1]  # 闭合雷达图
    
    plt.figure(figsize=(10, 10))
    ax = plt.subplot(111, polar=True)
    
    # 设置雷达图参数
    ax.set_theta_offset(np.pi / 2)  # 从上方开始
    ax.set_theta_direction(-1)  # 顺时针
    ax.set_rlabel_position(0)
    
    # 绘制每个模型的雷达图
    for i, row in df.iterrows():
        values = [row['accuracy'], row['precision'], row['recall'], row['f1_score'], row['auc']]
        values += values[:1]  # 闭合雷达图
        
        ax.plot(angles, values, linewidth=2, linestyle='solid', 
                label=f"{row['strategy']}_{row['method']}")
        ax.fill(angles, values, alpha=0.1)
    
    # 设置刻度和标签
    plt.xticks(angles[:-1], categories, fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_ylim(0, 1)
    
    # 添加图例
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1), prop=FONT_PROPERTIES)
    
    plt.title('多数据源集成模型性能雷达图', fontsize=14, fontweight='bold', fontproperties=FONT_PROPERTIES)
    plt.tight_layout()
    plt.savefig(save_path / 'multi_data_ensemble_radar.png', dpi=CONFIG['dpi'], bbox_inches='tight')
    plt.close()
    print(f"性能指标雷达图已保存至: {save_path / 'multi_data_ensemble_radar.png'}")

def generate_html_report(df, results, save_path):
    """
    生成HTML报告
    
    Args:
        df: 包含评估结果的数据框
        results: 包含模型信息的字典列表
        save_path: 保存路径
    """
    print("生成HTML报告...")
    
    # 按AUC降序排序
    df_sorted = df.sort_values('auc', ascending=False)
    
    # 找出每列的最大值索引
    best_idx = {
        'accuracy': df['accuracy'].idxmax(),
        'precision': df['precision'].idxmax(),
        'recall': df['recall'].idxmax(),
        'f1_score': df['f1_score'].idxmax(),
        'auc': df['auc'].idxmax()
    }
    
    # 创建HTML内容
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>多数据源集成模型性能报告</title>
        <style>
            @font-face {{
                font-family: 'CustomFont';
                src: local('Microsoft YaHei'), local('SimHei'), local('SimSun');
            }}
            body {{
                font-family: 'CustomFont', 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
                margin: 40px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            h1 {{
                color: #2c3e50;
                text-align: center;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
            }}
            h2 {{
                color: #34495e;
                margin-top: 30px;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }}
            th {{
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }}
            tr:nth-child(even) {{
                background-color: #f2f2f2;
            }}
            .best-score {{
                font-weight: bold;
                color: #27ae60;
            }}
            .images {{
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 20px;
                margin: 30px 0;
            }}
            .image-container {{
                max-width: 100%;
                text-align: center;
            }}
            img {{
                max-width: 100%;
                height: auto;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>多数据源集成模型性能报告</h1>
            <p style="text-align: center; color: #7f8c8d;">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <h2>性能指标摘要</h2>
            <table>
                <tr>
                    <th>模型</th>
                    <th>策略</th>
                    <th>方法</th>
                    <th>准确率</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>F1分数</th>
                    <th>AUC</th>
                </tr>
    """
    
    # 添加表格行
    for i, row in df_sorted.iterrows():
        html_content += "<tr>"
        html_content += f"<td>{row['model_name']}</td>"
        html_content += f"<td>{row['strategy']}</td>"
        html_content += f"<td>{row['method']}</td>"
        
        # 添加性能指标，标记最佳值
        metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'auc']
        for metric in metrics:
            class_attr = "best-score" if i == best_idx[metric] else ""
            html_content += f"<td class='{class_attr}'>{row[metric]:.3f}</td>"
        
        html_content += "</tr>"
    
    html_content += """
            </table>
            
            <h2>可视化结果</h2>
            <div class="images">
                <div class="image-container">
                    <img src="multi_data_ensemble_comparison.png" alt="性能比较图">
                    <p>性能比较图</p>
                </div>
                <div class="image-container">
                    <img src="multi_data_ensemble_roc.png" alt="ROC曲线比较">
                    <p>ROC曲线比较</p>
                </div>
                <div class="image-container">
                    <img src="multi_data_ensemble_radar.png" alt="性能雷达图">
                    <p>性能雷达图</p>
                </div>
            </div>
            
            <h2>混淆矩阵</h2>
            <div class="images">
    """
    
    # 添加混淆矩阵图片
    successful_results = [r for r in results if r['status'] == 'success']
    for result in successful_results:
        strategy = result['strategy']
        method = result['method']
        html_content += f"""
                <div class="image-container">
                    <img src="confusion_matrix_{strategy}_{method}.png" alt="{strategy}_{method} 混淆矩阵">
                    <p>{strategy}_{method} 混淆矩阵</p>
                </div>
        """
    
    # 找出最佳模型
    best_model = df_sorted.iloc[0]
    
    html_content += f"""
            </div>
            
            <h2>结论</h2>
            <p>
                基于多数据源集成学习的比较，<strong>{best_model['strategy']}_{best_model['method']}</strong> 
                模型表现最佳，其AUC值为 <strong>{best_model['auc']:.3f}</strong>。
            </p>
            <p>
                该模型在准确率、精确率、召回率和F1分数方面的表现分别为：
                <strong>{best_model['accuracy']:.3f}</strong>、
                <strong>{best_model['precision']:.3f}</strong>、
                <strong>{best_model['recall']:.3f}</strong> 和
                <strong>{best_model['f1_score']:.3f}</strong>。
            </p>
            <p>
                建议在实际应用中使用 <strong>{best_model['strategy']}_{best_model['method']}</strong> 
                模型配置进行多数据源集成学习。
            </p>
        </div>
    </body>
    </html>
    """
    
    # 保存HTML报告
    with open(save_path / 'multi_data_ensemble_report.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML报告已保存至: {save_path / 'multi_data_ensemble_report.html'}")

def visualize_ensemble_models(ensemble_path=None, output_path=None):
    """
    可视化多数据源集成模型
    
    Args:
        ensemble_path: 集成模型保存路径
        output_path: 可视化结果保存路径
    """
    # 设置保存路径
    if output_path is None:
        output_path = CONFIG['ensemble_path'] / 'visualizations'
    else:
        output_path = Path(output_path)
    
    # 确保保存路径存在
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 加载模型
    results = load_ensemble_models(ensemble_path)
    
    if not results:
        print("没有找到可用的集成模型")
        return
    
    # 评估模型
    df = evaluate_ensemble_models(results)
    
    if df is None or df.empty:
        print("无法评估任何模型")
        return
    
    # 绘制图表
    plot_performance_comparison(df, output_path)
    plot_roc_curves(results, output_path)
    plot_confusion_matrices(results, output_path)
    plot_radar_chart(df, output_path)
    
    # 生成HTML报告
    generate_html_report(df, results, output_path)
    
    print("\n🎉 可视化完成!")
    print(f"📁 所有结果已保存至: {output_path}")
    print(f"📄 查看详细报告: {output_path / 'multi_data_ensemble_report.html'}")
    
    # 返回排序后的结果
    df_sorted = df.sort_values('auc', ascending=False)
    
    if not df_sorted.empty:
        best_model = df_sorted.iloc[0]
        print(f"\n🏆 最佳多数据源集成模型: {best_model['strategy']}_{best_model['method']}")
        print(f"📊 性能指标:")
        print(f"   准确率: {best_model['accuracy']:.4f}")
        print(f"   精确率: {best_model['precision']:.4f}")
        print(f"   召回率: {best_model['recall']:.4f}")
        print(f"   F1分数: {best_model['f1_score']:.4f}")
        print(f"   AUC: {best_model['auc']:.4f}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="多数据源集成学习可视化工具")
    parser.add_argument("--ensemble_path", type=str, default=None,
                        help="集成模型保存路径")
    parser.add_argument("--output_path", type=str, default=None,
                        help="可视化结果保存路径")
    
    args = parser.parse_args()
    
    visualize_ensemble_models(args.ensemble_path, args.output_path)

if __name__ == "__main__":
    main()