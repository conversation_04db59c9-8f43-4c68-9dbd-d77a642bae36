2025-07-16 00:41:06 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 221, 'max_depth': 12, 'min_samples_split': 15, 'min_samples_leaf': 5, 'max_features': 'sqrt'}
2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9725
2025-07-16 00:41:20 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250716_004120.html
2025-07-16 00:41:20 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250716_004120.html
2025-07-16 00:41:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.50 秒
