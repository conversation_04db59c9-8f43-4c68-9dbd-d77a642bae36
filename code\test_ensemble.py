#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成学习功能测试脚本
"""

import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from pathlib import Path

from model_ensemble import EnsembleClassifier, run_ensemble_pipeline, create_base_models_from_names
from plot_ensemble import visualize_ensemble_results
from logger import get_logger

logger = get_logger(__name__)

def create_test_data():
    """创建测试数据"""
    logger.info("创建测试数据...")
    
    # 生成二分类数据
    X, y = make_classification(
        n_samples=1000,
        n_features=20,
        n_informative=15,
        n_redundant=5,
        n_clusters_per_class=1,
        random_state=42
    )
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    logger.info(f"训练集大小: {X_train.shape}")
    logger.info(f"测试集大小: {X_test.shape}")
    logger.info(f"类别分布 - 训练集: {np.bincount(y_train)}")
    logger.info(f"类别分布 - 测试集: {np.bincount(y_test)}")
    
    return X_train, X_test, y_train, y_test

def test_ensemble_classifier():
    """测试EnsembleClassifier类"""
    logger.info("=" * 50)
    logger.info("测试EnsembleClassifier类")
    logger.info("=" * 50)
    
    X_train, X_test, y_train, y_test = create_test_data()
    
    # 创建基础模型
    model_names = ['RandomForest', 'XGBoost', 'LightGBM']
    base_models = create_base_models_from_names(model_names, X_train, y_train, X_test, y_test)
    
    if not base_models:
        logger.error("无法创建基础模型")
        return False
    
    logger.info(f"成功创建 {len(base_models)} 个基础模型")
    
    # 测试不同的集成方法
    ensemble_methods = ['voting', 'bagging', 'boosting', 'stacking']
    
    for method in ensemble_methods:
        try:
            logger.info(f"测试集成方法: {method}")
            
            ensemble = EnsembleClassifier(
                base_models=base_models,
                ensemble_method=method,
                random_state=42
            )
            
            # 训练
            ensemble.fit(X_train, y_train)
            
            # 预测
            y_pred = ensemble.predict(X_test)
            accuracy = np.mean(y_pred == y_test)
            
            logger.info(f"  {method} 准确率: {accuracy:.4f}")
            
        except Exception as e:
            logger.error(f"  {method} 测试失败: {e}")
    
    return True

def test_ensemble_pipeline():
    """测试集成学习管道"""
    logger.info("=" * 50)
    logger.info("测试集成学习管道")
    logger.info("=" * 50)
    
    X_train, X_test, y_train, y_test = create_test_data()
    
    # 运行集成学习管道
    model_names = ['RandomForest', 'XGBoost', 'LightGBM']
    
    try:
        ensemble_results = run_ensemble_pipeline(
            X_train=X_train,
            y_train=y_train,
            X_test=X_test,
            y_test=y_test,
            model_names=model_names,
            ensemble_methods=['voting', 'stacking'],
            save_results=False,  # 测试时不保存
            enable_shap=False    # 测试时禁用SHAP以加快速度
        )
        
        if ensemble_results:
            logger.info("集成学习管道测试成功")
            logger.info(f"生成了 {len(ensemble_results)} 个集成模型")
            
            # 显示结果
            for name, result in ensemble_results.items():
                metrics = result['metrics']
                logger.info(f"  {name}: 准确率={metrics['accuracy']:.4f}, F1={metrics['f1_score']:.4f}")
            
            return True
        else:
            logger.error("集成学习管道返回空结果")
            return False
            
    except Exception as e:
        logger.error(f"集成学习管道测试失败: {e}")
        return False

def test_visualization():
    """测试可视化功能"""
    logger.info("=" * 50)
    logger.info("测试可视化功能")
    logger.info("=" * 50)
    
    X_train, X_test, y_train, y_test = create_test_data()
    
    # 运行集成学习
    model_names = ['RandomForest', 'XGBoost']
    
    try:
        ensemble_results = run_ensemble_pipeline(
            X_train=X_train,
            y_train=y_train,
            X_test=X_test,
            y_test=y_test,
            model_names=model_names,
            ensemble_methods=['voting'],
            save_results=False,
            enable_shap=False
        )
        
        if ensemble_results:
            # 测试可视化（不显示图片）
            import matplotlib
            matplotlib.use('Agg')  # 使用非交互式后端
            
            output_dir = Path('test_output')
            output_dir.mkdir(exist_ok=True)
            
            visualize_ensemble_results(
                ensemble_results=ensemble_results,
                X_train=X_train,
                y_train=y_train,
                output_dir=output_dir
            )
            
            logger.info("可视化功能测试成功")
            return True
        else:
            logger.error("无法生成集成结果用于可视化测试")
            return False
            
    except Exception as e:
        logger.error(f"可视化功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始集成学习功能测试")
    
    test_results = []
    
    # 测试1: EnsembleClassifier类
    test_results.append(("EnsembleClassifier类", test_ensemble_classifier()))
    
    # 测试2: 集成学习管道
    test_results.append(("集成学习管道", test_ensemble_pipeline()))
    
    # 测试3: 可视化功能
    test_results.append(("可视化功能", test_visualization()))
    
    # 总结测试结果
    logger.info("=" * 50)
    logger.info("测试结果总结")
    logger.info("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！集成学习功能正常工作")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
