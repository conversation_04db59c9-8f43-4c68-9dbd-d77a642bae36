2025-07-16 00:02:49 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:02:50 - GUI - INFO - GUI界面初始化完成
2025-07-16 00:03:11 - model_ensemble - INFO - ============================================================
2025-07-16 00:03:11 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 00:03:11 - model_ensemble - INFO - ============================================================
2025-07-16 00:03:11 - model_ensemble - INFO - 基础模型: ['RandomForest', 'SVM', 'KNN']
2025-07-16 00:03:11 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 00:03:11 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 00:03:11 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 00:03:11 - model_training - INFO - 模型名称: Random Forest
2025-07-16 00:03:11 - model_training - INFO - 准确率: 0.9000
2025-07-16 00:03:11 - model_training - INFO - AUC: 0.9386
2025-07-16 00:03:11 - model_training - INFO - 混淆矩阵:
2025-07-16 00:03:11 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-16 00:03:11 - model_training - INFO - 
分类报告:
2025-07-16 00:03:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-16 00:03:11 - model_training - INFO - 训练时间: 0.08 秒
2025-07-16 00:03:11 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 00:03:11 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-16 00:03:11 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 00:03:11 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-16 00:03:11 - model_training - INFO - 模型名称: SVM
2025-07-16 00:03:11 - model_training - INFO - 准确率: 0.8000
2025-07-16 00:03:11 - model_training - INFO - AUC: 0.9207
2025-07-16 00:03:11 - model_training - INFO - 混淆矩阵:
2025-07-16 00:03:11 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-07-16 00:03:11 - model_training - INFO - 
分类报告:
2025-07-16 00:03:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-07-16 00:03:11 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 00:03:11 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 00:03:11 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-16 00:03:11 - model_ensemble - INFO -   SVM 训练完成
2025-07-16 00:03:11 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 00:03:11 - model_training - INFO - 模型名称: KNN
2025-07-16 00:03:11 - model_training - INFO - 准确率: 0.8750
2025-07-16 00:03:11 - model_training - INFO - AUC: 0.9322
2025-07-16 00:03:11 - model_training - INFO - 混淆矩阵:
2025-07-16 00:03:11 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-16 00:03:11 - model_training - INFO - 
分类报告:
2025-07-16 00:03:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 00:03:11 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 00:03:11 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-16 00:03:11 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-16 00:03:11 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 00:03:11 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-16 00:03:11 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 00:03:11 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 00:03:12 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8219
2025-07-16 00:03:12 - model_ensemble - INFO - ============================================================
2025-07-16 00:03:12 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 00:03:12 - model_ensemble - INFO - ============================================================
2025-07-16 00:03:12 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 00:03:12 - model_ensemble - INFO - 最佳F1分数: 0.8219
2025-07-16 00:03:12 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-16 00:03:12 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 00:03:12 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8287, 召回率: 0.8250, F1: 0.8219, AUC: 0.9463
2025-07-16 00:03:12 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 00:03:12 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_000312.joblib
2025-07-16 00:03:12 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 00:03:12 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-16 00:03:12 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 00:03:12 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 00:03:12 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 00:03:12 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-16 00:03:12 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([ 0.08245011,  0.16006776, -0.00884842, -0.02489161,  0.00250002,
        0.09556891, -0.00171511,  0.0209163 ])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.08245011, -0.16006776,  0.00884842,  0.02489161, -0.00250002,
       -0.09556891,  0.00171511, -0.0209163 ])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([ 0.06842657, -0.06569761, -0.04071069, -0.08469976,  0.03658027,
       -0.01931681, -0.01036062, -0.05031249])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.06842657,  0.06569761,  0.04071069,  0.08469976, -0.03658027,
        0.01931681,  0.01036062,  0.05031249])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.07687107, -0.087123  , -0.01211207, -0.02541045, -0.07053802,
       -0.27032952, -0.01066713, -0.01867114])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([0.07687107, 0.087123  , 0.01211207, 0.02541045, 0.07053802,
       0.27032952, 0.01066713, 0.01867114])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.18990095, -0.0204291 ,  0.02053969, -0.00991238, -0.08132911,
       -0.23990921, -0.01524824, -0.01988668])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([ 0.18990095,  0.0204291 , -0.02053969,  0.00991238,  0.08132911,
        0.23990921,  0.01524824,  0.01988668])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([ 0.08849268,  0.1547991 , -0.00074254, -0.01123607, -0.09405729,
       -0.01204174,  0.0350615 ,  0.11798287])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.08849268, -0.1547991 ,  0.00074254,  0.01123607,  0.09405729,
        0.01204174, -0.0350615 , -0.11798287])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([ 0.05805816,  0.10697641, -0.00057398,  0.03786925,  0.09797108,
        0.00326194,  0.01904048,  0.01998791])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.05805816, -0.10697641,  0.00057398, -0.03786925, -0.09797108,
       -0.00326194, -0.01904048, -0.01998791])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([ 0.10536393, -0.02377057,  0.00840063, -0.02306718,  0.00915651,
        0.23750014, -0.00135824, -0.01510133])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.10536393,  0.02377057, -0.00840063,  0.02306718, -0.00915651,
       -0.23750014,  0.00135824,  0.01510133])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([ 0.09513163, -0.05660696, -0.0055398 , -0.03184029, -0.08686743,
        0.05654161, -0.00881652,  0.05785759])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.09513163,  0.05660696,  0.0055398 ,  0.03184029,  0.08686743,
       -0.05654161,  0.00881652, -0.05785759])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.1398085 , -0.25719025,  0.02293741, -0.00917548, -0.08435136,
       -0.03020922, -0.02238734, -0.01151488])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([ 0.1398085 ,  0.25719025, -0.02293741,  0.00917548,  0.08435136,
        0.03020922,  0.02238734,  0.01151488])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([ 0.04272886,  0.21518293, -0.02033019, -0.02534677,  0.052869  ,
       -0.28593139, -0.02930468, -0.05688638])
2025-07-16 00:03:12 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:12 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:12 - shap - INFO - phi = array([-0.04272886, -0.21518293,  0.02033019,  0.02534677, -0.052869  ,
        0.28593139,  0.02930468,  0.05688638])
2025-07-16 00:03:12 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.04088313,  0.18867741,  0.00851113, -0.01194678, -0.00357268,
        0.13344981, -0.00338319, -0.01385352])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.04088313, -0.18867741, -0.00851113,  0.01194678,  0.00357268,
       -0.13344981,  0.00338319,  0.01385352])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.06533595,  0.13525497,  0.00318404, -0.01627159, -0.00268775,
        0.16860194,  0.00077225, -0.0111053 ])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.06533595, -0.13525497, -0.00318404,  0.01627159,  0.00268775,
       -0.16860194, -0.00077225,  0.0111053 ])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.05113031, -0.30270067, -0.02011545, -0.03305232, -0.09793961,
       -0.01839089, -0.00717934, -0.01798221])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([0.05113031, 0.30270067, 0.02011545, 0.03305232, 0.09793961,
       0.01839089, 0.00717934, 0.01798221])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.06265288, -0.17060589, -0.01296568, -0.02766323, -0.08688822,
       -0.19410023, -0.01077217, -0.00493793])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([0.06265288, 0.17060589, 0.01296568, 0.02766323, 0.08688822,
       0.19410023, 0.01077217, 0.00493793])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.0368083 , -0.20884257,  0.00491988, -0.02363074, -0.05735478,
        0.05201255, -0.02070211, -0.01324667])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.0368083 ,  0.20884257, -0.00491988,  0.02363074,  0.05735478,
       -0.05201255,  0.02070211,  0.01324667])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.09761234, -0.02788734,  0.00614243,  0.05159194,  0.14900935,
        0.00533792,  0.02141689,  0.0105375 ])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.09761234,  0.02788734, -0.00614243, -0.05159194, -0.14900935,
       -0.00533792, -0.02141689, -0.0105375 ])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.08374256,  0.17287413, -0.00500604, -0.01869003, -0.02686765,
        0.12262771, -0.00281195,  0.01445506])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.08374256, -0.17287413,  0.00500604,  0.01869003,  0.02686765,
       -0.12262771,  0.00281195, -0.01445506])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.07893502,  0.22535696, -0.01227543, -0.01301704,  0.02899906,
        0.05257694, -0.00314408, -0.01429134])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.07893502, -0.22535696,  0.01227543,  0.01301704, -0.02899906,
       -0.05257694,  0.00314408,  0.01429134])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.1049826 , -0.09648322,  0.00119756,  0.02051076,  0.14994754,
       -0.01291893, -0.0116468 , -0.04073097])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.1049826 ,  0.09648322, -0.00119756, -0.02051076, -0.14994754,
        0.01291893,  0.0116468 ,  0.04073097])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.03368023, -0.19201056, -0.00251449,  0.04336527, -0.03854472,
       -0.03160992,  0.03230105,  0.03644809])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.03368023,  0.19201056,  0.00251449, -0.04336527,  0.03854472,
        0.03160992, -0.03230105, -0.03644809])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.02482979, -0.07131049,  0.01359309,  0.12219147,  0.07923648,
       -0.11161192,  0.00308026, -0.00366462])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.02482979,  0.07131049, -0.01359309, -0.12219147, -0.07923648,
        0.11161192, -0.00308026,  0.00366462])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.00414643,  0.08569337,  0.00710125,  0.05540413,  0.14303346,
       -0.02663166,  0.01105431, -0.00658576])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.00414643, -0.08569337, -0.00710125, -0.05540413, -0.14303346,
        0.02663166, -0.01105431,  0.00658576])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.06154405,  0.13114839,  0.00027815,  0.03332756,  0.0977441 ,
        0.00505211,  0.02278712, -0.00911453])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.06154405, -0.13114839, -0.00027815, -0.03332756, -0.0977441 ,
       -0.00505211, -0.02278712,  0.00911453])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.07527785,  0.19166533, -0.00216405, -0.02045686,  0.00199155,
        0.08666843, -0.00453821,  0.01525331])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.07527785, -0.19166533,  0.00216405,  0.02045686, -0.00199155,
       -0.08666843,  0.00453821, -0.01525331])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.09065599, -0.11756536, -0.00912651, -0.02717601, -0.08762023,
       -0.23137673, -0.00664655, -0.0012528 ])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([0.09065599, 0.11756536, 0.00912651, 0.02717601, 0.08762023,
       0.23137673, 0.00664655, 0.0012528 ])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([ 0.06120246,  0.10578084, -0.0006691 ,  0.03342876,  0.10336162,
        0.01010642,  0.01836347,  0.01249204])
2025-07-16 00:03:13 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:13 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:13 - shap - INFO - phi = array([-0.06120246, -0.10578084,  0.0006691 , -0.03342876, -0.10336162,
       -0.01010642, -0.01836347, -0.01249204])
2025-07-16 00:03:13 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.15430849, -0.15980886,  0.03274232, -0.00494601, -0.10552739,
       -0.13051674, -0.01750438, -0.01802974])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.15430849,  0.15980886, -0.03274232,  0.00494601,  0.10552739,
        0.13051674,  0.01750438,  0.01802974])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.10027002,  0.15910812, -0.01518665, -0.01526797,  0.04981133,
        0.07140959, -0.00398481, -0.01053397])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.10027002, -0.15910812,  0.01518665,  0.01526797, -0.04981133,
       -0.07140959,  0.00398481,  0.01053397])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.01028619, -0.1140485 , -0.01108292, -0.00486094, -0.0890459 ,
       -0.3103193 , -0.01257822, -0.02316036])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.01028619,  0.1140485 ,  0.01108292,  0.00486094,  0.0890459 ,
        0.3103193 ,  0.01257822,  0.02316036])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.28910559, -0.20380413,  0.02691432,  0.0087775 , -0.06206776,
        0.14739235, -0.02467665, -0.02706182])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.28910559,  0.20380413, -0.02691432, -0.0087775 ,  0.06206776,
       -0.14739235,  0.02467665,  0.02706182])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.06534455,  0.15065486, -0.00070341, -0.01838105,  0.00380808,
        0.15042791, -0.00190237, -0.0038325 ])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.06534455, -0.15065486,  0.00070341,  0.01838105, -0.00380808,
       -0.15042791,  0.00190237,  0.0038325 ])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.05618745, -0.33448075, -0.01039213, -0.03294513, -0.08749356,
        0.09953167, -0.02392245, -0.01575092])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.05618745,  0.33448075,  0.01039213,  0.03294513,  0.08749356,
       -0.09953167,  0.02392245,  0.01575092])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.01565985, -0.02894147,  0.01135514,  0.12305848,  0.09434897,
       -0.05204852,  0.04122599,  0.07334896])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.01565985,  0.02894147, -0.01135514, -0.12305848, -0.09434897,
        0.05204852, -0.04122599, -0.07334896])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.00269012,  0.20343123,  0.0038137 , -0.00703883,  0.07826245,
        0.04465591, -0.00470866, -0.0163502 ])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.00269012, -0.20343123, -0.0038137 ,  0.00703883, -0.07826245,
       -0.04465591,  0.00470866,  0.0163502 ])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-2.00084570e-01, -9.30796615e-02,  7.51757853e-03, -1.68618162e-04,
       -6.10989735e-02,  2.65425753e-01,  1.24096299e-02,  1.59849218e-02])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 2.00084570e-01,  9.30796615e-02, -7.51757853e-03,  1.68618162e-04,
        6.10989735e-02, -2.65425753e-01, -1.24096299e-02, -1.59849218e-02])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.04315226,  0.0823724 ,  0.01364549, -0.00945626, -0.00456716,
        0.1936199 ,  0.01325787,  0.003006  ])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.04315226, -0.0823724 , -0.01364549,  0.00945626,  0.00456716,
       -0.1936199 , -0.01325787, -0.003006  ])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-1.96678542e-01,  1.01238174e-01,  1.58171978e-02,  6.71861276e-04,
       -1.88297808e-04,  1.14431418e-01,  2.26130943e-02,  2.85636176e-02])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 1.96678542e-01, -1.01238174e-01, -1.58171978e-02, -6.71861276e-04,
        1.88297808e-04, -1.14431418e-01, -2.26130943e-02, -2.85636176e-02])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([0.06546755, 0.06030751, 0.0002362 , 0.03831598, 0.11941348,
       0.00913081, 0.02580871, 0.02483182])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.06546755, -0.06030751, -0.0002362 , -0.03831598, -0.11941348,
       -0.00913081, -0.02580871, -0.02483182])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.02118608, -0.12225847, -0.0050308 , -0.0129503 , -0.09236643,
       -0.29435305, -0.00939808, -0.01975796])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.02118608,  0.12225847,  0.0050308 ,  0.0129503 ,  0.09236643,
        0.29435305,  0.00939808,  0.01975796])
2025-07-16 00:03:14 - shap - INFO - num_full_subsets = 4
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([ 0.09565143,  0.12405553, -0.01275687, -0.02501324,  0.02292999,
        0.14628404, -0.00983473, -0.00804997])
2025-07-16 00:03:14 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-16 00:03:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-16 00:03:14 - shap - INFO - phi = array([-0.09565143, -0.12405553,  0.01275687,  0.02501324, -0.02292999,
       -0.14628404,  0.00983473,  0.00804997])
2025-07-16 00:03:19 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-16 00:03:19 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 00:03:19 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 00:03:19 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 00:03:19 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 00:03:23 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 00:03:23 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-16 00:03:23 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
