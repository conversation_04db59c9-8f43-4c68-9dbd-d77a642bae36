#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的SHAP可视化模块
提供完整的SHAP图表：摘要图、特征重要性、力图、瀑布图、依赖图等
"""

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from pathlib import Path
import warnings
import logging

# 设置安全的matplotlib配置
plt.rcParams.update(plt.rcParamsDefault)
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.max_open_warning'] = 0
plt.rcParams['font.size'] = 10

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)

try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    logger.warning("SHAP library not available")

def setup_safe_shap_plotting():
    """设置安全的SHAP绘图环境"""
    if not SHAP_AVAILABLE:
        return False
    
    try:
        # 设置SHAP的matplotlib后端
        shap.plots._config.matplotlib_backend = 'Agg'
        return True
    except:
        return True  # 如果设置失败，继续执行

def create_complete_shap_analysis(model, X_data, feature_names, model_name, output_dir, max_samples=200):
    """
    创建完整的SHAP分析图表
    
    Args:
        model: 训练好的模型
        X_data: 输入数据
        feature_names: 特征名称列表
        model_name: 模型名称
        output_dir: 输出目录
        max_samples: 最大样本数量
    
    Returns:
        dict: 生成的图表路径字典
    """
    if not SHAP_AVAILABLE:
        logger.warning("SHAP not available, skipping SHAP analysis")
        return {}
    
    setup_safe_shap_plotting()
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    results = {}
    
    try:
        # 准备数据
        if hasattr(X_data, 'values'):
            X_array = X_data.values
        else:
            X_array = X_data
            
        # 限制样本数量以提高性能
        if len(X_array) > max_samples:
            indices = np.random.choice(len(X_array), max_samples, replace=False)
            X_sample = X_array[indices]
        else:
            X_sample = X_array
            
        # 创建DataFrame用于SHAP分析
        X_df = pd.DataFrame(X_sample, columns=feature_names)
        
        # 创建SHAP解释器
        explainer = _create_shap_explainer(model, X_sample)
        if explainer is None:
            return {}
            
        # 计算SHAP值
        shap_values = explainer(X_sample)
        
        # 1. 生成摘要图（Summary Plot）
        results['summary'] = _create_summary_plot(shap_values, X_df, model_name, output_dir)
        
        # 2. 生成特征重要性条形图
        results['importance'] = _create_importance_plot(shap_values, feature_names, model_name, output_dir)
        
        # 3. 生成力图（Force Plot）
        results['force'] = _create_force_plots(shap_values, X_df, model_name, output_dir)
        
        # 4. 生成瀑布图（Waterfall Plot）
        results['waterfall'] = _create_waterfall_plots(shap_values, X_df, model_name, output_dir)
        
        # 5. 生成依赖图（Dependence Plot）
        results['dependence'] = _create_dependence_plots(shap_values, X_df, model_name, output_dir)
        
        # 6. 生成决策图（Decision Plot）
        results['decision'] = _create_decision_plot(shap_values, X_df, model_name, output_dir)
        
        logger.info(f"Complete SHAP analysis generated for {model_name}")
        return results
        
    except Exception as e:
        logger.error(f"Failed to create SHAP analysis for {model_name}: {e}")
        return {}

def _create_shap_explainer(model, X_sample):
    """创建SHAP解释器"""
    try:
        model_type = str(type(model)).lower()

        # 处理集成模型（VotingClassifier, BaggingClassifier等）
        if 'voting' in model_type or 'bagging' in model_type:
            logger.info("检测到集成模型，使用KernelExplainer")
            background_size = min(50, len(X_sample))  # 减少背景样本以提高速度
            background = X_sample[:background_size]

            if hasattr(model, 'predict_proba'):
                return shap.KernelExplainer(model.predict_proba, background)
            else:
                return shap.KernelExplainer(model.predict, background)

        # 尝试TreeExplainer（适用于单个树模型）
        if (hasattr(model, 'estimators_') and not 'voting' in model_type) or 'forest' in model_type or 'tree' in model_type:
            try:
                return shap.TreeExplainer(model)
            except Exception as e:
                logger.warning(f"TreeExplainer failed: {e}, falling back to KernelExplainer")

        # 尝试LinearExplainer（适用于线性模型）
        if 'linear' in model_type or 'logistic' in model_type:
            try:
                return shap.LinearExplainer(model, X_sample)
            except Exception as e:
                logger.warning(f"LinearExplainer failed: {e}, falling back to KernelExplainer")

        # 回退到KernelExplainer（通用但较慢）
        logger.info("使用通用KernelExplainer")
        background_size = min(50, len(X_sample))
        background = X_sample[:background_size]

        if hasattr(model, 'predict_proba'):
            return shap.KernelExplainer(model.predict_proba, background)
        else:
            return shap.KernelExplainer(model.predict, background)

    except Exception as e:
        logger.warning(f"Failed to create SHAP explainer: {e}")
        return None

def _create_summary_plot(shap_values, X_df, model_name, output_dir):
    """创建完整的摘要图 - 纵轴为特征名，横轴为SHAP值，右侧显示特征值颜色条"""
    try:
        # 设置matplotlib参数
        plt.rcParams.update({
            'font.size': 11,
            'axes.labelsize': 12,
            'axes.titlesize': 14,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'figure.facecolor': 'white'
        })

        # 创建图形，但不创建子图，让SHAP自己处理
        plt.figure(figsize=(12, 8))

        # 使用SHAP原生摘要图，确保正确的轴向和颜色条
        shap.summary_plot(
            shap_values,
            X_df,
            feature_names=X_df.columns.tolist(),
            show=False,
            max_display=min(15, len(X_df.columns)),
            plot_type='dot'  # 确保是点图，显示特征值颜色
        )

        # 获取当前图形和轴
        fig = plt.gcf()
        ax = plt.gca()

        # 确保轴标签正确设置
        ax.set_xlabel('SHAP value (impact on model output)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Features', fontsize=12, fontweight='bold')

        # 设置标题
        plt.title(f'{model_name} - SHAP Summary Plot', fontsize=16, fontweight='bold', pad=20)

        # 确保y轴标签显示
        ax.tick_params(axis='y', which='major', labelsize=10)
        ax.tick_params(axis='x', which='major', labelsize=10)

        # 查找并设置颜色条标签
        for child in fig.get_children():
            if hasattr(child, 'get_label') and 'colorbar' in str(type(child)).lower():
                try:
                    child.set_label('Feature value', fontsize=11, fontweight='bold')
                except:
                    pass

        # 尝试通过其他方式找到颜色条
        try:
            # 查找所有轴，包括颜色条轴
            for ax_child in fig.get_axes():
                if ax_child != ax:  # 不是主轴，可能是颜色条轴
                    ax_child.set_ylabel('Feature value', fontsize=11, fontweight='bold')
        except:
            pass

        # 添加网格线
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5, axis='x')
        ax.set_facecolor('#FAFAFA')

        # 调整布局
        plt.tight_layout()

        summary_path = output_dir / f'{model_name}_shap_summary.png'
        plt.savefig(summary_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()

        # 创建条形图摘要（特征重要性）- 修复纵坐标显示
        fig, ax = plt.subplots(figsize=(10, 8))

        # 手动创建条形图以确保特征名正确显示
        if hasattr(shap_values, 'values'):
            importance = np.abs(shap_values.values).mean(0)
        else:
            importance = np.abs(shap_values).mean(0)

        # 处理多维情况
        if len(importance.shape) > 1:
            importance = importance.mean(1) if importance.shape[1] > 1 else importance[:, 0]

        # 创建DataFrame并排序
        feature_names = X_df.columns.tolist()
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance
        }).sort_values('importance', ascending=True)

        # 取前12个重要特征
        top_features = importance_df.tail(12)

        # 创建颜色渐变
        colors = plt.cm.Blues(np.linspace(0.4, 0.9, len(top_features)))

        # 绘制水平条形图
        bars = ax.barh(range(len(top_features)), top_features['importance'],
                      color=colors, alpha=0.8, height=0.7)

        # 确保y轴显示特征名
        ax.set_yticks(range(len(top_features)))
        ax.set_yticklabels(top_features['feature'], fontsize=11)

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, top_features['importance'])):
            ax.text(value + max(top_features['importance']) * 0.01, i,
                   f'{value:.3f}', va='center', ha='left', fontsize=9, fontweight='bold')

        # 设置标签和标题
        ax.set_xlabel('Mean |SHAP Value|', fontsize=12, fontweight='bold')
        ax.set_title(f'{model_name} - SHAP Feature Importance', fontsize=16, fontweight='bold', pad=20)

        # 添加网格线
        ax.grid(True, alpha=0.3, axis='x', linestyle='-', linewidth=0.5)
        ax.set_axisbelow(True)

        # 设置背景色和边框
        ax.set_facecolor('#FAFAFA')
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_color('#CCCCCC')
        ax.spines['bottom'].set_color('#CCCCCC')

        # 设置x轴范围
        ax.set_xlim(0, max(top_features['importance']) * 1.15)

        plt.tight_layout()

        bar_summary_path = output_dir / f'{model_name}_shap_summary_bar.png'
        plt.savefig(bar_summary_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()

        return {'dot': str(summary_path), 'bar': str(bar_summary_path)}

    except Exception as e:
        logger.error(f"Failed to create summary plot: {e}")
        return None

def _create_importance_plot(shap_values, feature_names, model_name, output_dir):
    """创建专业风格的特征重要性图 - 参考第一张图的样式"""
    try:
        fig, ax = plt.subplots(figsize=(10, 8))

        # 计算特征重要性
        if hasattr(shap_values, 'values'):
            importance = np.abs(shap_values.values).mean(0)
        else:
            importance = np.abs(shap_values).mean(0)

        # 处理多维情况
        if len(importance.shape) > 1:
            importance = importance.mean(1) if importance.shape[1] > 1 else importance[:, 0]

        # 创建DataFrame并排序
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance
        }).sort_values('importance', ascending=True)

        # 取前12个重要特征（参考图片样式）
        top_features = importance_df.tail(12)

        # 创建颜色渐变 - 从浅蓝到深蓝
        colors = plt.cm.Blues(np.linspace(0.4, 0.9, len(top_features)))

        # 绘制水平条形图
        bars = ax.barh(range(len(top_features)), top_features['importance'],
                      color=colors, alpha=0.8, height=0.7)

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, top_features['importance'])):
            ax.text(value + max(top_features['importance']) * 0.01, i,
                   f'{value:.3f}', va='center', ha='left', fontsize=9, fontweight='bold')

        # 设置y轴标签
        ax.set_yticks(range(len(top_features)))
        ax.set_yticklabels(top_features['feature'], fontsize=11)

        # 设置x轴
        ax.set_xlabel('Mean |SHAP Value|', fontsize=12, fontweight='bold')
        ax.set_title(f'{model_name} - Feature Importance', fontsize=14, fontweight='bold', pad=20)

        # 添加网格线
        ax.grid(True, alpha=0.3, axis='x', linestyle='-', linewidth=0.5)
        ax.set_axisbelow(True)

        # 设置背景色
        ax.set_facecolor('#FAFAFA')

        # 移除顶部和右侧边框
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_color('#CCCCCC')
        ax.spines['bottom'].set_color('#CCCCCC')

        # 设置x轴范围，留出空间显示数值
        ax.set_xlim(0, max(top_features['importance']) * 1.15)

        plt.tight_layout()

        importance_path = output_dir / f'{model_name}_feature_importance.png'
        plt.savefig(importance_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        return str(importance_path)

    except Exception as e:
        logger.error(f"Failed to create importance plot: {e}")
        return None

def _create_force_plots(shap_values, X_df, model_name, output_dir):
    """创建力图"""
    try:
        force_plots = []

        # 为前3个样本创建力图
        for i in range(min(3, len(X_df))):
            try:
                plt.figure(figsize=(12, 4))

                # 创建力图
                if hasattr(shap_values, 'values'):
                    values = shap_values.values[i]
                    base_value = shap_values.base_values[i] if hasattr(shap_values, 'base_values') else 0
                else:
                    values = shap_values[i]
                    base_value = 0

                # 处理多类情况
                if len(values.shape) > 1:
                    values = values[:, 1] if values.shape[1] > 1 else values[:, 0]
                    if hasattr(base_value, '__len__') and len(base_value) > 1:
                        base_value = base_value[1] if len(base_value) > 1 else base_value[0]

                # 手动创建力图效果
                feature_values = X_df.iloc[i].values
                feature_names = X_df.columns.tolist()

                # 排序特征按重要性
                importance_order = np.argsort(np.abs(values))[::-1][:10]  # 前10个重要特征

                # 创建条形图显示SHAP值
                colors = ['red' if v > 0 else 'blue' for v in values[importance_order]]
                plt.barh(range(len(importance_order)), values[importance_order], color=colors, alpha=0.7)

                # 添加特征名和值
                labels = [f"{feature_names[idx]} = {feature_values[idx]:.3f}" for idx in importance_order]
                plt.yticks(range(len(importance_order)), labels)
                plt.xlabel('SHAP Value')
                plt.title(f'{model_name} - Force Plot (Sample {i+1})', fontsize=12)
                plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)
                plt.grid(True, alpha=0.3)
                plt.tight_layout()

                force_path = output_dir / f'{model_name}_force_plot_sample_{i+1}.png'
                plt.savefig(force_path, dpi=300, bbox_inches='tight', facecolor='white')
                plt.close()

                force_plots.append(str(force_path))

            except Exception as e:
                logger.warning(f"Failed to create force plot for sample {i+1}: {e}")
                continue

        return force_plots

    except Exception as e:
        logger.error(f"Failed to create force plots: {e}")
        return []

def _create_waterfall_plots(shap_values, X_df, model_name, output_dir):
    """创建箭头式瀑布图 - 纵轴按贡献绝对值从大到小排序，使用箭头指示贡献方向"""
    try:
        waterfall_plots = []

        # 为前3个样本创建瀑布图
        for i in range(min(3, len(X_df))):
            try:
                # 使用更大的图形尺寸以容纳箭头设计
                fig, ax = plt.subplots(figsize=(14, 10))

                # 获取SHAP值
                if hasattr(shap_values, 'values'):
                    values = shap_values.values[i]
                    base_value = shap_values.base_values[i] if hasattr(shap_values, 'base_values') else 0
                else:
                    values = shap_values[i]
                    base_value = 0

                # 处理多类情况
                if len(values.shape) > 1:
                    values = values[:, 1] if values.shape[1] > 1 else values[:, 0]
                    if hasattr(base_value, '__len__') and len(base_value) > 1:
                        base_value = base_value[1] if len(base_value) > 1 else base_value[0]

                feature_values = X_df.iloc[i].values
                feature_names = X_df.columns.tolist()

                # 按贡献绝对值从大到小排序特征
                importance_order = np.argsort(np.abs(values))[::-1][:10]
                selected_values = values[importance_order]
                selected_names = [feature_names[idx] for idx in importance_order]
                selected_feature_values = [feature_values[idx] for idx in importance_order]

                # 创建瀑布图数据结构 - 从上到下按贡献绝对值排序
                cumulative = base_value
                n_features = len(selected_names)

                # 计算y位置 - 从上到下排列
                y_positions = []
                labels = []
                arrow_data = []

                # 最终预测值 f(x) - 放在最上方
                final_prediction = base_value + np.sum(selected_values)
                y_positions.append(n_features + 2)
                labels.append(f'f(x) = {final_prediction:.3f}')

                # 特征贡献 - 按绝对值从大到小，从上往下排列
                for j, (name, value, feat_val) in enumerate(zip(selected_names, selected_values, selected_feature_values)):
                    y_pos = n_features + 1 - j
                    y_positions.append(y_pos)

                    # 创建特征标签 - 包含特征名和数值
                    if isinstance(feat_val, (int, float)):
                        if abs(feat_val) < 0.01:
                            feat_str = f"{feat_val:.4f}"
                        elif abs(feat_val) < 1:
                            feat_str = f"{feat_val:.3f}"
                        else:
                            feat_str = f"{feat_val:.2f}"
                    else:
                        feat_str = str(feat_val)

                    labels.append(f"{name} = {feat_str}")

                    # 存储箭头数据
                    arrow_data.append({
                        'y_pos': y_pos,
                        'value': value,
                        'cumulative_before': cumulative,
                        'name': name,
                        'feat_val': feat_str
                    })

                    cumulative += value

                # 基准值 E[f(X)] - 放在最下方
                y_positions.append(0)
                labels.append(f'E[f(X)] = {base_value:.3f}')

                # 绘制箭头式瀑布图
                for arrow_info in arrow_data:
                    y_pos = arrow_info['y_pos']
                    value = arrow_info['value']

                    # 箭头起始位置（从0开始）
                    arrow_start = 0
                    arrow_length = abs(value)

                    if value > 0:
                        # 正向贡献 - 红色箭头向右
                        color = '#FF4444'
                        arrow_direction = 1
                        arrow_end = arrow_length
                    else:
                        # 负向贡献 - 蓝色箭头向左
                        color = '#4488FF'
                        arrow_direction = -1
                        arrow_end = -arrow_length

                    # 绘制箭头主体（矩形部分）
                    arrow_height = 0.6
                    rect_width = arrow_length * 0.8  # 箭头主体占80%

                    if value > 0:
                        rect_x = arrow_start
                    else:
                        rect_x = arrow_start - rect_width

                    # 绘制矩形主体
                    rect = plt.Rectangle((rect_x, y_pos - arrow_height/2),
                                       rect_width, arrow_height,
                                       facecolor=color, alpha=0.8,
                                       edgecolor='white', linewidth=1)
                    ax.add_patch(rect)

                    # 绘制箭头头部（三角形）
                    arrow_head_width = arrow_length * 0.2  # 箭头头部占20%
                    arrow_head_height = arrow_height * 1.2

                    if value > 0:
                        # 向右的箭头
                        triangle_x = rect_x + rect_width
                        triangle_points = [
                            [triangle_x, y_pos - arrow_head_height/2],
                            [triangle_x, y_pos + arrow_head_height/2],
                            [triangle_x + arrow_head_width, y_pos]
                        ]
                    else:
                        # 向左的箭头
                        triangle_x = rect_x
                        triangle_points = [
                            [triangle_x, y_pos - arrow_head_height/2],
                            [triangle_x, y_pos + arrow_head_height/2],
                            [triangle_x - arrow_head_width, y_pos]
                        ]

                    # 绘制三角形箭头头部
                    triangle = plt.Polygon(triangle_points, facecolor=color, alpha=0.8,
                                         edgecolor='white', linewidth=1)
                    ax.add_patch(triangle)

                    # 添加数值标签
                    if value > 0:
                        label_x = arrow_end + 0.02
                        ha = 'left'
                    else:
                        label_x = arrow_end - 0.02
                        ha = 'right'

                    ax.text(label_x, y_pos, f"{value:+.3f}", ha=ha, va='center',
                           fontsize=10, fontweight='bold', color='black')

                # 绘制基准值和最终预测值的标记
                # 基准值标记
                ax.plot([base_value, base_value], [-0.5, n_features + 2.5],
                       'k--', alpha=0.6, linewidth=2, label='E[f(X)]')
                ax.text(base_value, -0.8, f'E[f(X)] = {base_value:.3f}',
                       ha='center', va='top', fontsize=11, fontweight='bold')

                # 最终预测值标记
                ax.plot([final_prediction, final_prediction], [-0.5, n_features + 2.5],
                       'k-', alpha=0.8, linewidth=2, label='f(x)')
                ax.text(final_prediction, n_features + 3, f'f(x) = {final_prediction:.3f}',
                       ha='center', va='bottom', fontsize=11, fontweight='bold')

                # 设置坐标轴和标签
                ax.set_yticks(y_positions)
                ax.set_yticklabels(labels, fontsize=10)
                ax.set_xlabel('Prediction Value', fontsize=12, fontweight='bold')
                ax.set_ylabel('Features (sorted by contribution magnitude)', fontsize=12, fontweight='bold')
                ax.set_title(f'{model_name} - Waterfall Plot (Sample {i+1})',
                           fontsize=14, fontweight='bold', pad=20)

                # 添加零线
                ax.axvline(x=0, color='black', linestyle='-', linewidth=2, alpha=0.8)

                # 设置x轴范围，确保箭头完全可见
                all_values = [base_value, final_prediction] + [arrow['value'] for arrow in arrow_data]
                x_min, x_max = min(all_values), max(all_values)
                x_range = x_max - x_min
                ax.set_xlim(x_min - x_range * 0.15, x_max + x_range * 0.15)

                # 设置y轴范围
                ax.set_ylim(-1.5, n_features + 3.5)

                # 添加网格
                ax.grid(True, alpha=0.3, axis='x', linestyle='-', linewidth=0.5)

                # 设置背景
                ax.set_facecolor('#FAFAFA')

                # 移除上方和右侧边框
                ax.spines['top'].set_visible(False)
                ax.spines['right'].set_visible(False)
                ax.spines['left'].set_linewidth(1)
                ax.spines['bottom'].set_linewidth(1)

                # 添加图例
                from matplotlib.patches import Patch
                legend_elements = [
                    Patch(facecolor='#FF4444', alpha=0.8, label='Positive contribution'),
                    Patch(facecolor='#4488FF', alpha=0.8, label='Negative contribution'),
                    plt.Line2D([0], [0], color='black', linestyle='--', alpha=0.6, label='E[f(X)]'),
                    plt.Line2D([0], [0], color='black', linestyle='-', alpha=0.8, label='f(x)')
                ]
                ax.legend(handles=legend_elements, loc='upper right', fontsize=10)

                # 调整布局
                plt.tight_layout()

                waterfall_path = output_dir / f'{model_name}_waterfall_plot_sample_{i+1}.png'
                plt.savefig(waterfall_path, dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                plt.close()

                waterfall_plots.append(str(waterfall_path))

            except Exception as e:
                logger.warning(f"Failed to create waterfall plot for sample {i+1}: {e}")
                continue

        return waterfall_plots

    except Exception as e:
        logger.error(f"Failed to create waterfall plots: {e}")
        return []

def _create_dependence_plots(shap_values, X_df, model_name, output_dir):
    """创建依赖图"""
    try:
        dependence_plots = []

        # 计算特征重要性，选择前5个重要特征
        if hasattr(shap_values, 'values'):
            importance = np.abs(shap_values.values).mean(0)
        else:
            importance = np.abs(shap_values).mean(0)

        if len(importance.shape) > 1:
            importance = importance.mean(1) if importance.shape[1] > 1 else importance[:, 0]

        top_features_idx = np.argsort(importance)[::-1][:5]

        for feature_idx in top_features_idx:
            try:
                plt.figure(figsize=(10, 6))

                feature_name = X_df.columns[feature_idx]
                feature_values = X_df.iloc[:, feature_idx].values

                if hasattr(shap_values, 'values'):
                    shap_vals = shap_values.values[:, feature_idx]
                else:
                    shap_vals = shap_values[:, feature_idx]

                # 处理多类情况
                if len(shap_vals.shape) > 1:
                    shap_vals = shap_vals[:, 1] if shap_vals.shape[1] > 1 else shap_vals[:, 0]

                # 创建散点图
                plt.scatter(feature_values, shap_vals, alpha=0.6, s=20)
                plt.xlabel(f'{feature_name} (Feature Value)')
                plt.ylabel(f'SHAP Value for {feature_name}')
                plt.title(f'{model_name} - Dependence Plot: {feature_name}')
                plt.grid(True, alpha=0.3)

                # 添加趋势线
                try:
                    z = np.polyfit(feature_values, shap_vals, 1)
                    p = np.poly1d(z)
                    plt.plot(feature_values, p(feature_values), "r--", alpha=0.8, linewidth=2)
                except:
                    pass

                plt.tight_layout()

                dependence_path = output_dir / f'{model_name}_dependence_{feature_name}.png'
                plt.savefig(dependence_path, dpi=300, bbox_inches='tight', facecolor='white')
                plt.close()

                dependence_plots.append(str(dependence_path))

            except Exception as e:
                logger.warning(f"Failed to create dependence plot for {feature_name}: {e}")
                continue

        return dependence_plots

    except Exception as e:
        logger.error(f"Failed to create dependence plots: {e}")
        return []

def _create_decision_plot(shap_values, X_df, model_name, output_dir):
    """创建决策图"""
    try:
        plt.figure(figsize=(12, 8))

        # 获取SHAP值
        if hasattr(shap_values, 'values'):
            values = shap_values.values
            base_value = shap_values.base_values[0] if hasattr(shap_values, 'base_values') else 0
        else:
            values = shap_values
            base_value = 0

        # 处理多类情况
        if len(values.shape) > 2:
            values = values[:, :, 1] if values.shape[2] > 1 else values[:, :, 0]
            if hasattr(base_value, '__len__') and len(base_value) > 1:
                base_value = base_value[1] if len(base_value) > 1 else base_value[0]

        # 选择前10个样本和前10个重要特征
        n_samples = min(10, len(values))
        feature_importance = np.abs(values).mean(0)
        top_features_idx = np.argsort(feature_importance)[::-1][:10]

        selected_values = values[:n_samples, top_features_idx]
        selected_features = [X_df.columns[i] for i in top_features_idx]

        # 创建决策图
        cumulative_values = np.zeros((n_samples, len(selected_features) + 1))
        cumulative_values[:, 0] = base_value

        for i in range(len(selected_features)):
            cumulative_values[:, i + 1] = cumulative_values[:, i] + selected_values[:, i]

        # 绘制决策路径
        for i in range(n_samples):
            plt.plot(range(len(selected_features) + 1), cumulative_values[i],
                    alpha=0.6, linewidth=1, marker='o', markersize=3)

        # 添加特征名标签
        feature_labels = ['Base'] + selected_features
        plt.xticks(range(len(feature_labels)), feature_labels, rotation=45, ha='right')
        plt.ylabel('Model Output')
        plt.title(f'{model_name} - Decision Plot (Top {n_samples} Samples)')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        decision_path = output_dir / f'{model_name}_decision_plot.png'
        plt.savefig(decision_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return str(decision_path)

    except Exception as e:
        logger.error(f"Failed to create decision plot: {e}")
        return None
