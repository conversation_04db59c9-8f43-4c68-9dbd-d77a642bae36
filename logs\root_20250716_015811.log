2025-07-16 01:58:12 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 01:58:13 - GUI - INFO - GUI界面初始化完成
2025-07-16 01:58:34 - model_ensemble - INFO - ============================================================
2025-07-16 01:58:34 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 01:58:34 - model_ensemble - INFO - ============================================================
2025-07-16 01:58:34 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM']
2025-07-16 01:58:34 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 01:58:34 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 01:58:34 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 01:58:34 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 01:58:34 - model_training - INFO - 准确率: 0.8250
2025-07-16 01:58:34 - model_training - INFO - AUC: 0.9028
2025-07-16 01:58:34 - model_training - INFO - 混淆矩阵:
2025-07-16 01:58:34 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-07-16 01:58:34 - model_training - INFO - 
分类报告:
2025-07-16 01:58:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-07-16 01:58:34 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 01:58:34 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 01:58:34 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-07-16 01:58:34 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 01:58:34 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 01:58:35 - model_training - INFO - 模型名称: Random Forest
2025-07-16 01:58:35 - model_training - INFO - 准确率: 0.8250
2025-07-16 01:58:35 - model_training - INFO - AUC: 0.9015
2025-07-16 01:58:35 - model_training - INFO - 混淆矩阵:
2025-07-16 01:58:35 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-16 01:58:35 - model_training - INFO - 
分类报告:
2025-07-16 01:58:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 01:58:35 - model_training - INFO - 训练时间: 0.09 秒
2025-07-16 01:58:35 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 01:58:35 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-16 01:58:35 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 01:58:35 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-16 01:58:35 - model_training - INFO - 模型名称: XGBoost
2025-07-16 01:58:35 - model_training - INFO - 准确率: 0.7750
2025-07-16 01:58:35 - model_training - INFO - AUC: 0.8568
2025-07-16 01:58:35 - model_training - INFO - 混淆矩阵:
2025-07-16 01:58:35 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-16 01:58:35 - model_training - INFO - 
分类报告:
2025-07-16 01:58:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-16 01:58:35 - model_training - INFO - 训练时间: 0.05 秒
2025-07-16 01:58:35 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 01:58:35 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-16 01:58:35 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-16 01:58:35 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-16 01:58:36 - model_training - INFO - 模型名称: LightGBM
2025-07-16 01:58:36 - model_training - INFO - 准确率: 0.7500
2025-07-16 01:58:36 - model_training - INFO - AUC: 0.8568
2025-07-16 01:58:36 - model_training - INFO - 混淆矩阵:
2025-07-16 01:58:36 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-07-16 01:58:36 - model_training - INFO - 
分类报告:
2025-07-16 01:58:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-07-16 01:58:36 - model_training - INFO - 训练时间: 1.69 秒
2025-07-16 01:58:36 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-16 01:58:36 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-16 01:58:36 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-16 01:58:36 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-16 01:58:36 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 01:58:36 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 01:58:38 - model_ensemble - INFO -   stacking - 准确率: 0.8000, F1: 0.8000
2025-07-16 01:58:38 - model_ensemble - INFO - ============================================================
2025-07-16 01:58:38 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 01:58:38 - model_ensemble - INFO - ============================================================
2025-07-16 01:58:38 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 01:58:38 - model_ensemble - INFO - 最佳F1分数: 0.8000
2025-07-16 01:58:38 - model_ensemble - INFO - 最佳准确率: 0.8000
2025-07-16 01:58:38 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 01:58:38 - model_ensemble - INFO -   stacking        - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.8000, AUC: 0.8875
2025-07-16 01:58:38 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 01:58:38 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_015838.joblib
2025-07-16 01:58:38 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 01:58:38 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-16 01:58:38 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 01:58:38 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 01:58:38 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 01:58:38 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-16 01:58:38 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-16 01:58:38 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.10601575,  0.10508763,  0.02807508,  0.01457547,  0.0042267 ,
        0.00129457,  0.12100381, -0.00382582, -0.01597269])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.10601575, -0.10508763, -0.02807508, -0.01457547, -0.0042267 ,
       -0.00129457, -0.12100381,  0.00382582,  0.01597269])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.09539007,  0.08906565,  0.02578161,  0.01238877,  0.01309047,
       -0.02090993,  0.14420671, -0.0042857 ,  0.0103633 ])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.09539007, -0.08906565, -0.02578161, -0.01238877, -0.01309047,
        0.02090993, -0.14420671,  0.0042857 , -0.0103633 ])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.18886574, -0.15143351, -0.05416962, -0.03110312, -0.01903476,
        0.0089838 , -0.06919161, -0.00686039,  0.00798283])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.18886574,  0.15143351,  0.05416962,  0.03110312,  0.01903476,
       -0.0089838 ,  0.06919161,  0.00686039, -0.00798283])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.13320399, -0.08872346, -0.04424831,  0.00886502,  0.00378163,
       -0.02390281, -0.23029566,  0.00365351, -0.00467153])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.13320399,  0.08872346,  0.04424831, -0.00886502, -0.00378163,
        0.02390281,  0.23029566, -0.00365351,  0.00467153])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.06521178,  0.11869929,  0.02550653,  0.01530779,  0.00976756,
        0.02254895,  0.11025154,  0.01204487, -0.01471034])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.06521178, -0.11869929, -0.02550653, -0.01530779, -0.00976756,
       -0.02254895, -0.11025154, -0.01204487,  0.01471034])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.08692406,  0.08580295,  0.0252764 , -0.01858735,  0.02671201,
        0.0239704 ,  0.09949217,  0.00322222,  0.02272799])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.08692406, -0.08580295, -0.0252764 ,  0.01858735, -0.02671201,
       -0.0239704 , -0.09949217, -0.00322222, -0.02272799])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.08892115,  0.11018131,  0.03017706,  0.01231806,  0.008377  ,
       -0.01789297,  0.14834351, -0.00589664, -0.01431213])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.08892115, -0.11018131, -0.03017706, -0.01231806, -0.008377  ,
        0.01789297, -0.14834351,  0.00589664,  0.01431213])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.0934438 ,  0.09209633,  0.01392102,  0.02571822, -0.01698898,
       -0.07395694, -0.2978461 ,  0.0197544 , -0.01342024])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.0934438 , -0.09209633, -0.01392102, -0.02571822,  0.01698898,
        0.07395694,  0.2978461 , -0.0197544 ,  0.01342024])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.16103614, -0.16673721, -0.11452671,  0.01725633, -0.00246032,
       -0.0063406 ,  0.08689295, -0.01926916,  0.04841559])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.16103614,  0.16673721,  0.11452671, -0.01725633,  0.00246032,
        0.0063406 , -0.08689295,  0.01926916, -0.04841559])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 2.49629963e-02, -1.76216249e-01, -3.26105680e-02, -3.63199352e-02,
       -3.68368535e-02,  3.03417007e-02, -1.35229489e-01, -1.68640695e-02,
       -1.30695623e-04])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-2.49629963e-02,  1.76216249e-01,  3.26105680e-02,  3.63199352e-02,
        3.68368535e-02, -3.03417007e-02,  1.35229489e-01,  1.68640695e-02,
        1.30695623e-04])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.07622787,  0.19287838, -0.01989478,  0.01811759,  0.00794114,
       -0.02604758,  0.18288793, -0.01786803, -0.01072485])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.07622787, -0.19287838,  0.01989478, -0.01811759, -0.00794114,
        0.02604758, -0.18288793,  0.01786803,  0.01072485])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.12330283,  0.08485859,  0.02377114,  0.01209157,  0.01143146,
       -0.01791268,  0.12341621, -0.00530905,  0.00947826])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.12330283, -0.08485859, -0.02377114, -0.01209157, -0.01143146,
        0.01791268, -0.12341621,  0.00530905, -0.00947826])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.13977137, -0.21320149, -0.02124587,  0.00373697, -0.02523968,
       -0.03263567, -0.06372222,  0.02195038, -0.03135574])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.13977137,  0.21320149,  0.02124587, -0.00373697,  0.02523968,
        0.03263567,  0.06372222, -0.02195038,  0.03135574])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.09262723, -0.21644694, -0.07618788,  0.00814692, -0.03752501,
        0.09497575,  0.06654848,  0.05060515, -0.04733378])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([ 0.09262723,  0.21644694,  0.07618788, -0.00814692,  0.03752501,
       -0.09497575, -0.06654848, -0.05060515,  0.04733378])
2025-07-16 01:58:39 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:39 - shap - INFO - phi = array([-0.1293025 , -0.11565411, -0.04183327,  0.01097385,  0.00753848,
        0.03797723, -0.2603902 ,  0.01830525, -0.01728829])
2025-07-16 01:58:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.1293025 ,  0.11565411,  0.04183327, -0.01097385, -0.00753848,
       -0.03797723,  0.2603902 , -0.01830525,  0.01728829])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.18675508, -0.20137452,  0.042639  ,  0.02412097, -0.03212482,
        0.02064894,  0.04591127,  0.01311194, -0.05277936])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.18675508,  0.20137452, -0.042639  , -0.02412097,  0.03212482,
       -0.02064894, -0.04591127, -0.01311194,  0.05277936])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.08348684,  0.09020277,  0.00449409,  0.005346  , -0.03063757,
       -0.06519642, -0.32129943,  0.02032733, -0.01991079])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.08348684, -0.09020277, -0.00449409, -0.005346  ,  0.03063757,
        0.06519642,  0.32129943, -0.02032733,  0.01991079])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.20220094, -0.05507195,  0.04185049,  0.01410046, -0.01415353,
        0.00629448,  0.09056963, -0.00826566, -0.00393145])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.20220094,  0.05507195, -0.04185049, -0.01410046,  0.01415353,
       -0.00629448, -0.09056963,  0.00826566,  0.00393145])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.12862206,  0.07727267,  0.0253713 , -0.01317665,  0.01724821,
        0.02502431,  0.07825905,  0.00351924,  0.01956586])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.12862206, -0.07727267, -0.0253713 ,  0.01317665, -0.01724821,
       -0.02502431, -0.07825905, -0.00351924, -0.01956586])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.1111144 , -0.11410029, -0.0279779 , -0.02404973,  0.02383205,
       -0.02464765, -0.23457801, -0.0117348 ,  0.01882803])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.1111144 ,  0.11410029,  0.0279779 ,  0.02404973, -0.02383205,
        0.02464765,  0.23457801,  0.0117348 , -0.01882803])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.10155972, -0.08083734,  0.06279702, -0.05913767, -0.03869394,
        0.01545731,  0.05749387, -0.02728677,  0.02101467])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.10155972,  0.08083734, -0.06279702,  0.05913767,  0.03869394,
       -0.01545731, -0.05749387,  0.02728677, -0.02101467])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.04343909,  0.00625451,  0.0741409 , -0.04158104, -0.02940688,
        0.01581017,  0.12068399, -0.01811841,  0.05416343])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.04343909, -0.00625451, -0.0741409 ,  0.04158104,  0.02940688,
       -0.01581017, -0.12068399,  0.01811841, -0.05416343])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.17764499,  0.08046976,  0.02264727, -0.01320161,  0.03527939,
        0.02925703,  0.02506266,  0.00727977, -0.0004582 ])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.17764499, -0.08046976, -0.02264727,  0.01320161, -0.03527939,
       -0.02925703, -0.02506266, -0.00727977,  0.0004582 ])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.12714748,  0.09605214,  0.02613248,  0.01185256,  0.00721284,
       -0.01875072,  0.12599413, -0.0090267 , -0.00152645])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.12714748, -0.09605214, -0.02613248, -0.01185256, -0.00721284,
        0.01875072, -0.12599413,  0.0090267 ,  0.00152645])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.12934986, -0.11948464, -0.03614019,  0.00305328, -0.01609708,
        0.03081439, -0.245161  ,  0.0175791 , -0.01884902])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.12934986,  0.11948464,  0.03614019, -0.00305328,  0.01609708,
       -0.03081439,  0.245161  , -0.0175791 ,  0.01884902])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.16069615,  0.07319645,  0.02194589, -0.01277151,  0.03192103,
        0.03001826,  0.02778722,  0.00682554,  0.02549887])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.16069615, -0.07319645, -0.02194589,  0.01277151, -0.03192103,
       -0.03001826, -0.02778722, -0.00682554, -0.02549887])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.09588362,  0.19733027, -0.0463318 ,  0.02471616, -0.00036397,
       -0.00902029,  0.1488907 , -0.01729243, -0.01517774])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.09588362, -0.19733027,  0.0463318 , -0.02471616,  0.00036397,
        0.00902029, -0.1488907 ,  0.01729243,  0.01517774])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.1738217 , -0.17628849,  0.06023672,  0.02506311,  0.00918295,
       -0.04976675,  0.09811562,  0.01486916, -0.04634827])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([-0.1738217 ,  0.17628849, -0.06023672, -0.02506311, -0.00918295,
        0.04976675, -0.09811562, -0.01486916,  0.04634827])
2025-07-16 01:58:40 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:40 - shap - INFO - phi = array([ 0.09512271,  0.01298156,  0.02171784,  0.00523746,  0.0598552 ,
       -0.0721671 , -0.29081522, -0.00260058,  0.00196691])
2025-07-16 01:58:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.09512271, -0.01298156, -0.02171784, -0.00523746, -0.0598552 ,
        0.0721671 ,  0.29081522,  0.00260058, -0.00196691])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.10112216, -0.18771353, -0.08777966,  0.02127328,  0.01010706,
        0.13144971,  0.1865985 ,  0.03792736, -0.04075727])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.10112216,  0.18771353,  0.08777966, -0.02127328, -0.01010706,
       -0.13144971, -0.1865985 , -0.03792736,  0.04075727])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.08885869,  0.13476507,  0.02518739,  0.00349627, -0.00796916,
        0.00769001,  0.1192604 , -0.00586931, -0.01540397])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.08885869, -0.13476507, -0.02518739, -0.00349627,  0.00796916,
       -0.00769001, -0.1192604 ,  0.00586931,  0.01540397])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.12537519, -0.12502989, -0.03487641,  0.00071938, -0.0174712 ,
       -0.00754331, -0.20284376, -0.01022618,  0.00961275])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.12537519,  0.12502989,  0.03487641, -0.00071938,  0.0174712 ,
        0.00754331,  0.20284376,  0.01022618, -0.00961275])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.14977407, -0.11207871,  0.0058687 , -0.02802158,  0.04467783,
       -0.00967211, -0.24801873, -0.00584839,  0.01995107])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.14977407,  0.11207871, -0.0058687 ,  0.02802158, -0.04467783,
        0.00967211,  0.24801873,  0.00584839, -0.01995107])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.17247539,  0.14348123, -0.00691148, -0.05248065, -0.03263003,
        0.02032885, -0.06794023, -0.019067  ,  0.00997385])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.17247539, -0.14348123,  0.00691148,  0.05248065,  0.03263003,
       -0.02032885,  0.06794023,  0.019067  , -0.00997385])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.080206  ,  0.17356569, -0.03522494,  0.01668956,  0.00515007,
       -0.00097201,  0.176459  , -0.00149149,  0.02784934])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.080206  , -0.17356569,  0.03522494, -0.01668956, -0.00515007,
        0.00097201, -0.176459  ,  0.00149149, -0.02784934])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.10669822,  0.03010527,  0.0204227 ,  0.01551452,  0.00353327,
       -0.03810537,  0.18061981, -0.02065752,  0.0204435 ])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.10669822, -0.03010527, -0.0204227 , -0.01551452, -0.00353327,
        0.03810537, -0.18061981,  0.02065752, -0.0204435 ])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.0742325 ,  0.16837881, -0.03220035,  0.01338062,  0.00440179,
       -0.0037777 ,  0.17373044,  0.00201632,  0.0404924 ])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.0742325 , -0.16837881,  0.03220035, -0.01338062, -0.00440179,
        0.0037777 , -0.17373044, -0.00201632, -0.0404924 ])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.09454901,  0.13311563,  0.03050008,  0.00574717, -0.01196912,
       -0.02158486,  0.12398045,  0.00088977, -0.00711053])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.09454901, -0.13311563, -0.03050008, -0.00574717,  0.01196912,
        0.02158486, -0.12398045, -0.00088977,  0.00711053])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.12882388, -0.08662744,  0.02912195, -0.02791598,  0.0323519 ,
       -0.02743563, -0.28378724, -0.01177715,  0.01255235])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.12882388,  0.08662744, -0.02912195,  0.02791598, -0.0323519 ,
        0.02743563,  0.28378724,  0.01177715, -0.01255235])
2025-07-16 01:58:41 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([ 0.13801087,  0.09117779,  0.02457707,  0.00853946, -0.00801715,
        0.01535325,  0.08865885, -0.00444008,  0.01129235])
2025-07-16 01:58:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:41 - shap - INFO - phi = array([-0.13801087, -0.09117779, -0.02457707, -0.00853946,  0.00801715,
       -0.01535325, -0.08865885,  0.00444008, -0.01129235])
2025-07-16 01:58:46 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-16 01:58:46 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 01:58:46 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 01:58:46 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 01:58:46 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 01:58:51 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 01:58:52 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-16 01:58:52 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
2025-07-16 01:58:52 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-0.00104733,  0.        ,  0.01402183,  0.        ,  0.00854089,
       -0.00544058,  0.26010255,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([ 0.00104733,  0.        , -0.01402183,  0.        , -0.00854089,
        0.00544058, -0.26010255,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-0.00026853,  0.        , -0.00141656,  0.        ,  0.00972365,
        0.02155447,  0.26726658,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([ 0.00026853,  0.        ,  0.00141656,  0.        , -0.00972365,
       -0.02155447, -0.26726658,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([ 0.00123305,  0.        , -0.00120914,  0.        ,  0.00324986,
       -0.00635239,  0.25755906,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-0.00123305,  0.        ,  0.00120914,  0.        , -0.00324986,
        0.00635239, -0.25755906,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([ 0.00121637,  0.        , -0.00118111,  0.        ,  0.00795681,
       -0.00627109,  0.25869552,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-0.00121637,  0.        ,  0.00118111,  0.        , -0.00795681,
        0.00627109, -0.25869552,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-0.00113533,  0.        , -0.00207941,  0.        , -0.0023075 ,
       -0.00673236,  0.25673902,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([ 0.00113533,  0.        ,  0.00207941,  0.        ,  0.0023075 ,
        0.00673236, -0.25673902,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-0.00111057,  0.        ,  0.00526711,  0.        , -0.00225324,
       -0.00611295,  0.25794551,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([ 0.00111057,  0.        , -0.00526711,  0.        ,  0.00225324,
        0.00611295, -0.25794551,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([ 0.00045545,  0.        , -0.00061054,  0.        ,  0.00891771,
        0.02020558,  0.2678914 ,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-0.00045545,  0.        ,  0.00061054,  0.        , -0.00891771,
       -0.02020558, -0.2678914 ,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([ 0.00045545,  0.        , -0.00061054,  0.        ,  0.00891771,
        0.02020558,  0.2678914 ,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-0.00045545,  0.        ,  0.00061054,  0.        , -0.00891771,
       -0.02020558, -0.2678914 ,  0.        ,  0.        ])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([ 4.65940471e-04,  0.00000000e+00, -3.06542854e-04,  0.00000000e+00,
       -3.01946534e-03, -2.28086151e-03, -3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-4.65940471e-04,  0.00000000e+00,  3.06542854e-04,  0.00000000e+00,
        3.01946534e-03,  2.28086151e-03,  3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-2.58188396e-04,  0.00000000e+00, -3.08606592e-04,  0.00000000e+00,
       -3.02777258e-03, -2.54238925e-03, -3.90072676e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([2.58188396e-04, 0.00000000e+00, 3.08606592e-04, 0.00000000e+00,
       3.02777258e-03, 2.54238925e-03, 3.90072676e-01, 0.00000000e+00,
       0.00000000e+00])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([-2.58188396e-04,  0.00000000e+00, -3.08606592e-04,  0.00000000e+00,
       -3.02777258e-03, -2.54238925e-03, -3.90072676e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:52 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:52 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:52 - shap - INFO - phi = array([2.58188396e-04, 0.00000000e+00, 3.08606592e-04, 0.00000000e+00,
       3.02777258e-03, 2.54238925e-03, 3.90072676e-01, 0.00000000e+00,
       0.00000000e+00])
2025-07-16 01:58:52 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00046503,  0.        , -0.00076888,  0.        , -0.00254452,
       -0.00227789, -0.39107637,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00046503,  0.        ,  0.00076888,  0.        ,  0.00254452,
        0.00227789,  0.39107637,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00113533,  0.        , -0.00207941,  0.        , -0.0023075 ,
       -0.00673236,  0.25673902,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00113533,  0.        ,  0.00207941,  0.        ,  0.0023075 ,
        0.00673236, -0.25673902,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00125132,  0.        , -0.00123056,  0.        , -0.00315901,
       -0.00644189,  0.25744142,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00125132,  0.        ,  0.00123056,  0.        ,  0.00315901,
        0.00644189, -0.25744142,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00026773,  0.        , -0.00061333,  0.        ,  0.00893938,
        0.02151828,  0.267283  ,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00026773,  0.        ,  0.00061333,  0.        , -0.00893938,
       -0.02151828, -0.267283  ,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-2.58188396e-04,  0.00000000e+00, -3.08606592e-04,  0.00000000e+00,
       -3.02777258e-03, -2.54238925e-03, -3.90072676e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([2.58188396e-04, 0.00000000e+00, 3.08606592e-04, 0.00000000e+00,
       3.02777258e-03, 2.54238925e-03, 3.90072676e-01, 0.00000000e+00,
       0.00000000e+00])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00114208,  0.        , -0.00324443,  0.        , -0.00446067,
       -0.00676875,  0.2566785 ,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00114208,  0.        ,  0.00324443,  0.        ,  0.00446067,
        0.00676875, -0.2566785 ,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00046871,  0.        , -0.00334999,  0.        , -0.00301776,
       -0.00230649, -0.39095666,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00046871,  0.        ,  0.00334999,  0.        ,  0.00301776,
        0.00230649,  0.39095666,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 4.65940471e-04,  0.00000000e+00, -3.06542854e-04,  0.00000000e+00,
       -3.01946534e-03, -2.28086151e-03, -3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-4.65940471e-04,  0.00000000e+00,  3.06542854e-04,  0.00000000e+00,
        3.01946534e-03,  2.28086151e-03,  3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00114208,  0.        , -0.00324443,  0.        , -0.00446067,
       -0.00676875,  0.2566785 ,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00114208,  0.        ,  0.00324443,  0.        ,  0.00446067,
        0.00676875, -0.2566785 ,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-2.58188396e-04,  0.00000000e+00, -3.08606592e-04,  0.00000000e+00,
       -3.02777258e-03, -2.54238925e-03, -3.90072676e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([2.58188396e-04, 0.00000000e+00, 3.08606592e-04, 0.00000000e+00,
       3.02777258e-03, 2.54238925e-03, 3.90072676e-01, 0.00000000e+00,
       0.00000000e+00])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00046871,  0.        , -0.00334999,  0.        , -0.00301776,
       -0.00230649, -0.39095666,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00046871,  0.        ,  0.00334999,  0.        ,  0.00301776,
        0.00230649,  0.39095666,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00108063,  0.        ,  0.01447113,  0.        , -0.00214525,
       -0.00558349,  0.25903051,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00108063,  0.        , -0.01447113,  0.        ,  0.00214525,
        0.00558349, -0.25903051,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 0.00046871,  0.        , -0.00334999,  0.        , -0.00301776,
       -0.00230649, -0.39095666,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-0.00046871,  0.        ,  0.00334999,  0.        ,  0.00301776,
        0.00230649,  0.39095666,  0.        ,  0.        ])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([ 4.65940471e-04,  0.00000000e+00, -3.06542854e-04,  0.00000000e+00,
       -3.01946534e-03, -2.28086151e-03, -3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:53 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:53 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:53 - shap - INFO - phi = array([-4.65940471e-04,  0.00000000e+00,  3.06542854e-04,  0.00000000e+00,
        3.01946534e-03,  2.28086151e-03,  3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:53 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 0.00125132,  0.        , -0.00123056,  0.        , -0.00315901,
       -0.00644189,  0.25744142,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-0.00125132,  0.        ,  0.00123056,  0.        ,  0.00315901,
        0.00644189, -0.25744142,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-0.00026773,  0.        , -0.00061333,  0.        ,  0.00893938,
        0.02151828,  0.267283  ,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 0.00026773,  0.        ,  0.00061333,  0.        , -0.00893938,
       -0.02151828, -0.267283  ,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-0.00108724,  0.        ,  0.01341287,  0.        , -0.00419166,
       -0.00561045,  0.25902368,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 0.00108724,  0.        , -0.01341287,  0.        ,  0.00419166,
        0.00561045, -0.25902368,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 4.65940471e-04,  0.00000000e+00, -3.06542854e-04,  0.00000000e+00,
       -3.01946534e-03, -2.28086151e-03, -3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-4.65940471e-04,  0.00000000e+00,  3.06542854e-04,  0.00000000e+00,
        3.01946534e-03,  2.28086151e-03,  3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 4.65940471e-04,  0.00000000e+00, -3.06542854e-04,  0.00000000e+00,
       -3.01946534e-03, -2.28086151e-03, -3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-4.65940471e-04,  0.00000000e+00,  3.06542854e-04,  0.00000000e+00,
        3.01946534e-03,  2.28086151e-03,  3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 4.65940471e-04,  0.00000000e+00, -3.06542854e-04,  0.00000000e+00,
       -3.01946534e-03, -2.28086151e-03, -3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-4.65940471e-04,  0.00000000e+00,  3.06542854e-04,  0.00000000e+00,
        3.01946534e-03,  2.28086151e-03,  3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-0.00026773,  0.        , -0.00061333,  0.        ,  0.00893938,
        0.02151828,  0.267283  ,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 0.00026773,  0.        ,  0.00061333,  0.        , -0.00893938,
       -0.02151828, -0.267283  ,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-0.00113458,  0.        , -0.00074353,  0.        , -0.00366749,
       -0.00673686,  0.25676689,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 0.00113458,  0.        ,  0.00074353,  0.        ,  0.00366749,
        0.00673686, -0.25676689,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 0.00121787,  0.        , -0.002018  ,  0.        ,  0.00881865,
       -0.00627109,  0.25866906,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-0.00121787,  0.        ,  0.002018  ,  0.        , -0.00881865,
        0.00627109, -0.25866906,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([-0.00114208,  0.        , -0.00324443,  0.        , -0.00446067,
       -0.00676875,  0.2566785 ,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:54 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:54 - shap - INFO - phi = array([ 0.00114208,  0.        ,  0.00324443,  0.        ,  0.00446067,
        0.00676875, -0.2566785 ,  0.        ,  0.        ])
2025-07-16 01:58:54 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([ 0.00045631,  0.        , -0.0014779 ,  0.        ,  0.00834368,
        0.01935152,  0.26751887,  0.        ,  0.        ])
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([-0.00045631,  0.        ,  0.0014779 ,  0.        , -0.00834368,
       -0.01935152, -0.26751887,  0.        ,  0.        ])
2025-07-16 01:58:55 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([ 4.65940471e-04,  0.00000000e+00, -3.06542854e-04,  0.00000000e+00,
       -3.01946534e-03, -2.28086151e-03, -3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([-4.65940471e-04,  0.00000000e+00,  3.06542854e-04,  0.00000000e+00,
        3.01946534e-03,  2.28086151e-03,  3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:55 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([ 0.0012605 ,  0.        , -0.00446324,  0.        , -0.00318234,
       -0.0064877 ,  0.25735722,  0.        ,  0.        ])
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([-0.0012605 ,  0.        ,  0.00446324,  0.        ,  0.00318234,
        0.0064877 , -0.25735722,  0.        ,  0.        ])
2025-07-16 01:58:55 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([-0.00113458,  0.        , -0.00074353,  0.        , -0.00366749,
       -0.00673686,  0.25676689,  0.        ,  0.        ])
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([ 0.00113458,  0.        ,  0.00074353,  0.        ,  0.00366749,
        0.00673686, -0.25676689,  0.        ,  0.        ])
2025-07-16 01:58:55 - shap - INFO - num_full_subsets = 4
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([ 4.65940471e-04,  0.00000000e+00, -3.06542854e-04,  0.00000000e+00,
       -3.01946534e-03, -2.28086151e-03, -3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:58:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 01:58:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 01:58:55 - shap - INFO - phi = array([-4.65940471e-04,  0.00000000e+00,  3.06542854e-04,  0.00000000e+00,
        3.01946534e-03,  2.28086151e-03,  3.91061687e-01,  0.00000000e+00,
        0.00000000e+00])
2025-07-16 01:59:00 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-16 02:03:26 - model_ensemble - INFO - ============================================================
2025-07-16 02:03:26 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 02:03:26 - model_ensemble - INFO - ============================================================
2025-07-16 02:03:26 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost', 'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet']
2025-07-16 02:03:26 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 02:03:26 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 02:03:26 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 02:03:26 - model_training - INFO - 准确率: 0.8250
2025-07-16 02:03:26 - model_training - INFO - AUC: 0.9028
2025-07-16 02:03:26 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:26 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-07-16 02:03:26 - model_training - INFO - 
分类报告:
2025-07-16 02:03:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-07-16 02:03:26 - model_training - INFO - 训练时间: 0.00 秒
2025-07-16 02:03:26 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 02:03:26 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-07-16 02:03:26 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 02:03:26 - model_training - INFO - 模型名称: Random Forest
2025-07-16 02:03:26 - model_training - INFO - 准确率: 0.8250
2025-07-16 02:03:26 - model_training - INFO - AUC: 0.9015
2025-07-16 02:03:26 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:26 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-16 02:03:26 - model_training - INFO - 
分类报告:
2025-07-16 02:03:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 02:03:26 - model_training - INFO - 训练时间: 0.09 秒
2025-07-16 02:03:26 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 02:03:26 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-16 02:03:26 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-16 02:03:26 - model_training - INFO - 模型名称: XGBoost
2025-07-16 02:03:26 - model_training - INFO - 准确率: 0.7750
2025-07-16 02:03:26 - model_training - INFO - AUC: 0.8568
2025-07-16 02:03:26 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:26 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-16 02:03:26 - model_training - INFO - 
分类报告:
2025-07-16 02:03:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-16 02:03:26 - model_training - INFO - 训练时间: 0.05 秒
2025-07-16 02:03:26 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 02:03:26 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-16 02:03:26 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-16 02:03:26 - model_training - INFO - 模型名称: LightGBM
2025-07-16 02:03:26 - model_training - INFO - 准确率: 0.7500
2025-07-16 02:03:26 - model_training - INFO - AUC: 0.8568
2025-07-16 02:03:26 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:26 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-07-16 02:03:26 - model_training - INFO - 
分类报告:
2025-07-16 02:03:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-07-16 02:03:26 - model_training - INFO - 训练时间: 0.22 秒
2025-07-16 02:03:26 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-16 02:03:26 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-16 02:03:26 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: CatBoost
2025-07-16 02:03:27 - model_training - INFO - 模型名称: CatBoost
2025-07-16 02:03:27 - model_training - INFO - 准确率: 0.8750
2025-07-16 02:03:27 - model_training - INFO - AUC: 0.9054
2025-07-16 02:03:27 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:27 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-07-16 02:03:27 - model_training - INFO - 
分类报告:
2025-07-16 02:03:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 02:03:27 - model_training - INFO - 训练时间: 1.21 秒
2025-07-16 02:03:27 - model_training - INFO - 模型 CatBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-07-16 02:03:27 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_feature_names.joblib
2025-07-16 02:03:27 - model_ensemble - INFO -   CatBoost 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-16 02:03:27 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 02:03:27 - model_training - INFO - 准确率: 0.8250
2025-07-16 02:03:27 - model_training - INFO - AUC: 0.9028
2025-07-16 02:03:27 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:27 - model_training - INFO - 
[[19  4]
 [ 3 14]]
2025-07-16 02:03:27 - model_training - INFO - 
分类报告:
2025-07-16 02:03:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.83      0.84        23
           1       0.78      0.82      0.80        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.83      0.82      0.83        40

2025-07-16 02:03:27 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 02:03:27 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 02:03:27 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-16 02:03:27 - model_ensemble - INFO -   Logistic 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-16 02:03:27 - model_training - INFO - 模型名称: SVM
2025-07-16 02:03:27 - model_training - INFO - 准确率: 0.8750
2025-07-16 02:03:27 - model_training - INFO - AUC: 0.9309
2025-07-16 02:03:27 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:27 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-07-16 02:03:27 - model_training - INFO - 
分类报告:
2025-07-16 02:03:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 02:03:27 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 02:03:27 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 02:03:27 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-16 02:03:27 - model_ensemble - INFO -   SVM 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 02:03:27 - model_training - INFO - 模型名称: KNN
2025-07-16 02:03:27 - model_training - INFO - 准确率: 0.9000
2025-07-16 02:03:27 - model_training - INFO - AUC: 0.8913
2025-07-16 02:03:27 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:27 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-07-16 02:03:27 - model_training - INFO - 
分类报告:
2025-07-16 02:03:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-16 02:03:27 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 02:03:27 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-16 02:03:27 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-16 02:03:27 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: NaiveBayes
2025-07-16 02:03:27 - model_training - INFO - 模型名称: Naive Bayes
2025-07-16 02:03:27 - model_training - INFO - 准确率: 0.8750
2025-07-16 02:03:27 - model_training - INFO - AUC: 0.9079
2025-07-16 02:03:27 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:27 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-16 02:03:27 - model_training - INFO - 
分类报告:
2025-07-16 02:03:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 02:03:27 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 02:03:27 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-07-16 02:03:27 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-07-16 02:03:27 - model_ensemble - INFO -   NaiveBayes 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-07-16 02:03:28 - model_training - INFO - 模型名称: Neural Network
2025-07-16 02:03:28 - model_training - INFO - 准确率: 0.8500
2025-07-16 02:03:28 - model_training - INFO - AUC: 0.9156
2025-07-16 02:03:28 - model_training - INFO - 混淆矩阵:
2025-07-16 02:03:28 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-07-16 02:03:28 - model_training - INFO - 
分类报告:
2025-07-16 02:03:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-07-16 02:03:28 - model_training - INFO - 训练时间: 0.31 秒
2025-07-16 02:03:28 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-07-16 02:03:28 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-07-16 02:03:28 - model_ensemble - INFO -   NeuralNet 训练完成
2025-07-16 02:03:28 - model_ensemble - INFO - 成功训练了 10 个基础模型
2025-07-16 02:03:28 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 02:03:28 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 02:03:37 - model_ensemble - INFO -   stacking - 准确率: 0.9000, F1: 0.8990
2025-07-16 02:03:37 - model_ensemble - INFO - ============================================================
2025-07-16 02:03:37 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 02:03:37 - model_ensemble - INFO - ============================================================
2025-07-16 02:03:37 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 02:03:37 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-16 02:03:37 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-16 02:03:37 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 02:03:37 - model_ensemble - INFO -   stacking        - 准确率: 0.9000, 精确率: 0.9027, 召回率: 0.9000, F1: 0.8990, AUC: 0.9182
2025-07-16 02:03:37 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 02:03:37 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_020337.joblib
2025-07-16 02:03:37 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-16 02:03:37 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 02:03:37 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 02:03:37 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 02:03:37 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-16 02:03:37 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-16 02:03:37 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:37 - shap - INFO - phi = array([ 0.05288385,  0.14270169,  0.06220675,  0.02247582, -0.00827368,
       -0.00660236,  0.15056514, -0.00467555, -0.02895728])
2025-07-16 02:03:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:37 - shap - INFO - phi = array([-0.05288385, -0.14270169, -0.06220675, -0.02247582,  0.00827368,
        0.00660236, -0.15056514,  0.00467555,  0.02895728])
2025-07-16 02:03:37 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([ 0.04430531,  0.08865602,  0.06197066,  0.04116585, -0.00823223,
       -0.0249968 ,  0.18974058, -0.02156886,  0.0173416 ])
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([-0.04430531, -0.08865602, -0.06197066, -0.04116585,  0.00823223,
        0.0249968 , -0.18974058,  0.02156886, -0.0173416 ])
2025-07-16 02:03:38 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([-0.10189303, -0.13838566, -0.09834686, -0.04598741, -0.01042652,
        0.01495822, -0.12453974, -0.02679522,  0.00725777])
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([ 0.10189303,  0.13838566,  0.09834686,  0.04598741,  0.01042652,
       -0.01495822,  0.12453974,  0.02679522, -0.00725777])
2025-07-16 02:03:38 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([-0.10242509, -0.06187291, -0.17111332,  0.03308049, -0.00781097,
       -0.02894977, -0.18594281,  0.00220717, -0.00660231])
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([ 0.10242509,  0.06187291,  0.17111332, -0.03308049,  0.00781097,
        0.02894977,  0.18594281, -0.00220717,  0.00660231])
2025-07-16 02:03:38 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([ 0.03872172,  0.13080928,  0.07208169,  0.02011449, -0.00220069,
       -0.009936  ,  0.06910416,  0.11097795, -0.04793885])
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([-0.03872172, -0.13080928, -0.07208169, -0.02011449,  0.00220069,
        0.009936  , -0.06910416, -0.11097795,  0.04793885])
2025-07-16 02:03:38 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([ 0.05342514,  0.12052968,  0.07719258, -0.03187975,  0.03480533,
        0.05729001,  0.05453039, -0.02910493,  0.04882634])
2025-07-16 02:03:38 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:38 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:38 - shap - INFO - phi = array([-0.05342514, -0.12052968, -0.07719258,  0.03187975, -0.03480533,
       -0.05729001, -0.05453039,  0.02910493, -0.04882634])
2025-07-16 02:03:38 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:39 - shap - INFO - phi = array([ 0.04690225,  0.12588456,  0.09280731,  0.01844691, -0.00653496,
       -0.01902768,  0.15764254, -0.00620875, -0.02217079])
2025-07-16 02:03:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:39 - shap - INFO - phi = array([-0.04690225, -0.12588456, -0.09280731, -0.01844691,  0.00653496,
        0.01902768, -0.15764254,  0.00620875,  0.02217079])
2025-07-16 02:03:39 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:39 - shap - INFO - phi = array([ 0.06877903,  0.14500381,  0.08849044,  0.04403956, -0.01410085,
       -0.05766884, -0.22800906,  0.13118167, -0.04275355])
2025-07-16 02:03:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:39 - shap - INFO - phi = array([-0.06877903, -0.14500381, -0.08849044, -0.04403956,  0.01410085,
        0.05766884,  0.22800906, -0.13118167,  0.04275355])
2025-07-16 02:03:39 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:39 - shap - INFO - phi = array([-0.1151257 , -0.1249327 , -0.23407291,  0.07354681, -0.04235055,
       -0.00768827,  0.06596679, -0.04144655,  0.06300122])
2025-07-16 02:03:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:39 - shap - INFO - phi = array([ 0.1151257 ,  0.1249327 ,  0.23407291, -0.07354681,  0.04235055,
        0.00768827, -0.06596679,  0.04144655, -0.06300122])
2025-07-16 02:03:39 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:39 - shap - INFO - phi = array([ 0.01000505, -0.13197151, -0.04112337, -0.06528597, -0.01554075,
        0.02436693, -0.21025388, -0.0328306 , -0.00220516])
2025-07-16 02:03:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:39 - shap - INFO - phi = array([-0.01000505,  0.13197151,  0.04112337,  0.06528597,  0.01554075,
       -0.02436693,  0.21025388,  0.0328306 ,  0.00220516])
2025-07-16 02:03:39 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:39 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:39 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([-0.02469092,  0.22464209,  0.00646848,  0.04519295, -0.01007113,
       -0.0320169 ,  0.17556273, -0.01481677, -0.00963571])
2025-07-16 02:03:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([ 0.02469092, -0.22464209, -0.00646848, -0.04519295,  0.01007113,
        0.0320169 , -0.17556273,  0.01481677,  0.00963571])
2025-07-16 02:03:40 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([ 0.05563166,  0.08644967,  0.06718799,  0.04018953, -0.00882294,
       -0.02108933,  0.17310752, -0.01749083,  0.01363972])
2025-07-16 02:03:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([-0.05563166, -0.08644967, -0.06718799, -0.04018953,  0.00882294,
        0.02108933, -0.17310752,  0.01749083, -0.01363972])
2025-07-16 02:03:40 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([-0.05920846, -0.30360537, -0.02613842, -0.00674254, -0.01403378,
       -0.04210108, -0.06497695,  0.04448058, -0.0569406 ])
2025-07-16 02:03:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([ 0.05920846,  0.30360537,  0.02613842,  0.00674254,  0.01403378,
        0.04210108,  0.06497695, -0.04448058,  0.0569406 ])
2025-07-16 02:03:40 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([-0.05057351, -0.3144843 , -0.08114805, -0.00728444, -0.01167051,
        0.00236928, -0.00824366,  0.05099683, -0.06631037])
2025-07-16 02:03:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([ 0.05057351,  0.3144843 ,  0.08114805,  0.00728444,  0.01167051,
       -0.00236928,  0.00824366, -0.05099683,  0.06631037])
2025-07-16 02:03:40 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([-0.03249441, -0.15165681, -0.12804891,  0.02578602, -0.00616719,
        0.00460288, -0.18798361,  0.02215505, -0.02595814])
2025-07-16 02:03:40 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:40 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:40 - shap - INFO - phi = array([ 0.03249441,  0.15165681,  0.12804891, -0.02578602,  0.00616719,
       -0.00460288,  0.18798361, -0.02215505,  0.02595814])
2025-07-16 02:03:40 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([ 0.09058059, -0.20990921,  0.12076405,  0.00486004, -0.02118692,
        0.01109885,  0.04060521,  0.00261765, -0.11003169])
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([-0.09058059,  0.20990921, -0.12076405, -0.00486004,  0.02118692,
       -0.01109885, -0.04060521, -0.00261765,  0.11003169])
2025-07-16 02:03:41 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([ 0.05844015,  0.11892615,  0.05680341, -0.01974253, -0.00800294,
       -0.05769882, -0.30679468,  0.11916489, -0.0295212 ])
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([-0.05844015, -0.11892615, -0.05680341,  0.01974253,  0.00800294,
        0.05769882,  0.30679468, -0.11916489,  0.0295212 ])
2025-07-16 02:03:41 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([ 0.09813687, -0.00580692,  0.13475245, -0.00492897, -0.00503627,
       -0.00153012,  0.11452009,  0.00256076, -0.00568131])
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([-0.09813687,  0.00580692, -0.13475245,  0.00492897,  0.00503627,
        0.00153012, -0.11452009, -0.00256076,  0.00568131])
2025-07-16 02:03:41 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([ 0.06797213,  0.08141393,  0.085883  , -0.02736596,  0.01989051,
        0.07900798,  0.06403153, -0.02490763,  0.04333341])
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([-0.06797213, -0.08141393, -0.085883  ,  0.02736596, -0.01989051,
       -0.07900798, -0.06403153,  0.02490763, -0.04333341])
2025-07-16 02:03:41 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([-0.05237719, -0.15129691, -0.04156697, -0.03875962,  0.03289379,
       -0.05864888, -0.24416632, -0.02210947,  0.04557601])
2025-07-16 02:03:41 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:41 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:41 - shap - INFO - phi = array([ 0.05237719,  0.15129691,  0.04156697,  0.03875962, -0.03289379,
        0.05864888,  0.24416632,  0.02210947, -0.04557601])
2025-07-16 02:03:41 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:42 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:42 - shap - INFO - phi = array([-0.02224755, -0.00906651,  0.08743449, -0.05890194,  0.02007568,
        0.07916822,  0.05579518, -0.03284382,  0.05315517])
2025-07-16 02:03:42 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:42 - shap - INFO - phi = array([ 0.02224755,  0.00906651, -0.08743449,  0.05890194, -0.02007568,
       -0.07916822, -0.05579518,  0.03284382, -0.05315517])
2025-07-16 02:03:42 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:42 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:42 - shap - INFO - phi = array([-0.0020812 ,  0.03476664,  0.09269953, -0.04787521,  0.00751697,
        0.07597851,  0.11535988, -0.0382905 ,  0.06612573])
2025-07-16 02:03:42 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:42 - shap - INFO - phi = array([ 0.0020812 , -0.03476664, -0.09269953,  0.04787521, -0.00751697,
       -0.07597851, -0.11535988,  0.0382905 , -0.06612573])
2025-07-16 02:03:42 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:42 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:42 - shap - INFO - phi = array([ 0.08560994,  0.12800354,  0.07635166, -0.02632929,  0.04114813,
        0.09855826,  0.01533009, -0.03152815,  0.00238791])
2025-07-16 02:03:42 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:42 - shap - INFO - phi = array([-0.08560994, -0.12800354, -0.07635166,  0.02632929, -0.04114813,
       -0.09855826, -0.01533009,  0.03152815, -0.00238791])
2025-07-16 02:03:42 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:42 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:42 - shap - INFO - phi = array([ 0.05751606,  0.10062038,  0.07599795,  0.04007081, -0.00903358,
       -0.0206919 ,  0.15994792, -0.01489912,  0.00087447])
2025-07-16 02:03:42 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:42 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:42 - shap - INFO - phi = array([-0.05751606, -0.10062038, -0.07599795, -0.04007081,  0.00903358,
        0.0206919 , -0.15994792,  0.01489912, -0.00087447])
2025-07-16 02:03:42 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([-0.06387483, -0.1522426 , -0.11378876, -0.00535017, -0.00665915,
       -0.01082448, -0.18450978,  0.02903805, -0.02963433])
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([ 0.06387483,  0.1522426 ,  0.11378876,  0.00535017,  0.00665915,
        0.01082448,  0.18450978, -0.02903805,  0.02963433])
2025-07-16 02:03:43 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([ 0.07867834,  0.09637996,  0.06099747, -0.02716477,  0.04118997,
        0.10141781,  0.01432995, -0.02710158,  0.05123475])
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([-0.07867834, -0.09637996, -0.06099747,  0.02716477, -0.04118997,
       -0.10141781, -0.01432995,  0.02710158, -0.05123475])
2025-07-16 02:03:43 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([-0.13335292,  0.09709573, -0.29729108,  0.03561647, -0.01939095,
       -0.0251353 ,  0.05138956, -0.02384587, -0.03532364])
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([ 0.13335292, -0.09709573,  0.29729108, -0.03561647,  0.01939095,
        0.0251353 , -0.05138956,  0.02384587,  0.03532364])
2025-07-16 02:03:43 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([ 0.08687784, -0.24783261,  0.13519197,  0.03192047, -0.00456619,
       -0.03908819,  0.10100804,  0.08596511, -0.10329179])
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([-0.08687784,  0.24783261, -0.13519197, -0.03192047,  0.00456619,
        0.03908819, -0.10100804, -0.08596511,  0.10329179])
2025-07-16 02:03:43 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([ 0.0282344 , -0.02538795,  0.03531886, -0.06105652,  0.03272447,
       -0.08441779, -0.35788932, -0.00916757,  0.        ])
2025-07-16 02:03:43 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:43 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:43 - shap - INFO - phi = array([-0.0282344 ,  0.02538795, -0.03531886,  0.06105652, -0.03272447,
        0.08441779,  0.35788932,  0.00916757,  0.        ])
2025-07-16 02:03:43 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([-0.09215063, -0.26193193, -0.2060933 ,  0.03161323, -0.01078885,
        0.03024248,  0.10561471,  0.0179772 , -0.04152218])
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([ 0.09215063,  0.26193193,  0.2060933 , -0.03161323,  0.01078885,
       -0.03024248, -0.10561471, -0.0179772 ,  0.04152218])
2025-07-16 02:03:44 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([ 0.04578103,  0.18427804,  0.05496035,  0.00899235, -0.01034461,
        0.0042446 ,  0.12343724, -0.00559113, -0.0208456 ])
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([-0.04578103, -0.18427804, -0.05496035, -0.00899235,  0.01034461,
       -0.0042446 , -0.12343724,  0.00559113,  0.0208456 ])
2025-07-16 02:03:44 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([-0.05924789, -0.16120035, -0.07415904, -0.0188551 ,  0.00216871,
       -0.02392998, -0.2036683 , -0.01212532,  0.01392747])
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([ 0.05924789,  0.16120035,  0.07415904,  0.0188551 , -0.00216871,
        0.02392998,  0.2036683 ,  0.01212532, -0.01392747])
2025-07-16 02:03:44 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([-0.05926605, -0.13124694, -0.00585655, -0.049454  ,  0.04910247,
       -0.02881809, -0.31091908, -0.02767615,  0.04249459])
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([ 0.05926605,  0.13124694,  0.00585655,  0.049454  , -0.04910247,
        0.02881809,  0.31091908,  0.02767615, -0.04249459])
2025-07-16 02:03:44 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([-0.05757231,  0.18362507,  0.01620765, -0.06619752,  0.00710667,
        0.11257797, -0.03327476, -0.04115165,  0.02679649])
2025-07-16 02:03:44 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:44 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:44 - shap - INFO - phi = array([ 0.05757231, -0.18362507, -0.01620765,  0.06619752, -0.00710667,
       -0.11257797,  0.03327476,  0.04115165, -0.02679649])
2025-07-16 02:03:44 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:45 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:45 - shap - INFO - phi = array([-0.05803885,  0.19178509, -0.11053343,  0.04837635, -0.01334512,
       -0.00045262,  0.25178106, -0.02230792,  0.04941717])
2025-07-16 02:03:45 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:45 - shap - INFO - phi = array([ 0.05803885, -0.19178509,  0.11053343, -0.04837635,  0.01334512,
        0.00045262, -0.25178106,  0.02230792, -0.04941717])
2025-07-16 02:03:45 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:45 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:45 - shap - INFO - phi = array([-0.03160821,  0.05188272,  0.04769737,  0.03784971, -0.01762723,
       -0.03564982,  0.26251996, -0.02698215,  0.02232993])
2025-07-16 02:03:45 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:45 - shap - INFO - phi = array([ 0.03160821, -0.05188272, -0.04769737, -0.03784971,  0.01762723,
        0.03564982, -0.26251996,  0.02698215, -0.02232993])
2025-07-16 02:03:45 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:45 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:45 - shap - INFO - phi = array([-0.01813258,  0.1233589 , -0.19380809,  0.07351266, -0.02153567,
       -0.00557268,  0.21435376, -0.0340232 ,  0.08456029])
2025-07-16 02:03:45 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:45 - shap - INFO - phi = array([ 0.01813258, -0.1233589 ,  0.19380809, -0.07351266,  0.02153567,
        0.00557268, -0.21435376,  0.0340232 , -0.08456029])
2025-07-16 02:03:45 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:45 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:45 - shap - INFO - phi = array([ 0.05604368,  0.11445961,  0.10407816, -0.00787182, -0.00652168,
       -0.02236597,  0.13853434,  0.00511229, -0.00923658])
2025-07-16 02:03:45 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:45 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:45 - shap - INFO - phi = array([-0.05604368, -0.11445961, -0.10407816,  0.00787182,  0.00652168,
        0.02236597, -0.13853434, -0.00511229,  0.00923658])
2025-07-16 02:03:45 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:46 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:46 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:46 - shap - INFO - phi = array([-0.05474576, -0.09449122,  0.02328904, -0.05302075,  0.03749863,
       -0.04853916, -0.33669687, -0.01789068,  0.02197746])
2025-07-16 02:03:46 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:46 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:46 - shap - INFO - phi = array([ 0.05474576,  0.09449122, -0.02328904,  0.05302075, -0.03749863,
        0.04853916,  0.33669687,  0.01789068, -0.02197746])
2025-07-16 02:03:46 - shap - INFO - num_full_subsets = 4
2025-07-16 02:03:46 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:46 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:46 - shap - INFO - phi = array([ 0.0665902 ,  0.10605803,  0.0862639 , -0.00678808, -0.00583724,
        0.017567  ,  0.12309862, -0.01704705,  0.02023828])
2025-07-16 02:03:46 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:03:46 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:03:46 - shap - INFO - phi = array([-0.0665902 , -0.10605803, -0.0862639 ,  0.00678808,  0.00583724,
       -0.017567  , -0.12309862,  0.01704705, -0.02023828])
2025-07-16 02:03:50 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-16 02:03:50 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 02:03:50 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 02:03:50 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 02:03:50 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 02:03:59 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-16 02:03:59 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-16 02:03:59 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
2025-07-16 02:03:59 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-16 02:04:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:00 - shap - INFO - phi = array([-0.00056085,  0.02621219,  0.00813868,  0.00075356,  0.00470306,
       -0.01006956,  0.1975122 , -0.01370295, -0.0018124 ])
2025-07-16 02:04:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:00 - shap - INFO - phi = array([ 0.00056085, -0.02621219, -0.00813868, -0.00075356, -0.00470306,
        0.01006956, -0.1975122 ,  0.01370295,  0.0018124 ])
2025-07-16 02:04:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:00 - shap - INFO - phi = array([-0.00026106,  0.00076147,  0.        ,  0.00032584,  0.00578973,
       -0.00468009,  0.18874372,  0.03212727,  0.00357439])
2025-07-16 02:04:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:00 - shap - INFO - phi = array([ 0.00026106, -0.00076147,  0.        , -0.00032584, -0.00578973,
        0.00468009, -0.18874372, -0.03212727, -0.00357439])
2025-07-16 02:04:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:00 - shap - INFO - phi = array([ 0.00067279, -0.02334691, -0.00131116, -0.00027499,  0.00231902,
       -0.0367722 ,  0.20312368, -0.00256028, -0.00700703])
2025-07-16 02:04:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:00 - shap - INFO - phi = array([-0.00067279,  0.02334691,  0.00131116,  0.00027499, -0.00231902,
        0.0367722 , -0.20312368,  0.00256028,  0.00700703])
2025-07-16 02:04:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:00 - shap - INFO - phi = array([ 6.49362466e-04, -2.77555752e-02, -1.26014065e-03, -9.83564207e-05,
        4.94988828e-03, -5.41264557e-02,  1.94366203e-01,  3.27131596e-02,
       -1.05926321e-02])
2025-07-16 02:04:00 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:00 - shap - INFO - phi = array([-6.49362466e-04,  2.77555752e-02,  1.26014065e-03,  9.83564207e-05,
       -4.94988828e-03,  5.41264557e-02, -1.94366203e-01, -3.27131596e-02,
        1.05926321e-02])
2025-07-16 02:04:00 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([-0.00047658,  0.05349977, -0.00034028,  0.        , -0.00084408,
        0.00543732,  0.22600181,  0.00778081, -0.00469907])
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([ 0.00047658, -0.05349977,  0.00034028,  0.        ,  0.00084408,
       -0.00543732, -0.22600181, -0.00778081,  0.00469907])
2025-07-16 02:04:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([-0.00060637,  0.03284551,  0.00310982,  0.00080452, -0.00094734,
       -0.00451973,  0.18792809, -0.01753002, -0.00189027])
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([ 0.00060637, -0.03284551, -0.00310982, -0.00080452,  0.00094734,
        0.00451973, -0.18792809,  0.01753002,  0.00189027])
2025-07-16 02:04:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([ 0.00024308, -0.03357058, -0.00064827,  0.        ,  0.00560838,
       -0.03668932,  0.18888564,  0.05409573, -0.00932818])
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([-0.00024308,  0.03357058,  0.00064827,  0.        , -0.00560838,
        0.03668932, -0.18888564, -0.05409573,  0.00932818])
2025-07-16 02:04:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([ 2.42756427e-04,  8.61352556e-03, -4.97610149e-04,  0.00000000e+00,
        4.91006148e-03, -1.41391808e-02,  2.57405582e-01,  5.45609430e-02,
       -6.21327188e-03])
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([-2.42756427e-04, -8.61352556e-03,  4.97610149e-04,  0.00000000e+00,
       -4.91006148e-03,  1.41391808e-02, -2.57405582e-01, -5.45609430e-02,
        6.21327188e-03])
2025-07-16 02:04:01 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([ 0.00051102, -0.03068321, -0.00060914, -0.0003521 , -0.00185081,
        0.01339519, -0.29700782, -0.00166243,  0.00834297])
2025-07-16 02:04:01 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:01 - shap - INFO - phi = array([-0.00051102,  0.03068321,  0.00060914,  0.0003521 ,  0.00185081,
       -0.01339519,  0.29700782,  0.00166243, -0.00834297])
2025-07-16 02:04:02 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:02 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:02 - shap - INFO - phi = array([-2.42492390e-04,  4.56977320e-03, -2.78267852e-04,  2.48432854e-04,
       -1.92442323e-03,  1.03893769e-02, -3.30488049e-01,  0.00000000e+00,
        7.62118854e-03])
2025-07-16 02:04:02 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:02 - shap - INFO - phi = array([ 2.42492390e-04, -4.56977320e-03,  2.78267852e-04, -2.48432854e-04,
        1.92442323e-03, -1.03893769e-02,  3.30488049e-01,  0.00000000e+00,
       -7.62118854e-03])
2025-07-16 02:04:02 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:02 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:02 - shap - INFO - phi = array([-2.47646206e-04, -1.89987526e-02, -2.94990825e-04,  0.00000000e+00,
       -1.96995295e-03,  1.79697512e-02, -2.98957030e-01, -1.31860083e-02,
        5.70401438e-03])
2025-07-16 02:04:02 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:02 - shap - INFO - phi = array([ 2.47646206e-04,  1.89987526e-02,  2.94990825e-04,  0.00000000e+00,
        1.96995295e-03, -1.79697512e-02,  2.98957030e-01,  1.31860083e-02,
       -5.70401438e-03])
2025-07-16 02:04:02 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:02 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:02 - shap - INFO - phi = array([ 0.00032945, -0.03054847, -0.00049832,  0.        , -0.00113039,
       -0.04495179, -0.27031986,  0.03066148,  0.00737317])
2025-07-16 02:04:02 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:02 - shap - INFO - phi = array([-0.00032945,  0.03054847,  0.00049832,  0.        ,  0.00113039,
        0.04495179,  0.27031986, -0.03066148, -0.00737317])
2025-07-16 02:04:02 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:02 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:02 - shap - INFO - phi = array([-0.00062564,  0.05759851, -0.00029244,  0.00074185, -0.00098388,
       -0.00315234,  0.16004783, -0.016366  , -0.00182607])
2025-07-16 02:04:02 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:02 - shap - INFO - phi = array([ 0.00062564, -0.05759851,  0.00029244, -0.00074185,  0.00098388,
        0.00315234, -0.16004783,  0.016366  ,  0.00182607])
2025-07-16 02:04:02 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([ 0.00069485, -0.04786756, -0.00104815, -0.00022674, -0.0020264 ,
       -0.01252911,  0.13083569, -0.00072511, -0.0122053 ])
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([-0.00069485,  0.04786756,  0.00104815,  0.00022674,  0.0020264 ,
        0.01252911, -0.13083569,  0.00072511,  0.0122053 ])
2025-07-16 02:04:03 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([-0.00031436,  0.0730429 , -0.00038624,  0.00028295,  0.00473997,
       -0.01738379,  0.2319562 ,  0.01242665,  0.00023243])
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([ 0.00031436, -0.0730429 ,  0.00038624, -0.00028295, -0.00473997,
        0.01738379, -0.2319562 , -0.01242665, -0.00023243])
2025-07-16 02:04:03 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([-2.48354674e-04, -2.75120312e-02, -3.06034106e-04, -1.93783832e-04,
       -2.02490708e-03,  1.15016679e-02, -2.73324057e-01, -1.92914473e-02,
        1.41833230e-03])
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([ 2.48354674e-04,  2.75120312e-02,  3.06034106e-04,  1.93783832e-04,
        2.02490708e-03, -1.15016679e-02,  2.73324057e-01,  1.92914473e-02,
       -1.41833230e-03])
2025-07-16 02:04:03 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([-0.00051825,  0.12396747, -0.00018093,  0.00023105, -0.00198558,
        0.01804714,  0.17719151, -0.02759097, -0.00353596])
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([ 0.00051825, -0.12396747,  0.00018093, -0.00023105,  0.00198558,
       -0.01804714, -0.17719151,  0.02759097,  0.00353596])
2025-07-16 02:04:03 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([ 0.000323  , -0.02999228, -0.0021172 ,  0.        , -0.00197391,
        0.01322452, -0.29644206, -0.00153415,  0.00688432])
2025-07-16 02:04:03 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:03 - shap - INFO - phi = array([-0.000323  ,  0.02999228,  0.0021172 ,  0.        ,  0.00197391,
       -0.01322452,  0.29644206,  0.00153415, -0.00688432])
2025-07-16 02:04:03 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([ 0.00037709, -0.04260468, -0.00062048, -0.00032161, -0.00199481,
        0.01666817, -0.27428475, -0.00840225,  0.00125964])
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([-0.00037709,  0.04260468,  0.00062048,  0.00032161,  0.00199481,
       -0.01666817,  0.27428475,  0.00840225, -0.00125964])
2025-07-16 02:04:04 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([-0.00064964,  0.01686968, -0.00077544,  0.00135987, -0.00226457,
        0.00421216,  0.184264  , -0.01252725,  0.00293667])
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([ 0.00064964, -0.01686968,  0.00077544, -0.00135987,  0.00226457,
       -0.00421216, -0.184264  ,  0.01252725, -0.00293667])
2025-07-16 02:04:04 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([-2.68442300e-04, -1.26946623e-02, -5.41637843e-04,  0.00000000e+00,
       -1.93739424e-03,  1.37347709e-02, -2.99922095e-01, -1.43747135e-02,
        5.76070875e-03])
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([ 2.68442300e-04,  1.26946623e-02,  5.41637843e-04,  0.00000000e+00,
        1.93739424e-03, -1.37347709e-02,  2.99922095e-01,  1.43747135e-02,
       -5.76070875e-03])
2025-07-16 02:04:04 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([ 3.20035049e-04, -2.80560525e-02, -2.11761257e-03, -3.15233172e-04,
       -1.96132275e-03,  2.06410144e-02, -3.07019402e-01, -1.77148503e-04,
        7.05795682e-03])
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([-3.20035049e-04,  2.80560525e-02,  2.11761257e-03,  3.15233172e-04,
        1.96132275e-03, -2.06410144e-02,  3.07019402e-01,  1.77148503e-04,
       -7.05795682e-03])
2025-07-16 02:04:04 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([-0.00056408,  0.01499206,  0.00820853,  0.00082636, -0.0011072 ,
        0.00134518,  0.19783688, -0.01335653, -0.00305688])
2025-07-16 02:04:04 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:04 - shap - INFO - phi = array([ 0.00056408, -0.01499206, -0.00820853, -0.00082636,  0.0011072 ,
       -0.00134518, -0.19783688,  0.01335653,  0.00305688])
2025-07-16 02:04:04 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:05 - shap - INFO - phi = array([ 0.00035342, -0.04250173, -0.00220568, -0.00032914, -0.00210949,
        0.01751798, -0.25430961, -0.02708335, -0.00096746])
2025-07-16 02:04:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:05 - shap - INFO - phi = array([-0.00035342,  0.04250173,  0.00220568,  0.00032914,  0.00210949,
       -0.01751798,  0.25430961,  0.02708335,  0.00096746])
2025-07-16 02:04:05 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:05 - shap - INFO - phi = array([ 3.26087770e-04, -2.82887579e-02, -3.11249933e-04,  1.46708949e-04,
       -2.05365263e-03,  2.23716857e-02, -2.66564962e-01, -3.47515465e-02,
       -5.26100573e-04])
2025-07-16 02:04:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:05 - shap - INFO - phi = array([-3.26087770e-04,  2.82887579e-02,  3.11249933e-04, -1.46708949e-04,
        2.05365263e-03, -2.23716857e-02,  2.66564962e-01,  3.47515465e-02,
        5.26100573e-04])
2025-07-16 02:04:05 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:05 - shap - INFO - phi = array([ 0.00065136, -0.03164442, -0.00097735, -0.00041673, -0.00129705,
       -0.01958778,  0.14237376,  0.03338387,  0.00765295])
2025-07-16 02:04:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:05 - shap - INFO - phi = array([-0.00065136,  0.03164442,  0.00097735,  0.00041673,  0.00129705,
        0.01958778, -0.14237376, -0.03338387, -0.00765295])
2025-07-16 02:04:05 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:05 - shap - INFO - phi = array([-1.55501408e-04,  1.48195321e-02, -4.38231639e-04, -1.95732595e-04,
        4.92191733e-03, -2.07395230e-02,  2.30448019e-01,  7.40564313e-02,
        1.96720351e-03])
2025-07-16 02:04:05 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:05 - shap - INFO - phi = array([ 1.55501408e-04, -1.48195321e-02,  4.38231639e-04,  1.95732595e-04,
       -4.92191733e-03,  2.07395230e-02, -2.30448019e-01, -7.40564313e-02,
       -1.96720351e-03])
2025-07-16 02:04:05 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([-0.00057722,  0.02236501,  0.00793924,  0.00058252, -0.00217169,
        0.00176052,  0.1924787 , -0.01625147, -0.00237951])
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([ 0.00057722, -0.02236501, -0.00793924, -0.00058252,  0.00217169,
       -0.00176052, -0.1924787 ,  0.01625147,  0.00237951])
2025-07-16 02:04:06 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([ 0.0003522 , -0.03048578, -0.0005845 , -0.00030937, -0.00196565,
        0.0208544 , -0.30200029, -0.00226144,  0.00648411])
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([-0.0003522 ,  0.03048578,  0.0005845 ,  0.00030937,  0.00196565,
       -0.0208544 ,  0.30200029,  0.00226144, -0.00648411])
2025-07-16 02:04:06 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([ 3.32418685e-04, -3.43857392e-02, -5.98196508e-04, -1.69312974e-04,
       -1.97554109e-03,  1.28534729e-02, -2.92008808e-01, -7.13657839e-04,
        6.74903332e-03])
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([-3.32418685e-04,  3.43857392e-02,  5.98196508e-04,  1.69312974e-04,
        1.97554109e-03, -1.28534729e-02,  2.92008808e-01,  7.13657839e-04,
       -6.74903332e-03])
2025-07-16 02:04:06 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([ 3.32695949e-04, -2.55065289e-02, -2.86187729e-04, -2.85466040e-04,
       -1.96030777e-03,  2.15994049e-02, -2.87154781e-01, -1.93362458e-02,
        2.94562831e-03])
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([-3.32695949e-04,  2.55065289e-02,  2.86187729e-04,  2.85466040e-04,
        1.96030777e-03, -2.15994049e-02,  2.87154781e-01,  1.93362458e-02,
       -2.94562831e-03])
2025-07-16 02:04:06 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([-0.00020267,  0.02760417, -0.0006161 ,  0.00246044,  0.0061793 ,
       -0.00262437,  0.14328318,  0.04378382,  0.00641239])
2025-07-16 02:04:06 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:06 - shap - INFO - phi = array([ 0.00020267, -0.02760417,  0.0006161 , -0.00246044, -0.0061793 ,
        0.00262437, -0.14328318, -0.04378382, -0.00641239])
2025-07-16 02:04:06 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:07 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:07 - shap - INFO - phi = array([-0.00081209,  0.06358998, -0.0007598 , -0.00511205, -0.00203193,
        0.02019206,  0.11517731, -0.05138282, -0.01039772])
2025-07-16 02:04:07 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:07 - shap - INFO - phi = array([ 0.00081209, -0.06358998,  0.0007598 ,  0.00511205,  0.00203193,
       -0.02019206, -0.11517731,  0.05138282,  0.01039772])
2025-07-16 02:04:07 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:07 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:07 - shap - INFO - phi = array([ 4.79395324e-04,  2.46688031e-02, -3.81694365e-04,  6.77830000e-05,
        3.83954566e-03, -1.61351054e-02,  2.55340662e-01,  2.62709559e-02,
       -3.02503743e-03])
2025-07-16 02:04:07 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:07 - shap - INFO - phi = array([-4.79395324e-04, -2.46688031e-02,  3.81694365e-04, -6.77830000e-05,
       -3.83954566e-03,  1.61351054e-02, -2.55340662e-01, -2.62709559e-02,
        3.02503743e-03])
2025-07-16 02:04:07 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:07 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:07 - shap - INFO - phi = array([-0.00060415,  0.00731706, -0.00149584,  0.0002748 , -0.00232438,
        0.00204796,  0.19390451, -0.05879713, -0.01291668])
2025-07-16 02:04:07 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:07 - shap - INFO - phi = array([ 0.00060415, -0.00731706,  0.00149584, -0.0002748 ,  0.00232438,
       -0.00204796, -0.19390451,  0.05879713,  0.01291668])
2025-07-16 02:04:07 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:07 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:07 - shap - INFO - phi = array([ 2.17570258e-04,  5.84735419e-02, -4.18436407e-04,  1.00549671e-03,
        4.17400545e-03, -2.03798493e-02,  2.49087170e-01,  1.56965988e-02,
       -3.71734574e-03])
2025-07-16 02:04:07 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:07 - shap - INFO - phi = array([-2.17570258e-04, -5.84735419e-02,  4.18436407e-04, -1.00549671e-03,
       -4.17400545e-03,  2.03798493e-02, -2.49087170e-01, -1.56965988e-02,
        3.71734574e-03])
2025-07-16 02:04:07 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:08 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:08 - shap - INFO - phi = array([ 0.0003783 , -0.03151047, -0.00058964, -0.00033646, -0.00197258,
        0.02168545, -0.28074988, -0.01932502,  0.00250397])
2025-07-16 02:04:08 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:08 - shap - INFO - phi = array([-0.0003783 ,  0.03151047,  0.00058964,  0.00033646,  0.00197258,
       -0.02168545,  0.28074988,  0.01932502, -0.00250397])
2025-07-16 02:04:08 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:08 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:08 - shap - INFO - phi = array([ 5.36422252e-04, -5.05769767e-03, -2.17017304e-03, -1.00669422e-04,
       -1.17526946e-03, -1.81794425e-03,  2.85579207e-01,  1.39176266e-02,
       -4.26060611e-03])
2025-07-16 02:04:08 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:08 - shap - INFO - phi = array([-5.36422252e-04,  5.05769767e-03,  2.17017304e-03,  1.00669422e-04,
        1.17526946e-03,  1.81794425e-03, -2.85579207e-01, -1.39176266e-02,
        4.26060611e-03])
2025-07-16 02:04:08 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:08 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:08 - shap - INFO - phi = array([-0.00072023, -0.02377292, -0.00082485, -0.00029716, -0.00223585,
        0.01258901,  0.10623867, -0.03862134,  0.00021234])
2025-07-16 02:04:08 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:08 - shap - INFO - phi = array([ 0.00072023,  0.02377292,  0.00082485,  0.00029716,  0.00223585,
       -0.01258901, -0.10623867,  0.03862134, -0.00021234])
2025-07-16 02:04:08 - shap - INFO - num_full_subsets = 4
2025-07-16 02:04:08 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:08 - shap - INFO - phi = array([ 0.00031919, -0.02503927, -0.00056706, -0.00070944, -0.00194762,
        0.0202468 , -0.30946993,  0.        ,  0.007251  ])
2025-07-16 02:04:08 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-16 02:04:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-16 02:04:08 - shap - INFO - phi = array([-0.00031919,  0.02503927,  0.00056706,  0.00070944,  0.00194762,
       -0.0202468 ,  0.30946993,  0.        , -0.007251  ])
2025-07-16 02:04:13 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
