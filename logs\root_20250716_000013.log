2025-07-16 00:00:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:00:13 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 00:00:13 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-16 00:00:13 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-16 00:00:13 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:14 - shap - INFO - phi = array([-0.03212111, -0.06934162,  0.12401579,  0.01010495,  0.34017828,
       -0.08342649, -0.03515193,  0.01390238, -0.00151368, -0.0211192 ])
2025-07-16 00:00:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:14 - shap - INFO - phi = array([ 0.03212111,  0.06934162, -0.12401579, -0.01010495, -0.34017828,
        0.08342649,  0.03515193, -0.01390238,  0.00151368,  0.0211192 ])
2025-07-16 00:00:14 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:14 - shap - INFO - phi = array([-0.00283672, -0.14689305, -0.10346122,  0.00222485, -0.108345  ,
        0.07413851, -0.02708253,  0.00321673,  0.01532378, -0.00046276])
2025-07-16 00:00:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:14 - shap - INFO - phi = array([ 0.00283672,  0.14689305,  0.10346122, -0.00222485,  0.108345  ,
       -0.07413851,  0.02708253, -0.00321673, -0.01532378,  0.00046276])
2025-07-16 00:00:14 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:14 - shap - INFO - phi = array([-0.0389218 ,  0.08817531,  0.14965423,  0.00214163,  0.13488108,
        0.06504314,  0.05787404, -0.00678538, -0.03073553,  0.01149176])
2025-07-16 00:00:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:14 - shap - INFO - phi = array([ 0.0389218 , -0.08817531, -0.14965423, -0.00214163, -0.13488108,
       -0.06504314, -0.05787404,  0.00678538,  0.03073553, -0.01149176])
2025-07-16 00:00:14 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:14 - shap - INFO - phi = array([-0.00905897,  0.04627166, -0.04544055,  0.00354544,  0.05777175,
        0.25360399, -0.00698446,  0.00751053,  0.01623183, -0.00069383])
2025-07-16 00:00:14 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:14 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:14 - shap - INFO - phi = array([ 0.00905897, -0.04627166,  0.04544055, -0.00354544, -0.05777175,
       -0.25360399,  0.00698446, -0.00751053, -0.01623183,  0.00069383])
2025-07-16 00:00:14 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([-2.72180373e-02,  1.86239146e-04,  2.41269320e-02, -1.42388445e-03,
        2.90867682e-01,  1.60655422e-01,  2.03366460e-03,  5.04982671e-03,
        2.75577053e-03, -2.73042113e-04])
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([ 2.72180373e-02, -1.86239146e-04, -2.41269320e-02,  1.42388445e-03,
       -2.90867682e-01, -1.60655422e-01, -2.03366460e-03, -5.04982671e-03,
       -2.75577053e-03,  2.73042113e-04])
2025-07-16 00:00:15 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([ 0.09434658,  0.05053179, -0.05261457, -0.0172736 ,  0.18486173,
       -0.16025775, -0.01313692, -0.02276282,  0.00356005,  0.05561699])
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([-0.09434658, -0.05053179,  0.05261457,  0.0172736 , -0.18486173,
        0.16025775,  0.01313692,  0.02276282, -0.00356005, -0.05561699])
2025-07-16 00:00:15 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([ 3.30261278e-02,  1.45137512e-01,  2.58852384e-02,  1.08269008e-03,
        1.15071789e-01,  1.45382920e-01,  3.18039306e-02, -1.23330407e-02,
        9.77454580e-05, -1.03835249e-02])
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([-3.30261278e-02, -1.45137512e-01, -2.58852384e-02, -1.08269008e-03,
       -1.15071789e-01, -1.45382920e-01, -3.18039306e-02,  1.23330407e-02,
       -9.77454580e-05,  1.03835249e-02])
2025-07-16 00:00:15 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([ 0.13844427,  0.02527217,  0.03929309,  0.00393155,  0.27628877,
        0.        ,  0.01855234, -0.00457464, -0.03101558, -0.00625736])
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([-0.13844427, -0.02527217, -0.03929309, -0.00393155, -0.27628877,
        0.        , -0.01855234,  0.00457464,  0.03101558,  0.00625736])
2025-07-16 00:00:15 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([ 0.05844383, -0.27539456, -0.03473277,  0.00212975,  0.06124995,
       -0.02162393,  0.13865817,  0.00202281, -0.00135684, -0.01711509])
2025-07-16 00:00:15 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:15 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:15 - shap - INFO - phi = array([-0.05844383,  0.27539456,  0.03473277, -0.00212975, -0.06124995,
        0.02162393, -0.13865817, -0.00202281,  0.00135684,  0.01711509])
2025-07-16 00:00:15 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:16 - shap - INFO - phi = array([-2.63607627e-03, -1.54818943e-01,  1.66169208e-04, -3.93397727e-03,
       -3.27770003e-01,  6.08771532e-02,  2.55655202e-02,  3.75967099e-03,
        9.73502576e-03,  2.23375438e-02])
2025-07-16 00:00:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:16 - shap - INFO - phi = array([ 2.63607627e-03,  1.54818943e-01, -1.66169208e-04,  3.93397727e-03,
        3.27770003e-01, -6.08771532e-02, -2.55655202e-02, -3.75967099e-03,
       -9.73502576e-03, -2.23375438e-02])
2025-07-16 00:00:16 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:16 - shap - INFO - phi = array([ 0.16260026, -0.14030854,  0.09919462, -0.01299819, -0.26547817,
        0.06301859, -0.06697443, -0.00645513, -0.00072402, -0.00362388])
2025-07-16 00:00:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:16 - shap - INFO - phi = array([-0.16260026,  0.14030854, -0.09919462,  0.01299819,  0.26547817,
       -0.06301859,  0.06697443,  0.00645513,  0.00072402,  0.00362388])
2025-07-16 00:00:16 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:16 - shap - INFO - phi = array([ 0.08119529, -0.13794075, -0.02209258, -0.0039657 , -0.16879822,
       -0.0832078 , -0.01259859,  0.00073447, -0.00852283, -0.0112362 ])
2025-07-16 00:00:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:16 - shap - INFO - phi = array([-0.08119529,  0.13794075,  0.02209258,  0.0039657 ,  0.16879822,
        0.0832078 ,  0.01259859, -0.00073447,  0.00852283,  0.0112362 ])
2025-07-16 00:00:16 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:16 - shap - INFO - phi = array([ 0.00226794,  0.04639793,  0.06629905,  0.00531745,  0.24689215,
       -0.1153321 ,  0.09000844,  0.00915681,  0.00460942,  0.03657673])
2025-07-16 00:00:16 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:16 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:16 - shap - INFO - phi = array([-0.00226794, -0.04639793, -0.06629905, -0.00531745, -0.24689215,
        0.1153321 , -0.09000844, -0.00915681, -0.00460942, -0.03657673])
2025-07-16 00:00:16 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([-0.02415783, -0.06717883, -0.05725475, -0.00055682, -0.14373367,
       -0.06293716, -0.04294693,  0.00195039,  0.00673837, -0.00361647])
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([ 0.02415783,  0.06717883,  0.05725475,  0.00055682,  0.14373367,
        0.06293716,  0.04294693, -0.00195039, -0.00673837,  0.00361647])
2025-07-16 00:00:17 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([-7.38897314e-02,  3.40913697e-03,  0.00000000e+00,  3.27937650e-03,
        4.01320438e-01, -4.66261847e-02,  3.07724451e-02,  1.05859759e-02,
        3.99588468e-04,  2.90305053e-02])
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([ 7.38897314e-02, -3.40913697e-03,  0.00000000e+00, -3.27937650e-03,
       -4.01320438e-01,  4.66261847e-02, -3.07724451e-02, -1.05859759e-02,
       -3.99588468e-04, -2.90305053e-02])
2025-07-16 00:00:17 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([ 0.10162812, -0.14557978,  0.02770334, -0.01759724, -0.06258689,
        0.09793866,  0.05008566,  0.00420368, -0.00338704,  0.03341263])
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([-0.10162812,  0.14557978, -0.02770334,  0.01759724,  0.06258689,
       -0.09793866, -0.05008566, -0.00420368,  0.00338704, -0.03341263])
2025-07-16 00:00:17 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([-0.05768579,  0.24148362,  0.00180458,  0.00492718,  0.19218913,
        0.03950843,  0.03487586, -0.00114859, -0.01622486,  0.01013039])
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([ 0.05768579, -0.24148362, -0.00180458, -0.00492718, -0.19218913,
       -0.03950843, -0.03487586,  0.00114859,  0.01622486, -0.01013039])
2025-07-16 00:00:17 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([-0.00735551,  0.08482504,  0.02075982,  0.00206716, -0.01211946,
        0.05506879,  0.04588524, -0.01608084,  0.05587303, -0.02707296])
2025-07-16 00:00:17 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:17 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:17 - shap - INFO - phi = array([ 0.00735551, -0.08482504, -0.02075982, -0.00206716,  0.01211946,
       -0.05506879, -0.04588524,  0.01608084, -0.05587303,  0.02707296])
2025-07-16 00:00:17 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:18 - shap - INFO - phi = array([-0.00669715, -0.06561924, -0.00270437,  0.0027188 , -0.27104826,
       -0.04279389,  0.05107096, -0.00405611, -0.01632133, -0.01481795])
2025-07-16 00:00:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:18 - shap - INFO - phi = array([ 0.00669715,  0.06561924,  0.00270437, -0.0027188 ,  0.27104826,
        0.04279389, -0.05107096,  0.00405611,  0.01632133,  0.01481795])
2025-07-16 00:00:18 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:18 - shap - INFO - phi = array([ 0.13023259,  0.00209173, -0.16280735, -0.00582489,  0.04327779,
       -0.14064452, -0.07479377, -0.02041041,  0.00364482, -0.0032481 ])
2025-07-16 00:00:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:18 - shap - INFO - phi = array([-0.13023259, -0.00209173,  0.16280735,  0.00582489, -0.04327779,
        0.14064452,  0.07479377,  0.02041041, -0.00364482,  0.0032481 ])
2025-07-16 00:00:18 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:18 - shap - INFO - phi = array([-0.03695374,  0.31204753, -0.11473213, -0.00235185, -0.04806599,
        0.00195093, -0.00318351,  0.0095713 ,  0.02523861,  0.0273353 ])
2025-07-16 00:00:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:18 - shap - INFO - phi = array([ 0.03695374, -0.31204753,  0.11473213,  0.00235185,  0.04806599,
       -0.00195093,  0.00318351, -0.0095713 , -0.02523861, -0.0273353 ])
2025-07-16 00:00:18 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:18 - shap - INFO - phi = array([-8.49645272e-03,  2.90937466e-01, -8.57558063e-03, -1.52166170e-03,
        2.45376372e-01, -2.76986911e-04, -9.63428423e-02,  9.99966181e-03,
        1.12680952e-02, -6.93924749e-03])
2025-07-16 00:00:18 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:18 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:18 - shap - INFO - phi = array([ 8.49645272e-03, -2.90937466e-01,  8.57558063e-03,  1.52166170e-03,
       -2.45376372e-01,  2.76986911e-04,  9.63428423e-02, -9.99966181e-03,
       -1.12680952e-02,  6.93924749e-03])
2025-07-16 00:00:18 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([-0.06691188,  0.06170753, -0.00183572, -0.01176555, -0.09518858,
        0.03135408,  0.08353954,  0.01667196, -0.02154609, -0.01043105])
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([ 0.06691188, -0.06170753,  0.00183572,  0.01176555,  0.09518858,
       -0.03135408, -0.08353954, -0.01667196,  0.02154609,  0.01043105])
2025-07-16 00:00:19 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([-0.10250092, -0.08047126, -0.01286659,  0.        , -0.19916166,
        0.01015476, -0.00700971,  0.00159075, -0.00176162, -0.00230707])
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([ 0.10250092,  0.08047126,  0.01286659,  0.        ,  0.19916166,
       -0.01015476,  0.00700971, -0.00159075,  0.00176162,  0.00230707])
2025-07-16 00:00:19 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([-0.21119972,  0.05083182,  0.00915191,  0.01523866,  0.16901289,
        0.06361371,  0.07859546,  0.00870988,  0.02810074,  0.00490759])
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([ 0.21119972, -0.05083182, -0.00915191, -0.01523866, -0.16901289,
       -0.06361371, -0.07859546, -0.00870988, -0.02810074, -0.00490759])
2025-07-16 00:00:19 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([ 0.0273357 ,  0.14549376, -0.15323231, -0.00191121,  0.18878231,
       -0.1836128 ,  0.03195768, -0.04621429, -0.04505957, -0.03030602])
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([-0.0273357 , -0.14549376,  0.15323231,  0.00191121, -0.18878231,
        0.1836128 , -0.03195768,  0.04621429,  0.04505957,  0.03030602])
2025-07-16 00:00:19 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([-0.0172385 , -0.0825672 , -0.01382834, -0.00055492, -0.06378044,
       -0.23245661,  0.0275152 ,  0.0005321 ,  0.01331646, -0.01341859])
2025-07-16 00:00:19 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:19 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:19 - shap - INFO - phi = array([ 0.0172385 ,  0.0825672 ,  0.01382834,  0.00055492,  0.06378044,
        0.23245661, -0.0275152 , -0.0005321 , -0.01331646,  0.01341859])
2025-07-16 00:00:19 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:20 - shap - INFO - phi = array([-0.06495366,  0.04513911, -0.00560394,  0.00368845, -0.39130883,
        0.05105721, -0.00788966,  0.00092564, -0.00943189,  0.01576139])
2025-07-16 00:00:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:20 - shap - INFO - phi = array([ 0.06495366, -0.04513911,  0.00560394, -0.00368845,  0.39130883,
       -0.05105721,  0.00788966, -0.00092564,  0.00943189, -0.01576139])
2025-07-16 00:00:20 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:20 - shap - INFO - phi = array([-0.02157775, -0.15423181, -0.05397736, -0.0013199 ,  0.04736345,
       -0.16083817, -0.02752623,  0.00357014,  0.00060547, -0.00049659])
2025-07-16 00:00:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:20 - shap - INFO - phi = array([ 0.02157775,  0.15423181,  0.05397736,  0.0013199 , -0.04736345,
        0.16083817,  0.02752623, -0.00357014, -0.00060547,  0.00049659])
2025-07-16 00:00:20 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:20 - shap - INFO - phi = array([-0.25220857, -0.01891205, -0.04428954, -0.00086485, -0.00418242,
       -0.01722116, -0.0354622 ,  0.00532812,  0.01582469,  0.00432037])
2025-07-16 00:00:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:20 - shap - INFO - phi = array([ 0.25220857,  0.01891205,  0.04428954,  0.00086485,  0.00418242,
        0.01722116,  0.0354622 , -0.00532812, -0.01582469, -0.00432037])
2025-07-16 00:00:20 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:20 - shap - INFO - phi = array([-0.00571143, -0.1297116 ,  0.00898589, -0.00164749, -0.13685698,
       -0.12830462, -0.0127628 ,  0.00676761,  0.01333792, -0.00418831])
2025-07-16 00:00:20 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:20 - shap - INFO - phi = array([ 0.00571143,  0.1297116 , -0.00898589,  0.00164749,  0.13685698,
        0.12830462,  0.0127628 , -0.00676761, -0.01333792,  0.00418831])
2025-07-16 00:00:20 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([-0.05139379, -0.20503351,  0.05180065, -0.00303351, -0.03428459,
       -0.1195905 , -0.01396603,  0.00258911, -0.00157418,  0.00937642])
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([ 0.05139379,  0.20503351, -0.05180065,  0.00303351,  0.03428459,
        0.1195905 ,  0.01396603, -0.00258911,  0.00157418, -0.00937642])
2025-07-16 00:00:21 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([-0.02180527, -0.1153067 , -0.0815287 ,  0.00341424,  0.19169425,
       -0.15465964, -0.09461748,  0.0100296 , -0.00509903, -0.01537277])
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([ 0.02180527,  0.1153067 ,  0.0815287 , -0.00341424, -0.19169425,
        0.15465964,  0.09461748, -0.0100296 ,  0.00509903,  0.01537277])
2025-07-16 00:00:21 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([ 0.19528554, -0.08411786,  0.12556538,  0.00540887, -0.26604005,
       -0.07529992,  0.06562283,  0.01005209, -0.00188695, -0.00537991])
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([-0.19528554,  0.08411786, -0.12556538, -0.00540887,  0.26604005,
        0.07529992, -0.06562283, -0.01005209,  0.00188695,  0.00537991])
2025-07-16 00:00:21 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([-0.05211836,  0.07460444,  0.08506891,  0.0130647 , -0.05705388,
        0.08000074,  0.02636182,  0.00740083, -0.02721363,  0.02483622])
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([ 0.05211836, -0.07460444, -0.08506891, -0.0130647 ,  0.05705388,
       -0.08000074, -0.02636182, -0.00740083,  0.02721363, -0.02483622])
2025-07-16 00:00:21 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([ 0.13633826, -0.09136948,  0.03442064, -0.00248943, -0.38015189,
       -0.08110933,  0.03865861, -0.00347632, -0.00152136, -0.00685583])
2025-07-16 00:00:21 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:21 - shap - INFO - phi = array([-0.13633826,  0.09136948, -0.03442064,  0.00248943,  0.38015189,
        0.08110933, -0.03865861,  0.00347632,  0.00152136,  0.00685583])
2025-07-16 00:00:21 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:22 - shap - INFO - phi = array([-7.51338499e-02, -6.32692617e-02, -3.94438911e-02, -9.67090017e-05,
       -1.65241355e-01,  1.58789277e-02, -6.64572478e-02,  1.39463405e-03,
       -1.87251095e-03,  2.53249177e-03])
2025-07-16 00:00:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:22 - shap - INFO - phi = array([ 7.51338499e-02,  6.32692617e-02,  3.94438911e-02,  9.67090017e-05,
        1.65241355e-01, -1.58789277e-02,  6.64572478e-02, -1.39463405e-03,
        1.87251095e-03, -2.53249177e-03])
2025-07-16 00:00:22 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:22 - shap - INFO - phi = array([-0.10280365, -0.06400778,  0.06499527,  0.00156779, -0.32070096,
        0.06925154, -0.0038895 ,  0.        , -0.02555724,  0.00743761])
2025-07-16 00:00:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:22 - shap - INFO - phi = array([ 0.10280365,  0.06400778, -0.06499527, -0.00156779,  0.32070096,
       -0.06925154,  0.0038895 ,  0.        ,  0.02555724, -0.00743761])
2025-07-16 00:00:22 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:22 - shap - INFO - phi = array([ 0.00033218,  0.13861819, -0.02294149,  0.00127476,  0.29741032,
        0.06386819,  0.00096711,  0.00645569,  0.        , -0.00990556])
2025-07-16 00:00:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:22 - shap - INFO - phi = array([-0.00033218, -0.13861819,  0.02294149, -0.00127476, -0.29741032,
       -0.06386819, -0.00096711, -0.00645569,  0.        ,  0.00990556])
2025-07-16 00:00:22 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:22 - shap - INFO - phi = array([-0.014638  ,  0.03670132, -0.0137043 , -0.00617992,  0.21093125,
        0.20072   , -0.03187549, -0.01436476,  0.00486249, -0.01534349])
2025-07-16 00:00:22 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:22 - shap - INFO - phi = array([ 0.014638  , -0.03670132,  0.0137043 ,  0.00617992, -0.21093125,
       -0.20072   ,  0.03187549,  0.01436476, -0.00486249,  0.01534349])
2025-07-16 00:00:22 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([ 0.20777391,  0.28417358, -0.01758306,  0.00229015, -0.29829135,
        0.04152819, -0.0926019 , -0.01859313,  0.02612758,  0.00124656])
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([-0.20777391, -0.28417358,  0.01758306, -0.00229015,  0.29829135,
       -0.04152819,  0.0926019 ,  0.01859313, -0.02612758, -0.00124656])
2025-07-16 00:00:23 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([ 0.16267177,  0.07279131,  0.00499467,  0.00275367,  0.12354111,
        0.07983243,  0.03230371,  0.0028212 ,  0.00068261, -0.00666671])
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([-0.16267177, -0.07279131, -0.00499467, -0.00275367, -0.12354111,
       -0.07983243, -0.03230371, -0.0028212 , -0.00068261,  0.00666671])
2025-07-16 00:00:23 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([-0.09604603,  0.01383354,  0.0646371 ,  0.00963633,  0.05787855,
       -0.02213334, -0.15692628,  0.00836877,  0.02548033, -0.01659417])
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([ 0.09604603, -0.01383354, -0.0646371 , -0.00963633, -0.05787855,
        0.02213334,  0.15692628, -0.00836877, -0.02548033,  0.01659417])
2025-07-16 00:00:23 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([-0.30990618,  0.04025392, -0.03317782, -0.00856473, -0.16432953,
        0.18298625, -0.02923124,  0.        , -0.01561773, -0.01193216])
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([ 0.30990618, -0.04025392,  0.03317782,  0.00856473,  0.16432953,
       -0.18298625,  0.02923124,  0.        ,  0.01561773,  0.01193216])
2025-07-16 00:00:23 - shap - INFO - num_full_subsets = 5
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([ 0.26225445, -0.00884151,  0.07004762,  0.00392776,  0.10767747,
        0.02493659, -0.00329623,  0.00221641, -0.01572423, -0.02029231])
2025-07-16 00:00:23 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 00:00:23 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 00:00:23 - shap - INFO - phi = array([-0.26225445,  0.00884151, -0.07004762, -0.00392776, -0.10767747,
       -0.02493659,  0.00329623, -0.00221641,  0.01572423,  0.02029231])
2025-07-16 00:00:28 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
