2025-07-15 19:40:10 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 193, 'max_depth': 21, 'min_samples_split': 14, 'min_samples_leaf': 2, 'max_features': 'sqrt'}
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9707
2025-07-15 19:40:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250715_194024.html
2025-07-15 19:40:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250715_194024.html
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.74 秒
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 20
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 150, 'max_depth': 10, 'learning_rate': 0.09664322129632669, 'subsample': 0.601194064031334, 'colsample_bytree': 0.9950507433179563}
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9675
2025-07-15 19:40:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250715_194028.html
2025-07-15 19:40:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250715_194028.html
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.28 秒
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 20
2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 52, 'max_depth': 3, 'learning_rate': 0.10663368377403017, 'feature_fraction': 0.7760322736126682, 'bagging_fraction': 0.689390897018191}
2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9708
2025-07-15 19:40:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250715_194031.html
2025-07-15 19:40:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250715_194031.html
2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.83 秒
2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 20
2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 8.624492190155658, 'solver': 'liblinear'}
2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9483
2025-07-15 19:40:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250715_194032.html
2025-07-15 19:40:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250715_194032.html
2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.53 秒
