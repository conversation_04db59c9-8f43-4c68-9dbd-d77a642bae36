2025-07-15 23:28:51 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 23:28:51 - GUI - INFO - GUI界面初始化完成
2025-07-15 23:29:17 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 9)
2025-07-15 23:29:17 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 23:29:17 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-07-15 23:29:56 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 226, 'max_depth': 32, 'min_samples_split': 17, 'min_samples_leaf': 1, 'max_features': 'log2'}
2025-07-15 23:29:56 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9732
2025-07-15 23:29:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 23:29:56 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250715_232956.html
2025-07-15 23:29:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 23:29:56 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250715_232956.html
2025-07-15 23:29:56 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 39.72 秒
2025-07-15 23:30:50 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 9)
2025-07-15 23:30:50 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 23:30:50 - model_training - INFO - 模型名称: Random Forest
2025-07-15 23:30:50 - model_training - INFO - 准确率: 0.8250
2025-07-15 23:30:50 - model_training - INFO - AUC: 0.9015
2025-07-15 23:30:50 - model_training - INFO - 混淆矩阵:
2025-07-15 23:30:50 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-15 23:30:50 - model_training - INFO - 
分类报告:
2025-07-15 23:30:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-15 23:30:50 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 23:30:50 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 23:30:50 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 23:32:31 - model_ensemble - INFO - ============================================================
2025-07-15 23:32:31 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 23:32:31 - model_ensemble - INFO - ============================================================
2025-07-15 23:32:31 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'SVM']
2025-07-15 23:32:31 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-15 23:32:31 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 23:32:31 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 23:32:31 - model_training - INFO - 模型名称: Random Forest
2025-07-15 23:32:31 - model_training - INFO - 准确率: 0.8250
2025-07-15 23:32:31 - model_training - INFO - AUC: 0.9015
2025-07-15 23:32:31 - model_training - INFO - 混淆矩阵:
2025-07-15 23:32:31 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-15 23:32:31 - model_training - INFO - 
分类报告:
2025-07-15 23:32:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-15 23:32:31 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 23:32:31 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 23:32:31 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 23:32:31 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 23:32:31 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 23:32:31 - model_training - INFO - 模型名称: XGBoost
2025-07-15 23:32:31 - model_training - INFO - 准确率: 0.7750
2025-07-15 23:32:31 - model_training - INFO - AUC: 0.8568
2025-07-15 23:32:31 - model_training - INFO - 混淆矩阵:
2025-07-15 23:32:31 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-15 23:32:31 - model_training - INFO - 
分类报告:
2025-07-15 23:32:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-15 23:32:31 - model_training - INFO - 训练时间: 0.04 秒
2025-07-15 23:32:31 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 23:32:31 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 23:32:31 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 23:32:31 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 23:32:31 - model_training - INFO - 模型名称: SVM
2025-07-15 23:32:31 - model_training - INFO - 准确率: 0.8750
2025-07-15 23:32:31 - model_training - INFO - AUC: 0.9309
2025-07-15 23:32:31 - model_training - INFO - 混淆矩阵:
2025-07-15 23:32:31 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-07-15 23:32:31 - model_training - INFO - 
分类报告:
2025-07-15 23:32:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 23:32:31 - model_training - INFO - 训练时间: 0.02 秒
2025-07-15 23:32:31 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-15 23:32:31 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-15 23:32:31 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 23:32:31 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-15 23:32:31 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 23:32:31 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 23:32:31 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 23:32:31 - model_ensemble - INFO - ============================================================
2025-07-15 23:32:31 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 23:32:31 - model_ensemble - INFO - ============================================================
2025-07-15 23:32:31 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 23:32:31 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 23:32:31 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 23:32:31 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 23:32:31 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9182
2025-07-15 23:32:31 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 23:32:31 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_233231.joblib
2025-07-15 23:32:31 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 23:32:31 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-15 23:32:31 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 23:32:31 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 23:32:31 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 23:32:31 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 23:32:31 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 23:32:31 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([ 0.05593296,  0.12355624,  0.04405396,  0.01545144,  0.00234908,
       -0.0030998 ,  0.14919189, -0.00845771, -0.01729605])
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([-0.05593296, -0.12355624, -0.04405396, -0.01545144, -0.00234908,
        0.0030998 , -0.14919189,  0.00845771,  0.01729605])
2025-07-15 23:32:32 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([ 0.05082148,  0.08942058,  0.05310257,  0.01753952,  0.01215776,
       -0.02352595,  0.17264263, -0.0118562 ,  0.00594035])
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([-0.05082148, -0.08942058, -0.05310257, -0.01753952, -0.01215776,
        0.02352595, -0.17264263,  0.0118562 , -0.00594035])
2025-07-15 23:32:32 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([-0.12356876, -0.14277633, -0.12021472, -0.02541141, -0.01665477,
        0.01936788, -0.08805417, -0.01541061, -0.00027987])
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([ 0.12356876,  0.14277633,  0.12021472,  0.02541141,  0.01665477,
       -0.01936788,  0.08805417,  0.01541061,  0.00027987])
2025-07-15 23:32:32 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([-0.08991031, -0.07812679, -0.09865982,  0.01915063,  0.00317499,
       -0.03239907, -0.20347328, -0.00405195, -0.01263822])
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([ 0.08991031,  0.07812679,  0.09865982, -0.01915063, -0.00317499,
        0.03239907,  0.20347328,  0.00405195,  0.01263822])
2025-07-15 23:32:32 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([ 0.03017535,  0.1258052 ,  0.05039268,  0.01826398,  0.01112355,
       -0.02003498,  0.08349667,  0.05507454,  0.00398087])
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([-0.03017535, -0.1258052 , -0.05039268, -0.01826398, -0.01112355,
        0.02003498, -0.08349667, -0.05507454, -0.00398087])
2025-07-15 23:32:32 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([ 0.04696278,  0.10611237,  0.06290564, -0.01597226,  0.0194025 ,
        0.05482817,  0.06308588, -0.00208943,  0.02825958])
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([-0.04696278, -0.10611237, -0.06290564,  0.01597226, -0.0194025 ,
       -0.05482817, -0.06308588,  0.00208943, -0.02825958])
2025-07-15 23:32:32 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([ 0.04703929,  0.11298496,  0.07550422,  0.01348954,  0.00449164,
       -0.01485412,  0.15251143, -0.00979122, -0.01393491])
2025-07-15 23:32:32 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:32 - shap - INFO - phi = array([-0.04703929, -0.11298496, -0.07550422, -0.01348954, -0.00449164,
        0.01485412, -0.15251143,  0.00979122,  0.01393491])
2025-07-15 23:32:32 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([ 0.041072  ,  0.11543522,  0.06605816,  0.0289903 , -0.01509151,
       -0.05440168, -0.25713407,  0.07741836,  0.01491448])
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([-0.041072  , -0.11543522, -0.06605816, -0.0289903 ,  0.01509151,
        0.05440168,  0.25713407, -0.07741836, -0.01491448])
2025-07-15 23:32:33 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([-0.12111011, -0.13845215, -0.22858912,  0.0201611 , -0.00635312,
       -0.00275022,  0.07316623, -0.02452733,  0.04351937])
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([ 0.12111011,  0.13845215,  0.22858912, -0.0201611 ,  0.00635312,
        0.00275022, -0.07316623,  0.02452733, -0.04351937])
2025-07-15 23:32:33 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([-0.00429611, -0.15109887, -0.05762986, -0.03401879, -0.02485456,
        0.02926044, -0.17354046, -0.02737127, -0.00856867])
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([ 0.00429611,  0.15109887,  0.05762986,  0.03401879,  0.02485456,
       -0.02926044,  0.17354046,  0.02737127,  0.00856867])
2025-07-15 23:32:33 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([-0.02644152,  0.22555977, -0.01553786,  0.02455389,  0.01066275,
       -0.0311015 ,  0.16835959, -0.01766168, -0.00831682])
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([ 0.02644152, -0.22555977,  0.01553786, -0.02455389, -0.01066275,
        0.0311015 , -0.16835959,  0.01766168,  0.00831682])
2025-07-15 23:32:33 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([ 0.06912453,  0.08483436,  0.057006  ,  0.01691669,  0.01196098,
       -0.0191425 ,  0.15326085, -0.0115893 ,  0.00491085])
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([-0.06912453, -0.08483436, -0.057006  , -0.01691669, -0.01196098,
        0.0191425 , -0.15326085,  0.0115893 , -0.00491085])
2025-07-15 23:32:33 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([-0.07482542, -0.25911165, -0.03825221, -0.00311462, -0.02067053,
       -0.04241509, -0.06897411,  0.02852143, -0.03497618])
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([ 0.07482542,  0.25911165,  0.03825221,  0.00311462,  0.02067053,
        0.04241509,  0.06897411, -0.02852143,  0.03497618])
2025-07-15 23:32:33 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([-0.05325669, -0.26259283, -0.09739078, -0.00228679, -0.02646805,
       -0.0118242 , -0.00433844,  0.03893257, -0.0413329 ])
2025-07-15 23:32:33 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:33 - shap - INFO - phi = array([ 0.05325669,  0.26259283,  0.09739078,  0.00228679,  0.02646805,
        0.0118242 ,  0.00433844, -0.03893257,  0.0413329 ])
2025-07-15 23:32:33 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([ 0.0564301 , -0.12754254, -0.03050169,  0.0154735 ,  0.0131312 ,
       -0.00056842, -0.19907086,  0.01423963, -0.01985324])
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([-0.0564301 ,  0.12754254,  0.03050169, -0.0154735 , -0.0131312 ,
        0.00056842,  0.19907086, -0.01423963,  0.01985324])
2025-07-15 23:32:34 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([ 0.09598355, -0.2070017 ,  0.11356199,  0.01005819, -0.02941606,
        0.02009759,  0.03171851,  0.01012577, -0.06743272])
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([-0.09598355,  0.2070017 , -0.11356199, -0.01005819,  0.02941606,
       -0.02009759, -0.03171851, -0.01012577,  0.06743272])
2025-07-15 23:32:34 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([ 0.03116642,  0.0934965 ,  0.03112525, -0.01640642, -0.03198602,
       -0.05307511, -0.32702388,  0.06351871, -0.00961474])
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([-0.03116642, -0.0934965 , -0.03112525,  0.01640642,  0.03198602,
        0.05307511,  0.32702388, -0.06351871,  0.00961474])
2025-07-15 23:32:34 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([ 0.11566756, -0.02092891,  0.12066435,  0.00410467, -0.01373727,
        0.01024304,  0.09811959, -0.0137542 , -0.01145801])
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([-0.11566756,  0.02092891, -0.12066435, -0.00410467,  0.01373727,
       -0.01024304, -0.09811959,  0.0137542 ,  0.01145801])
2025-07-15 23:32:34 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([ 0.07234845,  0.07364764,  0.07263479, -0.01241533,  0.01023023,
        0.06745055,  0.06264885, -0.00129662,  0.02500237])
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([-0.07234845, -0.07364764, -0.07263479,  0.01241533, -0.01023023,
       -0.06745055, -0.06264885,  0.00129662, -0.02500237])
2025-07-15 23:32:34 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([-0.06056356, -0.13939905, -0.05734767, -0.02184104,  0.01623466,
       -0.05308532, -0.20515912, -0.02081216,  0.02323188])
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([ 0.06056356,  0.13939905,  0.05734767,  0.02184104, -0.01623466,
        0.05308532,  0.20515912,  0.02081216, -0.02323188])
2025-07-15 23:32:34 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([-0.03024042, -0.02861471,  0.07862401, -0.03633371, -0.01362722,
        0.07786774,  0.04059582, -0.03041489,  0.02292714])
2025-07-15 23:32:34 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:34 - shap - INFO - phi = array([ 0.03024042,  0.02861471, -0.07862401,  0.03633371,  0.01362722,
       -0.07786774, -0.04059582,  0.03041489, -0.02292714])
2025-07-15 23:32:34 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([-0.00185051,  0.02324827,  0.0873827 , -0.02378606, -0.01329284,
        0.07389713,  0.1041627 , -0.01582916,  0.05303315])
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([ 0.00185051, -0.02324827, -0.0873827 ,  0.02378606,  0.01329284,
       -0.07389713, -0.1041627 ,  0.01582916, -0.05303315])
2025-07-15 23:32:35 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([ 0.09155293,  0.10579287,  0.05952762, -0.01133473,  0.02735919,
        0.07619519,  0.01819223,  0.00452734, -0.00025885])
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([-0.09155293, -0.10579287, -0.05952762,  0.01133473, -0.02735919,
       -0.07619519, -0.01819223, -0.00452734,  0.00025885])
2025-07-15 23:32:35 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([ 0.06917733,  0.09759383,  0.0635628 ,  0.01986302,  0.00748117,
       -0.01832416,  0.14876003, -0.01425974, -0.00223235])
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([-0.06917733, -0.09759383, -0.0635628 , -0.01986302, -0.00748117,
        0.01832416, -0.14876003,  0.01425974,  0.00223235])
2025-07-15 23:32:35 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([-0.06610188, -0.13911053, -0.09692244, -0.00264011, -0.01507616,
       -0.02317582, -0.18154027,  0.01760912, -0.02024829])
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([ 0.06610188,  0.13911053,  0.09692244,  0.00264011,  0.01507616,
        0.02317582,  0.18154027, -0.01760912,  0.02024829])
2025-07-15 23:32:35 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([ 0.08388964,  0.08427246,  0.04636909, -0.01197973,  0.02760461,
        0.08589993,  0.01995368,  0.00485927,  0.03163245])
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([-0.08388964, -0.08427246, -0.04636909,  0.01197973, -0.02760461,
       -0.08589993, -0.01995368, -0.00485927, -0.03163245])
2025-07-15 23:32:35 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([-0.11793498,  0.13174065, -0.21120097,  0.02226019, -0.00210299,
       -0.02466053,  0.08345387, -0.02862249, -0.02652919])
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([ 0.11793498, -0.13174065,  0.21120097, -0.02226019,  0.00210299,
        0.02466053, -0.08345387,  0.02862249,  0.02652919])
2025-07-15 23:32:35 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([ 0.09668641, -0.18670313,  0.123967  ,  0.03025126,  0.01209003,
       -0.03278069,  0.10276041,  0.06260013, -0.02018611])
2025-07-15 23:32:35 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:35 - shap - INFO - phi = array([-0.09668641,  0.18670313, -0.123967  , -0.03025126, -0.01209003,
        0.03278069, -0.10276041, -0.06260013,  0.02018611])
2025-07-15 23:32:35 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([ 0.02509267, -0.01994229,  0.02437312, -0.02131871,  0.02654916,
       -0.07490936, -0.32647539, -0.01206768, -0.00317527])
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([-0.02509267,  0.01994229, -0.02437312,  0.02131871, -0.02654916,
        0.07490936,  0.32647539,  0.01206768,  0.00317527])
2025-07-15 23:32:36 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([-0.08945382, -0.25151574, -0.17517466,  0.01814371,  0.00691594,
        0.01942461,  0.11662863,  0.02179284, -0.03765802])
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([ 0.08945382,  0.25151574,  0.17517466, -0.01814371, -0.00691594,
       -0.01942461, -0.11662863, -0.02179284,  0.03765802])
2025-07-15 23:32:36 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([ 0.04395001,  0.17082871,  0.03785903,  0.00251082, -0.00723585,
        0.00749381,  0.12799481, -0.0094494 , -0.01494152])
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([-0.04395001, -0.17082871, -0.03785903, -0.00251082,  0.00723585,
       -0.00749381, -0.12799481,  0.0094494 ,  0.01494152])
2025-07-15 23:32:36 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([-0.06799832, -0.15059831, -0.086521  , -0.00479337, -0.01277254,
       -0.01802342, -0.1711174 , -0.01730943,  0.00231164])
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([ 0.06799832,  0.15059831,  0.086521  ,  0.00479337,  0.01277254,
        0.01802342,  0.1711174 ,  0.01730943, -0.00231164])
2025-07-15 23:32:36 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([-0.07442022, -0.13361986, -0.01928993, -0.02460586,  0.0344291 ,
       -0.02304214, -0.25748752, -0.01668467,  0.02398557])
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([ 0.07442022,  0.13361986,  0.01928993,  0.02460586, -0.0344291 ,
        0.02304214,  0.25748752,  0.01668467, -0.02398557])
2025-07-15 23:32:36 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([-0.07459086,  0.1721484 , -0.00228618, -0.03643524, -0.01648561,
        0.10515259, -0.03548458, -0.01883949,  0.01096003])
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([ 0.07459086, -0.1721484 ,  0.00228618,  0.03643524,  0.01648561,
       -0.10515259,  0.03548458,  0.01883949, -0.01096003])
2025-07-15 23:32:36 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([-0.05933057,  0.19382941, -0.11660964,  0.012809  ,  0.00110481,
        0.00358943,  0.21373252, -0.00825954,  0.02647254])
2025-07-15 23:32:36 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:36 - shap - INFO - phi = array([ 0.05933057, -0.19382941,  0.11660964, -0.012809  , -0.00110481,
       -0.00358943, -0.21373252,  0.00825954, -0.02647254])
2025-07-15 23:32:36 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([-0.03655767,  0.06224378,  0.0217304 ,  0.01024654,  0.00280482,
       -0.03419591,  0.2311743 , -0.02426821,  0.00699513])
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([ 0.03655767, -0.06224378, -0.0217304 , -0.01024654, -0.00280482,
        0.03419591, -0.2311743 ,  0.02426821, -0.00699513])
2025-07-15 23:32:37 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([-0.02235272,  0.12725276, -0.01937703,  0.01396414,  0.00500663,
       -0.00298863,  0.13817538,  0.00077516,  0.03884286])
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([ 0.02235272, -0.12725276,  0.01937703, -0.01396414, -0.00500663,
        0.00298863, -0.13817538, -0.00077516, -0.03884286])
2025-07-15 23:32:37 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([ 0.05552254,  0.11402112,  0.08888608, -0.00094235, -0.01104934,
       -0.01693722,  0.12972752, -0.00315128, -0.01023588])
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([-0.05552254, -0.11402112, -0.08888608,  0.00094235,  0.01104934,
        0.01693722, -0.12972752,  0.00315128,  0.01023588])
2025-07-15 23:32:37 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([-0.06077845, -0.09356102,  0.0194378 , -0.03083039,  0.01897005,
       -0.04328504, -0.29315684, -0.02204995,  0.00449036])
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([ 0.06077845,  0.09356102, -0.0194378 ,  0.03083039, -0.01897005,
        0.04328504,  0.29315684,  0.02204995, -0.00449036])
2025-07-15 23:32:37 - shap - INFO - num_full_subsets = 4
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([ 0.07698689,  0.09687131,  0.07277634,  0.00226482, -0.00836045,
        0.02383278,  0.10851637, -0.01011927,  0.00975716])
2025-07-15 23:32:37 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 23:32:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 23:32:37 - shap - INFO - phi = array([-0.07698689, -0.09687131, -0.07277634, -0.00226482,  0.00836045,
       -0.02383278, -0.10851637,  0.01011927, -0.00975716])
2025-07-15 23:32:41 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-15 23:32:41 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 23:32:41 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 23:32:41 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 23:32:41 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 23:32:46 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 23:32:46 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-15 23:32:46 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
