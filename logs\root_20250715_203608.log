2025-07-15 20:36:10 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:36:10 - GUI - INFO - GUI界面初始化完成
2025-07-15 20:36:48 - model_ensemble - INFO - ============================================================
2025-07-15 20:36:48 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:36:48 - model_ensemble - INFO - ============================================================
2025-07-15 20:36:48 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 20:36:48 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:36:48 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:36:48 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:36:48 - model_training - INFO - 模型名称: Random Forest
2025-07-15 20:36:48 - model_training - INFO - 准确率: 0.8250
2025-07-15 20:36:48 - model_training - INFO - AUC: 0.9015
2025-07-15 20:36:48 - model_training - INFO - 混淆矩阵:
2025-07-15 20:36:48 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-15 20:36:48 - model_training - INFO - 
分类报告:
2025-07-15 20:36:48 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-15 20:36:48 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 20:36:48 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 20:36:48 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 20:36:48 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:36:48 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:36:48 - model_training - INFO - 模型名称: XGBoost
2025-07-15 20:36:48 - model_training - INFO - 准确率: 0.7750
2025-07-15 20:36:48 - model_training - INFO - AUC: 0.8568
2025-07-15 20:36:48 - model_training - INFO - 混淆矩阵:
2025-07-15 20:36:48 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-15 20:36:48 - model_training - INFO - 
分类报告:
2025-07-15 20:36:48 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-15 20:36:48 - model_training - INFO - 训练时间: 0.05 秒
2025-07-15 20:36:48 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 20:36:48 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 20:36:48 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:36:48 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:36:50 - model_training - INFO - 模型名称: LightGBM
2025-07-15 20:36:50 - model_training - INFO - 准确率: 0.7500
2025-07-15 20:36:50 - model_training - INFO - AUC: 0.8568
2025-07-15 20:36:50 - model_training - INFO - 混淆矩阵:
2025-07-15 20:36:50 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-07-15 20:36:50 - model_training - INFO - 
分类报告:
2025-07-15 20:36:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-07-15 20:36:50 - model_training - INFO - 训练时间: 1.67 秒
2025-07-15 20:36:50 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 20:36:50 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 20:36:50 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:36:50 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 20:36:50 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 20:36:50 - model_training - INFO - 准确率: 0.8250
2025-07-15 20:36:50 - model_training - INFO - AUC: 0.9028
2025-07-15 20:36:50 - model_training - INFO - 混淆矩阵:
2025-07-15 20:36:50 - model_training - INFO - 
[[19  4]
 [ 3 14]]
2025-07-15 20:36:50 - model_training - INFO - 
分类报告:
2025-07-15 20:36:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.83      0.84        23
           1       0.78      0.82      0.80        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.83      0.82      0.83        40

2025-07-15 20:36:50 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 20:36:50 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 20:36:50 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 20:36:50 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 20:36:50 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:36:50 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:36:50 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:36:50 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:36:50 - model_ensemble - INFO -     voting_soft - 准确率: 0.8000, F1: 0.8000
2025-07-15 20:36:50 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:36:50 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:36:51 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:36:51 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:36:51 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:36:53 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-15 20:36:53 - model_ensemble - INFO - ============================================================
2025-07-15 20:36:53 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:36:53 - model_ensemble - INFO - ============================================================
2025-07-15 20:36:53 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 20:36:53 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-15 20:36:53 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-15 20:36:53 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:36:53 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.8000, AUC: 0.8900
2025-07-15 20:36:53 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-15 20:36:53 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:36:53 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_203653.joblib
