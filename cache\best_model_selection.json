{"strategy": "balanced", "best_model": "XGBoost", "best_score": 0.9735009542358032, "top_models": [["XGBoost", 0.9735009542358032], ["RandomForest", 0.9691999999999998], ["LightGBM", 0.9690254485969043], ["KNN", 0.8640920716112532], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.8464332330829013]], "all_scores": {"DecisionTree": 0.8063384328182787, "RandomForest": 0.9691999999999998, "XGBoost": 0.9735009542358032, "LightGBM": 0.9690254485969043, "CatBoost": 0.7606137411675444, "Logistic": 0.7986844891703432, "SVM": 0.795312436923646, "KNN": 0.8640920716112532, "NaiveBayes": 0.8464332330829013, "NeuralNet": 0.8244309462915602}, "detailed_metrics": {"DecisionTree": {"accuracy": 0.825, "precision": 0.75, "recall": 0.8823529411764706, "f1_score": 0.8108108108108109, "mcc": 0.6574382586514258, "kappa": 0.65, "auc_roc": 0.9028132992327367, "auc_pr": 0.8174291938997822, "specificity": 0.782608695652174, "sensitivity": 0.8823529411764706, "balanced_accuracy": 0.8324808184143222, "interpretability_score": 1.0}, "RandomForest": {"accuracy": 0.97, "precision": 0.97, "recall": 0.97, "f1_score": 0.97, "mcc": 0.94, "kappa": 0.94, "auc_roc": 0.9908, "auc_pr": 0.9926509011550864, "specificity": 0.97, "sensitivity": 0.97, "balanced_accuracy": 0.97, "interpretability_score": 0.8}, "XGBoost": {"accuracy": 0.975, "precision": 0.9611650485436893, "recall": 0.99, "f1_score": 0.9753694581280788, "mcc": 0.9504277887790924, "kappa": 0.95, "auc_roc": 0.9878999999999999, "auc_pr": 0.9928565350209123, "specificity": 0.96, "sensitivity": 0.99, "balanced_accuracy": 0.975, "interpretability_score": 0.7}, "LightGBM": {"accuracy": 0.97, "precision": 0.9607843137254902, "recall": 0.98, "f1_score": 0.9702970297029703, "mcc": 0.9401880564188067, "kappa": 0.94, "auc_roc": 0.9894000000000001, "auc_pr": 0.9937961669262343, "specificity": 0.96, "sensitivity": 0.98, "balanced_accuracy": 0.97, "interpretability_score": 0.7}, "CatBoost": {"accuracy": 0.8, "precision": 0.8, "recall": 0.7058823529411765, "f1_score": 0.75, "mcc": 0.5875955600576713, "kappa": 0.5844155844155844, "auc_roc": 0.8900255754475703, "auc_pr": 0.8633765165700824, "specificity": 0.8695652173913043, "sensitivity": 0.7058823529411765, "balanced_accuracy": 0.7877237851662404, "interpretability_score": 0.7}, "Logistic": {"accuracy": 0.825, "precision": 0.7777777777777778, "recall": 0.8235294117647058, "f1_score": 0.8, "mcc": 0.6455022270395531, "kappa": 0.6446700507614214, "auc_roc": 0.9028132992327366, "auc_pr": 0.8880515005947531, "specificity": 0.8260869565217391, "sensitivity": 0.8235294117647058, "balanced_accuracy": 0.8248081841432224, "interpretability_score": 0.9}, "SVM": {"accuracy": 0.825, "precision": 0.8125, "recall": 0.7647058823529411, "f1_score": 0.7878787878787878, "mcc": 0.6400261077368838, "kappa": 0.6391752577319587, "auc_roc": 0.9130434782608696, "auc_pr": 0.8835291832350656, "specificity": 0.8695652173913043, "sensitivity": 0.7647058823529411, "balanced_accuracy": 0.8171355498721227, "interpretability_score": 0.4}, "KNN": {"accuracy": 0.9, "precision": 0.8823529411764706, "recall": 0.8823529411764706, "f1_score": 0.8823529411764706, "mcc": 0.7953964194373402, "kappa": 0.7953964194373402, "auc_roc": 0.8682864450127877, "auc_pr": 0.8465109573241062, "specificity": 0.9130434782608695, "sensitivity": 0.8823529411764706, "balanced_accuracy": 0.8976982097186701, "interpretability_score": 0.6}, "NaiveBayes": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "auc_roc": 0.907928388746803, "auc_pr": 0.9045862904249176, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "balanced_accuracy": 0.8682864450127876, "interpretability_score": 0.7}, "NeuralNet": {"accuracy": 0.85, "precision": 0.8235294117647058, "recall": 0.8235294117647058, "f1_score": 0.8235294117647058, "mcc": 0.6930946291560103, "kappa": 0.6930946291560103, "auc_roc": 0.9156010230179028, "auc_pr": 0.9167677813787871, "specificity": 0.8695652173913043, "sensitivity": 0.8235294117647058, "balanced_accuracy": 0.8465473145780051, "interpretability_score": 0.3}}, "selection_reasoning": "推荐 XGBoost 作为最佳模型的理由：\n\n1. 在平衡策略下，该模型获得了最高的综合得分 0.9735\n2. 在多个关键指标上表现均衡：\n   - AUC-ROC: 0.9879\n   - F1分数: 0.9754\n   - MCC: 0.9504\n\n4. 该模型在二分类任务中展现出了优秀的综合性能"}