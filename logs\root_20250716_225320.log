2025-07-16 22:53:21 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.116
2025-07-16 22:53:21 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:21 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.140
2025-07-16 22:53:21 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:21 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.103
2025-07-16 22:53:21 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:21 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.7542
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.8207
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[86 33]
 [26 95]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.77      0.72      0.74       119
           1       0.74      0.79      0.76       121

    accuracy                           0.75       240
   macro avg       0.76      0.75      0.75       240
weighted avg       0.75      0.75      0.75       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7708
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:21 - model_training - INFO - 模型名称: Random Forest
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.9125
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.9747
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[116   3]
 [ 18 103]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.97      0.92       119
           1       0.97      0.85      0.91       121

    accuracy                           0.91       240
   macro avg       0.92      0.91      0.91       240
weighted avg       0.92      0.91      0.91       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.17 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9288
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:21 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.8125
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.9008
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[99 20]
 [25 96]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.83      0.81       119
           1       0.83      0.79      0.81       121

    accuracy                           0.81       240
   macro avg       0.81      0.81      0.81       240
weighted avg       0.81      0.81      0.81       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8346
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:21 - model_training - INFO - 模型名称: SVM
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.9417
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.9811
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[116   3]
 [ 11 110]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.97      0.94       119
           1       0.97      0.91      0.94       121

    accuracy                           0.94       240
   macro avg       0.94      0.94      0.94       240
weighted avg       0.94      0.94      0.94       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.04 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9518
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 22:53:21 - model_training - INFO - 模型名称: XGBoost
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.9083
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.9708
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[114   5]
 [ 17 104]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.96      0.91       119
           1       0.95      0.86      0.90       121

    accuracy                           0.91       240
   macro avg       0.91      0.91      0.91       240
weighted avg       0.91      0.91      0.91       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.16 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.9244
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 模型多样性矩阵计算完成
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.512, 多样性=0.488
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.437, 多样性=0.563
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.498, 多样性=0.502
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.551, 多样性=0.449
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.701, 多样性=0.299
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.892, 多样性=0.108
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.874, 多样性=0.126
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.691, 多样性=0.309
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.625, 多样性=0.375
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.900, 多样性=0.100
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.6770
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:21 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.7542
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.8207
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[86 33]
 [26 95]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.77      0.72      0.74       119
           1       0.74      0.79      0.76       121

    accuracy                           0.75       240
   macro avg       0.76      0.75      0.75       240
weighted avg       0.75      0.75      0.75       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7708
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:21 - model_training - INFO - 模型名称: Random Forest
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.9125
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.9747
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[116   3]
 [ 18 103]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.97      0.92       119
           1       0.97      0.85      0.91       121

    accuracy                           0.91       240
   macro avg       0.92      0.91      0.91       240
weighted avg       0.92      0.91      0.91       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.20 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9288
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:21 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.8125
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.9008
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[99 20]
 [25 96]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.83      0.81       119
           1       0.83      0.79      0.81       121

    accuracy                           0.81       240
   macro avg       0.81      0.81      0.81       240
weighted avg       0.81      0.81      0.81       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8346
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:21 - model_training - INFO - 模型名称: SVM
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.9417
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.9811
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[116   3]
 [ 11 110]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.97      0.94       119
           1       0.97      0.91      0.94       121

    accuracy                           0.94       240
   macro avg       0.94      0.94      0.94       240
weighted avg       0.94      0.94      0.94       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.04 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9518
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 22:53:21 - model_training - INFO - 模型名称: XGBoost
2025-07-16 22:53:21 - model_training - INFO - 准确率: 0.9083
2025-07-16 22:53:21 - model_training - INFO - AUC: 0.9708
2025-07-16 22:53:21 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:21 - model_training - INFO - 
[[114   5]
 [ 17 104]]
2025-07-16 22:53:21 - model_training - INFO - 
分类报告:
2025-07-16 22:53:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.96      0.91       119
           1       0.95      0.86      0.90       121

    accuracy                           0.91       240
   macro avg       0.91      0.91      0.91       240
weighted avg       0.91      0.91      0.91       240

2025-07-16 22:53:21 - model_training - INFO - 训练时间: 0.08 秒
2025-07-16 22:53:21 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.9244
2025-07-16 22:53:21 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 22:53:21 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.103
2025-07-16 22:53:21 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:22 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.279
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.103
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.279
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.737, 多样性=0.263
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.602, 多样性=0.398
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.616, 多样性=0.384
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.679, 多样性=0.321
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.850, 多样性=0.150
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.921, 多样性=0.079
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.920, 多样性=0.080
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.820, 多样性=0.180
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.803, 多样性=0.197
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.938, 多样性=0.062
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.378
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.300
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 3, 'linear': 2}
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 综合得分: 0.6357
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:22 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 22:53:22 - model_training - INFO - 准确率: 0.8800
2025-07-16 22:53:22 - model_training - INFO - AUC: 0.9032
2025-07-16 22:53:22 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:22 - model_training - INFO - 
[[65 10]
 [ 8 67]]
2025-07-16 22:53:22 - model_training - INFO - 
分类报告:
2025-07-16 22:53:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.89      0.87      0.88        75
           1       0.87      0.89      0.88        75

    accuracy                           0.88       150
   macro avg       0.88      0.88      0.88       150
weighted avg       0.88      0.88      0.88       150

2025-07-16 22:53:22 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 22:53:22 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8858
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:22 - model_training - INFO - 模型名称: Random Forest
2025-07-16 22:53:22 - model_training - INFO - 准确率: 0.9267
2025-07-16 22:53:22 - model_training - INFO - AUC: 0.9852
2025-07-16 22:53:22 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:22 - model_training - INFO - 
[[68  7]
 [ 4 71]]
2025-07-16 22:53:22 - model_training - INFO - 
分类报告:
2025-07-16 22:53:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.91      0.93        75
           1       0.91      0.95      0.93        75

    accuracy                           0.93       150
   macro avg       0.93      0.93      0.93       150
weighted avg       0.93      0.93      0.93       150

2025-07-16 22:53:22 - model_training - INFO - 训练时间: 0.14 秒
2025-07-16 22:53:22 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9414
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:22 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 22:53:22 - model_training - INFO - 准确率: 0.8533
2025-07-16 22:53:22 - model_training - INFO - AUC: 0.9372
2025-07-16 22:53:22 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:22 - model_training - INFO - 
[[61 14]
 [ 8 67]]
2025-07-16 22:53:22 - model_training - INFO - 
分类报告:
2025-07-16 22:53:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.81      0.85        75
           1       0.83      0.89      0.86        75

    accuracy                           0.85       150
   macro avg       0.86      0.85      0.85       150
weighted avg       0.86      0.85      0.85       150

2025-07-16 22:53:22 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:53:22 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8745
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:22 - model_training - INFO - 模型名称: SVM
2025-07-16 22:53:22 - model_training - INFO - 准确率: 0.9600
2025-07-16 22:53:22 - model_training - INFO - AUC: 0.9947
2025-07-16 22:53:22 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:22 - model_training - INFO - 
[[71  4]
 [ 2 73]]
2025-07-16 22:53:22 - model_training - INFO - 
分类报告:
2025-07-16 22:53:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.95      0.96        75
           1       0.95      0.97      0.96        75

    accuracy                           0.96       150
   macro avg       0.96      0.96      0.96       150
weighted avg       0.96      0.96      0.96       150

2025-07-16 22:53:22 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 22:53:22 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9687
2025-07-16 22:53:22 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 22:53:22 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.116
2025-07-16 22:53:22 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:22 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.215
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.116
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.215
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.895, 多样性=0.105
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.816, 多样性=0.184
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.820, 多样性=0.180
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.875, 多样性=0.125
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.951, 多样性=0.049
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.854, 多样性=0.146
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.164
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 综合得分: 0.6192
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:22 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 22:53:22 - model_training - INFO - 准确率: 0.6533
2025-07-16 22:53:22 - model_training - INFO - AUC: 0.6899
2025-07-16 22:53:22 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:22 - model_training - INFO - 
[[108  43]
 [ 61  88]]
2025-07-16 22:53:22 - model_training - INFO - 
分类报告:
2025-07-16 22:53:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.64      0.72      0.68       151
           1       0.67      0.59      0.63       149

    accuracy                           0.65       300
   macro avg       0.66      0.65      0.65       300
weighted avg       0.66      0.65      0.65       300

2025-07-16 22:53:22 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 22:53:22 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.6624
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:22 - model_training - INFO - 模型名称: Random Forest
2025-07-16 22:53:22 - model_training - INFO - 准确率: 0.7767
2025-07-16 22:53:22 - model_training - INFO - AUC: 0.8584
2025-07-16 22:53:22 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:22 - model_training - INFO - 
[[125  26]
 [ 41 108]]
2025-07-16 22:53:22 - model_training - INFO - 
分类报告:
2025-07-16 22:53:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.75      0.83      0.79       151
           1       0.81      0.72      0.76       149

    accuracy                           0.78       300
   macro avg       0.78      0.78      0.78       300
weighted avg       0.78      0.78      0.78       300

2025-07-16 22:53:22 - model_training - INFO - 训练时间: 0.34 秒
2025-07-16 22:53:22 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.7973
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:22 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 22:53:22 - model_training - INFO - 准确率: 0.6733
2025-07-16 22:53:22 - model_training - INFO - AUC: 0.7176
2025-07-16 22:53:22 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:22 - model_training - INFO - 
[[ 96  55]
 [ 43 106]]
2025-07-16 22:53:22 - model_training - INFO - 
分类报告:
2025-07-16 22:53:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.69      0.64      0.66       151
           1       0.66      0.71      0.68       149

    accuracy                           0.67       300
   macro avg       0.67      0.67      0.67       300
weighted avg       0.67      0.67      0.67       300

2025-07-16 22:53:22 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:53:22 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.6844
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:22 - model_training - INFO - 模型名称: SVM
2025-07-16 22:53:22 - model_training - INFO - 准确率: 0.8500
2025-07-16 22:53:22 - model_training - INFO - AUC: 0.9256
2025-07-16 22:53:22 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:22 - model_training - INFO - 
[[125  26]
 [ 19 130]]
2025-07-16 22:53:22 - model_training - INFO - 
分类报告:
2025-07-16 22:53:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.83      0.85       151
           1       0.83      0.87      0.85       149

    accuracy                           0.85       300
   macro avg       0.85      0.85      0.85       300
weighted avg       0.85      0.85      0.85       300

2025-07-16 22:53:22 - model_training - INFO - 训练时间: 0.18 秒
2025-07-16 22:53:22 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8690
2025-07-16 22:53:22 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 22:53:22 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.140
2025-07-16 22:53:22 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:23 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.529
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.140
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.529
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.605, 多样性=0.395
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.407, 多样性=0.593
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.429, 多样性=0.571
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.614, 多样性=0.386
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.806, 多样性=0.194
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.528, 多样性=0.472
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.705
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 综合得分: 0.6463
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:23 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 22:53:23 - model_training - INFO - 准确率: 0.7542
2025-07-16 22:53:23 - model_training - INFO - AUC: 0.8207
2025-07-16 22:53:23 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:23 - model_training - INFO - 
[[86 33]
 [26 95]]
2025-07-16 22:53:23 - model_training - INFO - 
分类报告:
2025-07-16 22:53:23 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.77      0.72      0.74       119
           1       0.74      0.79      0.76       121

    accuracy                           0.75       240
   macro avg       0.76      0.75      0.75       240
weighted avg       0.75      0.75      0.75       240

2025-07-16 22:53:23 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:53:23 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7708
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:23 - model_training - INFO - 模型名称: Random Forest
2025-07-16 22:53:23 - model_training - INFO - 准确率: 0.9125
2025-07-16 22:53:23 - model_training - INFO - AUC: 0.9747
2025-07-16 22:53:23 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:23 - model_training - INFO - 
[[116   3]
 [ 18 103]]
2025-07-16 22:53:23 - model_training - INFO - 
分类报告:
2025-07-16 22:53:23 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.97      0.92       119
           1       0.97      0.85      0.91       121

    accuracy                           0.91       240
   macro avg       0.92      0.91      0.91       240
weighted avg       0.92      0.91      0.91       240

2025-07-16 22:53:23 - model_training - INFO - 训练时间: 0.17 秒
2025-07-16 22:53:23 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9288
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:23 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 22:53:23 - model_training - INFO - 准确率: 0.8125
2025-07-16 22:53:23 - model_training - INFO - AUC: 0.9008
2025-07-16 22:53:23 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:23 - model_training - INFO - 
[[99 20]
 [25 96]]
2025-07-16 22:53:23 - model_training - INFO - 
分类报告:
2025-07-16 22:53:23 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.83      0.81       119
           1       0.83      0.79      0.81       121

    accuracy                           0.81       240
   macro avg       0.81      0.81      0.81       240
weighted avg       0.81      0.81      0.81       240

2025-07-16 22:53:23 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:53:23 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8346
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:23 - model_training - INFO - 模型名称: SVM
2025-07-16 22:53:23 - model_training - INFO - 准确率: 0.9417
2025-07-16 22:53:23 - model_training - INFO - AUC: 0.9811
2025-07-16 22:53:23 - model_training - INFO - 混淆矩阵:
2025-07-16 22:53:23 - model_training - INFO - 
[[116   3]
 [ 11 110]]
2025-07-16 22:53:23 - model_training - INFO - 
分类报告:
2025-07-16 22:53:23 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.97      0.94       119
           1       0.97      0.91      0.94       121

    accuracy                           0.94       240
   macro avg       0.94      0.94      0.94       240
weighted avg       0.94      0.94      0.94       240

2025-07-16 22:53:23 - model_training - INFO - 训练时间: 0.03 秒
2025-07-16 22:53:23 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9518
2025-07-16 22:53:23 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-07-16 22:53:23 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.103
2025-07-16 22:53:23 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:23 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.359
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.103
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.359
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.737, 多样性=0.263
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.602, 多样性=0.398
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.616, 多样性=0.384
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.850, 多样性=0.150
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.921, 多样性=0.079
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.820, 多样性=0.180
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.458
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 综合得分: 0.6357
