2025-07-15 23:11:55 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 23:11:55 - GUI - INFO - GUI界面初始化完成
2025-07-15 23:12:16 - model_ensemble - INFO - ============================================================
2025-07-15 23:12:16 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 23:12:16 - model_ensemble - INFO - ============================================================
2025-07-15 23:12:16 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 23:12:16 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-15 23:12:16 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 23:12:16 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 23:12:16 - model_training - INFO - 模型名称: Random Forest
2025-07-15 23:12:16 - model_training - INFO - 准确率: 0.9000
2025-07-15 23:12:16 - model_training - INFO - AUC: 0.9386
2025-07-15 23:12:16 - model_training - INFO - 混淆矩阵:
2025-07-15 23:12:16 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-07-15 23:12:16 - model_training - INFO - 
分类报告:
2025-07-15 23:12:16 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-15 23:12:16 - model_training - INFO - 训练时间: 0.08 秒
2025-07-15 23:12:16 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 23:12:16 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 23:12:16 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 23:12:16 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 23:12:16 - model_training - INFO - 模型名称: XGBoost
2025-07-15 23:12:16 - model_training - INFO - 准确率: 0.8750
2025-07-15 23:12:16 - model_training - INFO - AUC: 0.9668
2025-07-15 23:12:16 - model_training - INFO - 混淆矩阵:
2025-07-15 23:12:16 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 23:12:16 - model_training - INFO - 
分类报告:
2025-07-15 23:12:16 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 23:12:16 - model_training - INFO - 训练时间: 0.10 秒
2025-07-15 23:12:16 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 23:12:16 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 23:12:16 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 23:12:16 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 23:12:18 - model_training - INFO - 模型名称: LightGBM
2025-07-15 23:12:18 - model_training - INFO - 准确率: 0.8750
2025-07-15 23:12:18 - model_training - INFO - AUC: 0.9463
2025-07-15 23:12:18 - model_training - INFO - 混淆矩阵:
2025-07-15 23:12:18 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-15 23:12:18 - model_training - INFO - 
分类报告:
2025-07-15 23:12:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-15 23:12:18 - model_training - INFO - 训练时间: 1.60 秒
2025-07-15 23:12:18 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 23:12:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 23:12:18 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 23:12:18 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 23:12:18 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 23:12:18 - model_training - INFO - 准确率: 0.8500
2025-07-15 23:12:18 - model_training - INFO - AUC: 0.9284
2025-07-15 23:12:18 - model_training - INFO - 混淆矩阵:
2025-07-15 23:12:18 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-07-15 23:12:18 - model_training - INFO - 
分类报告:
2025-07-15 23:12:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-07-15 23:12:18 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 23:12:18 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 23:12:18 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-15 23:12:18 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 23:12:18 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 23:12:18 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 23:12:18 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 23:12:20 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 23:12:20 - model_ensemble - INFO - ============================================================
2025-07-15 23:12:20 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 23:12:20 - model_ensemble - INFO - ============================================================
2025-07-15 23:12:20 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 23:12:20 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 23:12:20 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 23:12:20 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 23:12:20 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 23:12:20 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 23:12:20 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_231220.joblib
2025-07-15 23:12:20 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 23:12:20 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-07-15 23:12:20 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 23:12:20 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 23:12:20 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 23:12:20 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 23:12:20 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.12824845,  0.11018386, -0.01648603, -0.01878804,  0.00182537,
        0.12380315,  0.02001933, -0.01076704])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.12824845, -0.11018386,  0.01648603,  0.01878804, -0.00182537,
       -0.12380315, -0.02001933,  0.01076704])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.16842909, -0.08155484, -0.0349794 , -0.06420511,  0.02032457,
        0.05707634, -0.03842121, -0.01170643])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.16842909,  0.08155484,  0.0349794 ,  0.06420511, -0.02032457,
       -0.05707634,  0.03842121,  0.01170643])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.11308959, -0.09256786, -0.01727368, -0.02882095, -0.01823232,
       -0.28455334,  0.00798987, -0.01435149])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.11308959,  0.09256786,  0.01727368,  0.02882095,  0.01823232,
        0.28455334, -0.00798987,  0.01435149])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.1438788 , -0.01954911,  0.00556806, -0.05431083, -0.02117029,
       -0.32309042,  0.01836389, -0.02031651])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.1438788 ,  0.01954911, -0.00556806,  0.05431083,  0.02117029,
        0.32309042, -0.01836389,  0.02031651])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.15403861,  0.14843487, -0.01973529, -0.03169079, -0.01762651,
        0.00799604,  0.09309064, -0.02738599])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.15403861, -0.14843487,  0.01973529,  0.03169079,  0.01762651,
       -0.00799604, -0.09309064,  0.02738599])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.08386293,  0.1203865 ,  0.01036275,  0.08729312,  0.02327847,
        0.01729195, -0.02107206,  0.02875282])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.08386293, -0.1203865 , -0.01036275, -0.08729312, -0.02327847,
       -0.01729195,  0.02107206, -0.02875282])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.21311505, -0.05185957, -0.00913313, -0.03663836,  0.0020322 ,
        0.21032307, -0.01408276, -0.00979545])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.21311505,  0.05185957,  0.00913313,  0.03663836, -0.0020322 ,
       -0.21032307,  0.01408276,  0.00979545])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.20811245, -0.05953932, -0.01156202, -0.04574816, -0.02261527,
        0.11340285,  0.06593942, -0.02928707])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.20811245,  0.05953932,  0.01156202,  0.04574816,  0.02261527,
       -0.11340285, -0.06593942,  0.02928707])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.23392214, -0.2256735 ,  0.00290093, -0.07053501, -0.01577436,
        0.01179162,  0.01677976, -0.02707824])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.23392214,  0.2256735 , -0.00290093,  0.07053501,  0.01577436,
       -0.01179162, -0.01677976,  0.02707824])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.08140024,  0.17488925,  0.00710466, -0.03076399,  0.03112765,
       -0.41301049, -0.03501421,  0.00059816])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.08140024, -0.17488925, -0.00710466,  0.03076399, -0.03112765,
        0.41301049,  0.03501421, -0.00059816])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.07054917,  0.1537069 ,  0.00466355, -0.03563432,  0.00196401,
        0.15851307,  0.00622511, -0.0115623 ])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.07054917, -0.1537069 , -0.00466355,  0.03563432, -0.00196401,
       -0.15851307, -0.00622511,  0.0115623 ])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.10456796,  0.1289086 , -0.00187561, -0.03164679,  0.00072738,
        0.16821861, -0.00859955, -0.00899175])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.10456796, -0.1289086 ,  0.00187561,  0.03164679, -0.00072738,
       -0.16821861,  0.00859955,  0.00899175])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([-0.20507514, -0.24928164, -0.03651573, -0.05151084, -0.02387816,
        0.02027108,  0.0456697 , -0.03591999])
2025-07-15 23:12:20 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:20 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:20 - shap - INFO - phi = array([ 0.20507514,  0.24928164,  0.03651573,  0.05151084,  0.02387816,
       -0.02027108, -0.0456697 ,  0.03591999])
2025-07-15 23:12:20 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.11139511, -0.12477805, -0.01785809, -0.03196101, -0.02120437,
       -0.26338559,  0.02715089, -0.01870511])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.11139511,  0.12477805,  0.01785809,  0.03196101,  0.02120437,
        0.26338559, -0.02715089,  0.01870511])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.31788853, -0.21001148, -0.0008943 , -0.05067315, -0.01259617,
        0.06716916,  0.01884352, -0.02334283])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.31788853,  0.21001148,  0.0008943 ,  0.05067315,  0.01259617,
       -0.06716916, -0.01884352,  0.02334283])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.17362832, -0.04310968,  0.01896638,  0.08827732,  0.02616542,
        0.05118761, -0.02293291,  0.02100061])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.17362832,  0.04310968, -0.01896638, -0.08827732, -0.02616542,
       -0.05118761,  0.02293291, -0.02100061])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.11820028,  0.13242507, -0.0108597 , -0.02133324, -0.00704689,
        0.13167593,  0.01468083, -0.01110438])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.11820028, -0.13242507,  0.0108597 ,  0.02133324,  0.00704689,
       -0.13167593, -0.01468083,  0.01110438])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.11544889,  0.15604121, -0.01515181, -0.00834804,  0.01330446,
        0.09204102,  0.00698448, -0.00880434])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.11544889, -0.15604121,  0.01515181,  0.00834804, -0.01330446,
       -0.09204102, -0.00698448,  0.00880434])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.20713723, -0.05850747,  0.01576743,  0.04310671,  0.02554026,
        0.07961092, -0.02079478, -0.01723248])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.20713723,  0.05850747, -0.01576743, -0.04310671, -0.02554026,
       -0.07961092,  0.02079478,  0.01723248])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.12952023, -0.16471915,  0.03933346,  0.06966338, -0.01575533,
        0.02204018, -0.04720557,  0.06334956])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.12952023,  0.16471915, -0.03933346, -0.06966338,  0.01575533,
       -0.02204018,  0.04720557, -0.06334956])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.15656985, -0.13441087,  0.03207708,  0.16470434,  0.02133001,
       -0.17788722, -0.03623172,  0.02760877])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.15656985,  0.13441087, -0.03207708, -0.16470434, -0.02133001,
        0.17788722,  0.03623172, -0.02760877])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.05274834,  0.12408541,  0.03018978,  0.18033371,  0.03146856,
       -0.0058091 , -0.03029123,  0.0303651 ])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.05274834, -0.12408541, -0.03018978, -0.18033371, -0.03146856,
        0.0058091 ,  0.03029123, -0.0303651 ])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.0980972 ,  0.14098638,  0.00811403,  0.08347599,  0.02492024,
        0.02561104, -0.0232825 , -0.00725092])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.0980972 , -0.14098638, -0.00811403, -0.08347599, -0.02492024,
       -0.02561104,  0.0232825 ,  0.00725092])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.11222962,  0.147917  , -0.00754361, -0.02508523,  0.00206138,
        0.11563921,  0.01451138, -0.01042952])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.11222962, -0.147917  ,  0.00754361,  0.02508523, -0.00206138,
       -0.11563921, -0.01451138,  0.01042952])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.12816144, -0.09588338, -0.014297  , -0.03578429, -0.02114705,
       -0.27519537,  0.02492631, -0.0165333 ])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.12816144,  0.09588338,  0.014297  ,  0.03578429,  0.02114705,
        0.27519537, -0.02492631,  0.0165333 ])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.08806835,  0.11185955,  0.00910389,  0.07298346,  0.02274104,
        0.04379808, -0.01934083,  0.02182429])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.08806835, -0.11185955, -0.00910389, -0.07298346, -0.02274104,
       -0.04379808,  0.01934083, -0.02182429])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.17087181, -0.14767359,  0.00336899, -0.06845938, -0.02102379,
       -0.15100887,  0.01266139, -0.01239839])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.17087181,  0.14767359, -0.00336899,  0.06845938,  0.02102379,
        0.15100887, -0.01266139,  0.01239839])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.13650484,  0.11399197, -0.0194294 , -0.0094213 ,  0.01143192,
        0.11513432,  0.00333032, -0.00689225])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.13650484, -0.11399197,  0.0194294 ,  0.0094213 , -0.01143192,
       -0.11513432, -0.00333032,  0.00689225])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.06253083, -0.08861203,  0.00517919, -0.00534932, -0.02570195,
       -0.48482088,  0.01501494, -0.01347447])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.06253083,  0.08861203, -0.00517919,  0.00534932,  0.02570195,
        0.48482088, -0.01501494,  0.01347447])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.27400412, -0.23837143, -0.00264971, -0.08689676, -0.02037086,
        0.13227658,  0.0174054 , -0.03167416])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.27400412,  0.23837143,  0.00264971,  0.08689676,  0.02037086,
       -0.13227658, -0.0174054 ,  0.03167416])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.10673374,  0.13513611, -0.00725749, -0.02586128,  0.00238693,
        0.15431897, -0.00826421, -0.00467935])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.10673374, -0.13513611,  0.00725749,  0.02586128, -0.00238693,
       -0.15431897,  0.00826421,  0.00467935])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.22009503, -0.24478831, -0.02807661, -0.0638124 , -0.02773168,
        0.09038746,  0.01488169, -0.03032833])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.22009503,  0.24478831,  0.02807661,  0.0638124 ,  0.02773168,
       -0.09038746, -0.01488169,  0.03032833])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([-0.06814821, -0.07039998,  0.04948161,  0.2458803 ,  0.0245252 ,
       -0.01921155, -0.03540433,  0.11900684])
2025-07-15 23:12:21 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:21 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:21 - shap - INFO - phi = array([ 0.06814821,  0.07039998, -0.04948161, -0.2458803 , -0.0245252 ,
        0.01921155,  0.03540433, -0.11900684])
2025-07-15 23:12:21 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([-0.06067159,  0.2695458 ,  0.01850565, -0.00416176,  0.01975023,
        0.08549763, -0.01679557, -0.01213007])
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([ 0.06067159, -0.2695458 , -0.01850565,  0.00416176, -0.01975023,
       -0.08549763,  0.01679557,  0.01213007])
2025-07-15 23:12:22 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([-0.23059672, -0.16516901, -0.0052167 , -0.07005512, -0.02290822,
        0.19756312, -0.02335802,  0.04012675])
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([ 0.23059672,  0.16516901,  0.0052167 ,  0.07005512,  0.02290822,
       -0.19756312,  0.02335802, -0.04012675])
2025-07-15 23:12:22 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([ 0.08572944,  0.0900528 ,  0.00302055, -0.03942325, -0.00059334,
        0.2138024 , -0.01415807,  0.01133361])
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([-0.08572944, -0.0900528 , -0.00302055,  0.03942325,  0.00059334,
       -0.2138024 ,  0.01415807, -0.01133361])
2025-07-15 23:12:22 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([-2.13283029e-01,  2.36533181e-01,  1.01676270e-02, -3.91588891e-02,
        2.34194883e-04,  1.65472744e-01, -9.58889307e-03,  3.84319404e-02])
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([ 2.13283029e-01, -2.36533181e-01, -1.01676270e-02,  3.91588891e-02,
       -2.34194883e-04, -1.65472744e-01,  9.58889307e-03, -3.84319404e-02])
2025-07-15 23:12:22 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([ 0.09641386,  0.07491052,  0.01037142,  0.08523721,  0.02423098,
        0.04944696, -0.02355016,  0.03072402])
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([-0.09641386, -0.07491052, -0.01037142, -0.08523721, -0.02423098,
       -0.04944696,  0.02355016, -0.03072402])
2025-07-15 23:12:22 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([ 0.08265063, -0.09756945,  0.00992597, -0.01672129, -0.02463115,
       -0.47544898,  0.02211255, -0.00959541])
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([-0.08265063,  0.09756945, -0.00992597,  0.01672129,  0.02463115,
        0.47544898, -0.02211255,  0.00959541])
2025-07-15 23:12:22 - shap - INFO - num_full_subsets = 4
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([ 0.13422252,  0.09403475, -0.01737767, -0.01815664,  0.00863721,
        0.15206071, -0.01819283,  0.0086151 ])
2025-07-15 23:12:22 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 23:12:22 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 23:12:22 - shap - INFO - phi = array([-0.13422252, -0.09403475,  0.01737767,  0.01815664, -0.00863721,
       -0.15206071,  0.01819283, -0.0086151 ])
2025-07-15 23:12:26 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-15 23:12:26 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 23:12:26 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 23:12:26 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 23:12:26 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 23:12:29 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 23:12:29 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-07-15 23:12:29 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
