2025-07-15 20:10:19 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 259, 'max_depth': 20, 'min_samples_split': 4, 'min_samples_leaf': 6, 'max_features': 'log2'}
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9709
2025-07-15 20:10:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250715_201030.html
2025-07-15 20:10:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250715_201030.html
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.26 秒
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 20
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 54, 'max_depth': 8, 'learning_rate': 0.06708292487614473, 'subsample': 0.7268981881756755, 'colsample_bytree': 0.6621441442264917}
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9725
2025-07-15 20:10:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250715_201033.html
2025-07-15 20:10:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250715_201033.html
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.18 秒
2025-07-15 20:10:33 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 20
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 58, 'max_depth': 10, 'learning_rate': 0.12376958094247054, 'feature_fraction': 0.670670773570036, 'bagging_fraction': 0.7177785029988614}
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9676
2025-07-15 20:10:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250715_201035.html
2025-07-15 20:10:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250715_201035.html
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.64 秒
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 20
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 8.751786349278616, 'solver': 'liblinear'}
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9492
2025-07-15 20:10:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250715_201035.html
2025-07-15 20:10:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250715_201035.html
2025-07-15 20:10:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.48 秒
