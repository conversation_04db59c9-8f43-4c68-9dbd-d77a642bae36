2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 初始化二分类分析流程
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 数据文件: sample_data.csv
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 输出目录: output\complete_pipeline
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 超参数调优: 禁用
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - SHAP分析: 禁用
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 🚀 开始完整的二分类分析流程
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 步骤1: 数据预处理
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 数据预处理完成
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 训练集: 800 样本, 20 特征
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 测试集: 200 样本
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 类别分布 - 训练集: 400:400
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 类别分布 - 测试集: 100:100
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 步骤2: 模型训练
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:17 - binary_classification_pipeline - INFO - 训练模型: RandomForest
2025-07-15 20:06:18 - binary_classification_pipeline - INFO -   RandomForest 训练完成
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 训练模型: XGBoost
2025-07-15 20:06:18 - binary_classification_pipeline - INFO -   XGBoost 训练完成
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 训练模型: LightGBM
2025-07-15 20:06:18 - binary_classification_pipeline - INFO -   LightGBM 训练完成
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 模型训练完成，成功训练 3 个模型
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 步骤3: 最佳模型选择
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 最佳模型选择完成
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 推荐模型: XGBoost
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 综合得分: 0.9735
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 步骤4: 集成学习
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - ============================================================
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 使用 3 个模型进行集成学习
2025-07-15 20:06:18 - binary_classification_pipeline - INFO - 模型列表: ['RandomForest', 'XGBoost', 'LightGBM']
2025-07-15 20:06:21 - binary_classification_pipeline - ERROR - 集成学习过程出错: ' font-family'
2025-07-15 20:06:21 - binary_classification_pipeline - ERROR - 集成学习失败，分析终止
