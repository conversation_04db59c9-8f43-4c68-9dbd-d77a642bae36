2025-07-15 22:42:55 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:42:55 - model_ensemble - INFO - ============================================================
2025-07-15 22:42:55 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:42:55 - model_ensemble - INFO - ============================================================
2025-07-15 22:42:55 - model_ensemble - INFO - 基础模型: ['RandomForest']
2025-07-15 22:42:55 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-15 22:42:55 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:42:55 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:42:55 - model_training - INFO - 模型名称: Random Forest
2025-07-15 22:42:55 - model_training - INFO - 准确率: 0.9667
2025-07-15 22:42:55 - model_training - INFO - AUC: 0.9911
2025-07-15 22:42:55 - model_training - INFO - 混淆矩阵:
2025-07-15 22:42:55 - model_training - INFO - 
[[29  1]
 [ 1 29]]
2025-07-15 22:42:55 - model_training - INFO - 
分类报告:
2025-07-15 22:42:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.97      0.97        30
           1       0.97      0.97      0.97        30

    accuracy                           0.97        60
   macro avg       0.97      0.97      0.97        60
weighted avg       0.97      0.97      0.97        60

2025-07-15 22:42:55 - model_training - INFO - 训练时间: 0.10 秒
2025-07-15 22:42:55 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 22:42:55 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 22:42:55 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:42:55 - model_ensemble - INFO - 成功训练了 1 个基础模型
2025-07-15 22:42:55 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:42:55 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:42:55 - model_ensemble - INFO -   stacking - 准确率: 0.9833, F1: 0.9833
2025-07-15 22:42:55 - model_ensemble - INFO - ============================================================
2025-07-15 22:42:55 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:42:55 - model_ensemble - INFO - ============================================================
2025-07-15 22:42:55 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 22:42:55 - model_ensemble - INFO - 最佳F1分数: 0.9833
2025-07-15 22:42:55 - model_ensemble - INFO - 最佳准确率: 0.9833
2025-07-15 22:42:55 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:42:55 - model_ensemble - INFO -   stacking        - 准确率: 0.9833, 精确率: 0.9839, 召回率: 0.9833, F1: 0.9833, AUC: 0.9911
2025-07-15 22:42:55 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:42:55 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_224255.joblib
2025-07-15 22:42:55 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:42:55 - safe_visualization - INFO - Summary report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_summary_report.txt
2025-07-15 22:42:55 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 22:42:55 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:42:55 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:42:55 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 22:42:55 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 22:42:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([-0.19571955, -0.05452925, -0.05890071, -0.00917185, -0.02266796,
       -0.01202321,  0.00420564, -0.05533844, -0.00670574])
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([ 0.19571955,  0.05452925,  0.05890071,  0.00917185,  0.02266796,
        0.01202321, -0.00420564,  0.05533844,  0.00670574])
2025-07-15 22:42:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([ 0.31255476,  0.08392251, -0.00921117,  0.00038646,  0.03304692,
       -0.00738219,  0.00962583,  0.01023576, -0.00360545])
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([-0.31255476, -0.08392251,  0.00921117, -0.00038646, -0.03304692,
        0.00738219, -0.00962583, -0.01023576,  0.00360545])
2025-07-15 22:42:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([-0.20346676, -0.04732362, -0.05289171, -0.00520929, -0.01441926,
       -0.01629057,  0.00460119, -0.07602464, -0.00337768])
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([ 0.20346676,  0.04732362,  0.05289171,  0.00520929,  0.01441926,
        0.01629057, -0.00460119,  0.07602464,  0.00337768])
2025-07-15 22:42:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([-1.24842796e-01, -1.27909945e-01, -5.36824766e-02, -1.50325462e-02,
        1.03969741e-02,  6.02083792e-03, -4.60685289e-05, -2.28975431e-02,
       -1.50792555e-02])
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([ 1.24842796e-01,  1.27909945e-01,  5.36824766e-02,  1.50325462e-02,
       -1.03969741e-02, -6.02083792e-03,  4.60685289e-05,  2.28975431e-02,
        1.50792555e-02])
2025-07-15 22:42:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([-0.22254524, -0.02635489, -0.03329773, -0.00433924,  0.00893062,
       -0.01597947, -0.01168836, -0.10253326,  0.00451562])
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([ 0.22254524,  0.02635489,  0.03329773,  0.00433924, -0.00893062,
        0.01597947,  0.01168836,  0.10253326, -0.00451562])
2025-07-15 22:42:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([-0.19641671,  0.01657653, -0.02429121,  0.00233517, -0.04529451,
       -0.01890947,  0.00180667, -0.12006522, -0.00647813])
2025-07-15 22:42:55 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:55 - shap - INFO - phi = array([ 0.19641671, -0.01657653,  0.02429121, -0.00233517,  0.04529451,
        0.01890947, -0.00180667,  0.12006522,  0.00647813])
2025-07-15 22:42:55 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 3.17000664e-01,  3.60219053e-02,  1.60779806e-02, -2.74892107e-04,
        2.05217876e-02,  1.70091029e-02,  8.88718418e-03,  1.52492502e-02,
       -9.19558378e-04])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-3.17000664e-01, -3.60219053e-02, -1.60779806e-02,  2.74892107e-04,
       -2.05217876e-02, -1.70091029e-02, -8.88718418e-03, -1.52492502e-02,
        9.19558378e-04])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.21586841, -0.06152965, -0.06440891, -0.01103086,  0.00875627,
       -0.00836715,  0.00246789, -0.05316198, -0.00400648])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.21586841,  0.06152965,  0.06440891,  0.01103086, -0.00875627,
        0.00836715, -0.00246789,  0.05316198,  0.00400648])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.18117456,  0.00564567,  0.06201531,  0.01451861,  0.01848194,
        0.01584624, -0.01585375,  0.11121274, -0.00282791])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.18117456, -0.00564567, -0.06201531, -0.01451861, -0.01848194,
       -0.01584624,  0.01585375, -0.11121274,  0.00282791])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.17799819, -0.00594877,  0.05869586, -0.00488018,  0.01160077,
        0.0028272 , -0.02616044, -0.11467855, -0.00448439])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.17799819,  0.00594877, -0.05869586,  0.00488018, -0.01160077,
       -0.0028272 ,  0.02616044,  0.11467855,  0.00448439])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.19362028, -0.01297316, -0.02640413,  0.00644209, -0.04608947,
       -0.0183331 , -0.00026705, -0.11322131, -0.00638467])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.19362028,  0.01297316,  0.02640413, -0.00644209,  0.04608947,
        0.0183331 ,  0.00026705,  0.11322131,  0.00638467])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.1982696 , -0.00379889,  0.06868124,  0.0178758 , -0.01311554,
       -0.00453064, -0.016348  , -0.15205319, -0.00022543])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.1982696 ,  0.00379889, -0.06868124, -0.0178758 ,  0.01311554,
        0.00453064,  0.016348  ,  0.15205319,  0.00022543])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.19051659,  0.006154  ,  0.07234131,  0.00053464,  0.01078298,
        0.02628001, -0.01174301,  0.1175626 ,  0.00555965])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.19051659, -0.006154  , -0.07234131, -0.00053464, -0.01078298,
       -0.02628001,  0.01174301, -0.1175626 , -0.00555965])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.1747801 , -0.04936747, -0.07739321, -0.01026349, -0.02449006,
       -0.01292426, -0.00023211, -0.05689505, -0.00805658])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([0.1747801 , 0.04936747, 0.07739321, 0.01026349, 0.02449006,
       0.01292426, 0.00023211, 0.05689505, 0.00805658])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 2.21299825e-01,  4.06996729e-02,  1.46584317e-02,  1.45095590e-04,
       -2.08611470e-02,  2.55654905e-02,  9.22976391e-03,  1.02858480e-01,
        1.70662348e-03])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-2.21299825e-01, -4.06996729e-02, -1.46584317e-02, -1.45095590e-04,
        2.08611470e-02, -2.55654905e-02, -9.22976391e-03, -1.02858480e-01,
       -1.70662348e-03])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 2.32437773e-01,  4.63109816e-03,  7.04627154e-02, -2.28790434e-04,
        1.82829724e-02,  2.15310419e-02, -2.48177560e-03,  7.42975132e-02,
        3.07670031e-03])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-2.32437773e-01, -4.63109816e-03, -7.04627154e-02,  2.28790434e-04,
       -1.82829724e-02, -2.15310419e-02,  2.48177560e-03, -7.42975132e-02,
       -3.07670031e-03])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.20047722, -0.06229093, -0.05185323, -0.0092496 , -0.01983478,
       -0.01463449,  0.00421943, -0.05117718, -0.00910432])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.20047722,  0.06229093,  0.05185323,  0.0092496 ,  0.01983478,
        0.01463449, -0.00421943,  0.05117718,  0.00910432])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 3.51076325e-01,  4.45812127e-02,  9.14911936e-03,  2.11161903e-03,
        3.34053174e-02,  3.56300697e-05,  3.63997287e-03, -5.00011037e-02,
        1.30414326e-03])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-3.51076325e-01, -4.45812127e-02, -9.14911936e-03, -2.11161903e-03,
       -3.34053174e-02, -3.56300697e-05, -3.63997287e-03,  5.00011037e-02,
       -1.30414326e-03])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.11714609,  0.05775282, -0.00834916,  0.00262639,  0.02076676,
        0.00099503, -0.01394486,  0.24939666,  0.02080446])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.11714609, -0.05775282,  0.00834916, -0.00262639, -0.02076676,
       -0.00099503,  0.01394486, -0.24939666, -0.02080446])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.2137327 ,  0.01488158, -0.004384  , -0.00241282, -0.01338179,
        0.00513872, -0.00981896, -0.14400337,  0.00147762])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.2137327 , -0.01488158,  0.004384  ,  0.00241282,  0.01338179,
       -0.00513872,  0.00981896,  0.14400337, -0.00147762])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.20138954, -0.03088452, -0.04158777, -0.00291123, -0.02182386,
       -0.0174395 ,  0.00391282, -0.09947272, -0.00280601])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.20138954,  0.03088452,  0.04158777,  0.00291123,  0.02182386,
        0.0174395 , -0.00391282,  0.09947272,  0.00280601])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.21836782,  0.01252937, -0.00839169,  0.00375441, -0.01798849,
       -0.01969352, -0.01080922, -0.14396333,  0.00365615])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.21836782, -0.01252937,  0.00839169, -0.00375441,  0.01798849,
        0.01969352,  0.01080922,  0.14396333, -0.00365615])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.19326914, -0.02039785, -0.01089801,  0.00595698, -0.02228024,
       -0.02770713, -0.00243273, -0.09074157, -0.00974672])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.19326914,  0.02039785,  0.01089801, -0.00595698,  0.02228024,
        0.02770713,  0.00243273,  0.09074157,  0.00974672])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.29451467,  0.0661066 , -0.00894731, -0.00326839,  0.0299553 ,
        0.00769321,  0.00839672,  0.03620584, -0.00108322])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.29451467, -0.0661066 ,  0.00894731,  0.00326839, -0.0299553 ,
       -0.00769321, -0.00839672, -0.03620584,  0.00108322])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.18782027,  0.00943359,  0.0244157 ,  0.01301895, -0.02702048,
       -0.00130265,  0.0013364 , -0.15263267, -0.00287352])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.18782027, -0.00943359, -0.0244157 , -0.01301895,  0.02702048,
        0.00130265, -0.0013364 ,  0.15263267,  0.00287352])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.25051611, -0.02385749, -0.01246985, -0.0286376 ,  0.02125206,
        0.0220652 ,  0.00586543,  0.09106634,  0.02962771])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.25051611,  0.02385749,  0.01246985,  0.0286376 , -0.02125206,
       -0.0220652 , -0.00586543, -0.09106634, -0.02962771])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 3.08597940e-01,  8.33760527e-02, -6.56365454e-03,  1.16288354e-04,
        3.47536411e-02, -7.78878640e-03,  9.41947045e-03,  1.45199787e-02,
       -1.05617988e-02])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-3.08597940e-01, -8.33760527e-02,  6.56365454e-03, -1.16288354e-04,
       -3.47536411e-02,  7.78878640e-03, -9.41947045e-03, -1.45199787e-02,
        1.05617988e-02])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.08659505,  0.00287677,  0.17327059,  0.07544041, -0.01845354,
        0.01123685, -0.00750677,  0.21114282, -0.01252932])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.08659505, -0.00287677, -0.17327059, -0.07544041,  0.01845354,
       -0.01123685,  0.00750677, -0.21114282,  0.01252932])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.00794506, -0.01016682,  0.02708675, -0.00240675,  0.02020034,
        0.01805715, -0.00237482,  0.2361694 ,  0.01805983])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.00794506,  0.01016682, -0.02708675,  0.00240675, -0.02020034,
       -0.01805715,  0.00237482, -0.2361694 , -0.01805983])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.26692081, -0.00603678, -0.01064645, -0.02933698,  0.02048963,
        0.02768189,  0.00557539,  0.08438306,  0.02041532])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.26692081,  0.00603678,  0.01064645,  0.02933698, -0.02048963,
       -0.02768189, -0.00557539, -0.08438306, -0.02041532])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.33545964,  0.018655  ,  0.03963217,  0.00135882,  0.03005967,
       -0.01177636,  0.01220315, -0.05364231,  0.00180833])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.33545964, -0.018655  , -0.03963217, -0.00135882, -0.03005967,
        0.01177636, -0.01220315,  0.05364231, -0.00180833])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.3420641 ,  0.03278571,  0.01284325,  0.00127169, -0.01642119,
        0.03129229,  0.01224615, -0.02157179, -0.00429678])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.3420641 , -0.03278571, -0.01284325, -0.00127169,  0.01642119,
       -0.03129229, -0.01224615,  0.02157179,  0.00429678])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.18540662,  0.01902329, -0.017144  ,  0.01272671, -0.06416396,
       -0.01000135, -0.00598209, -0.11670602,  0.00141833])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.18540662, -0.01902329,  0.017144  , -0.01272671,  0.06416396,
        0.01000135,  0.00598209,  0.11670602, -0.00141833])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([-0.09795394, -0.04102566,  0.18566991,  0.00740808, -0.00660794,
        0.03004124,  0.00154307,  0.20044553,  0.03323547])
2025-07-15 22:42:56 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:56 - shap - INFO - phi = array([ 0.09795394,  0.04102566, -0.18566991, -0.00740808,  0.00660794,
       -0.03004124, -0.00154307, -0.20044553, -0.03323547])
2025-07-15 22:42:56 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.23453647,  0.05962597, -0.01000726, -0.00169022,  0.03145885,
        0.01051209,  0.00492239,  0.09573912,  0.00447601])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.23453647, -0.05962597,  0.01000726,  0.00169022, -0.03145885,
       -0.01051209, -0.00492239, -0.09573912, -0.00447601])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.06420588, -0.13879316, -0.03641111, -0.00756641,  0.02655241,
        0.00891162,  0.0046674 ,  0.11924968, -0.03770061])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.06420588,  0.13879316,  0.03641111,  0.00756641, -0.02655241,
       -0.00891162, -0.0046674 , -0.11924968,  0.03770061])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.10771068,  0.02254149,  0.1591829 ,  0.06306117, -0.02187006,
       -0.01803833, -0.00262404,  0.23748525, -0.00413106])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.10771068, -0.02254149, -0.1591829 , -0.06306117,  0.02187006,
        0.01803833,  0.00262404, -0.23748525,  0.00413106])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.22631509, -0.00146057,  0.00943757,  0.00758411,  0.00333109,
       -0.02013674, -0.02412579, -0.1397332 ,  0.00068175])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.22631509,  0.00146057, -0.00943757, -0.00758411, -0.00333109,
        0.02013674,  0.02412579,  0.1397332 , -0.00068175])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.19452065, -0.02687635, -0.05823513, -0.00209397, -0.03340335,
       -0.01257643, -0.00267886, -0.07743833, -0.00657928])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([0.19452065, 0.02687635, 0.05823513, 0.00209397, 0.03340335,
       0.01257643, 0.00267886, 0.07743833, 0.00657928])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.22810395,  0.07435099, -0.00387258,  0.00127627,  0.03285617,
       -0.00039789,  0.00468004,  0.09531263, -0.00644046])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.22810395, -0.07435099,  0.00387258, -0.00127627, -0.03285617,
        0.00039789, -0.00468004, -0.09531263,  0.00644046])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.20471709, -0.0210355 , -0.03631643, -0.00274597, -0.02199732,
       -0.01990257,  0.00541645, -0.10734311, -0.00220954])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.20471709,  0.0210355 ,  0.03631643,  0.00274597,  0.02199732,
        0.01990257, -0.00541645,  0.10734311,  0.00220954])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.31198256,  0.03450519, -0.00714751, -0.01311913,  0.02390448,
        0.02754035,  0.00823669,  0.03299388,  0.00311273])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.31198256, -0.03450519,  0.00714751,  0.01311913, -0.02390448,
       -0.02754035, -0.00823669, -0.03299388, -0.00311273])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.20215679, -0.03081331, -0.04671091, -0.00257719, -0.02778072,
       -0.01490266,  0.00169421, -0.08020156, -0.00740213])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.20215679,  0.03081331,  0.04671091,  0.00257719,  0.02778072,
        0.01490266, -0.00169421,  0.08020156,  0.00740213])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.18455197, -0.0467379 , -0.07665178, -0.01296627, -0.0173174 ,
       -0.01510263,  0.00295151, -0.06494137,  0.00091547])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.18455197,  0.0467379 ,  0.07665178,  0.01296627,  0.0173174 ,
        0.01510263, -0.00295151,  0.06494137, -0.00091547])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.05586076, -0.0050994 , -0.08818458, -0.05094888,  0.02957118,
        0.01081194,  0.00926308,  0.16428527,  0.02628773])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.05586076,  0.0050994 ,  0.08818458,  0.05094888, -0.02957118,
       -0.01081194, -0.00926308, -0.16428527, -0.02628773])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.30710178, -0.02127289,  0.07958384, -0.00173664, -0.01632856,
        0.03761695,  0.00485003, -0.01836573,  0.00799712])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.30710178,  0.02127289, -0.07958384,  0.00173664,  0.01632856,
       -0.03761695, -0.00485003,  0.01836573, -0.00799712])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.21079949, -0.0183875 , -0.0268903 , -0.00537764,  0.00810633,
       -0.01521069, -0.01001378, -0.07916697, -0.00849566])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.21079949,  0.0183875 ,  0.0268903 ,  0.00537764, -0.00810633,
        0.01521069,  0.01001378,  0.07916697,  0.00849566])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.20542044, -0.02989219, -0.05499574, -0.00458054, -0.02150172,
       -0.01442573,  0.00144822, -0.08478792, -0.00024626])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.20542044,  0.02989219,  0.05499574,  0.00458054,  0.02150172,
        0.01442573, -0.00144822,  0.08478792,  0.00024626])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.11729123,  0.10175464, -0.0403305 ,  0.00401719,  0.03946266,
       -0.00743059,  0.01054365,  0.15337019,  0.00076743])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.11729123, -0.10175464,  0.0403305 , -0.00401719, -0.03946266,
        0.00743059, -0.01054365, -0.15337019, -0.00076743])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 3.38328393e-01,  7.63327913e-02, -1.57344283e-02,  9.04423992e-05,
        2.81862085e-02, -1.50196762e-03,  9.27661879e-03, -1.04225369e-02,
       -2.54627244e-03])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-3.38328393e-01, -7.63327913e-02,  1.57344283e-02, -9.04423992e-05,
       -2.81862085e-02,  1.50196762e-03, -9.27661879e-03,  1.04225369e-02,
        2.54627244e-03])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.09748217,  0.079843  ,  0.01896693,  0.05634771, -0.00317963,
       -0.00696171,  0.00799367,  0.25172808, -0.01057585])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.09748217, -0.079843  , -0.01896693, -0.05634771,  0.00317963,
        0.00696171, -0.00799367, -0.25172808,  0.01057585])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([0.09914886, 0.04052384, 0.00424598, 0.0034654 , 0.03225046,
       0.00869743, 0.00280351, 0.16910136, 0.00762503])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.09914886, -0.04052384, -0.00424598, -0.0034654 , -0.03225046,
       -0.00869743, -0.00280351, -0.16910136, -0.00762503])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.17975982, -0.06856069, -0.08470533, -0.03568745,  0.0063433 ,
        0.01233521, -0.01032999, -0.0421179 ,  0.01174579])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.17975982,  0.06856069,  0.08470533,  0.03568745, -0.0063433 ,
       -0.01233521,  0.01032999,  0.0421179 , -0.01174579])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.19331985, -0.02196191,  0.03157309,  0.01495931,  0.01127305,
       -0.02827063, -0.02357206, -0.08352861, -0.00893664])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.19331985,  0.02196191, -0.03157309, -0.01495931, -0.01127305,
        0.02827063,  0.02357206,  0.08352861,  0.00893664])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.21834108, -0.03369147, -0.03367917, -0.00612576,  0.01053204,
       -0.01651357, -0.00573871, -0.09970266, -0.00388889])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.21834108,  0.03369147,  0.03367917,  0.00612576, -0.01053204,
        0.01651357,  0.00573871,  0.09970266,  0.00388889])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.25327462,  0.07000753, -0.02992744, -0.00426575,  0.03388349,
        0.00596953,  0.0075746 ,  0.08598156,  0.003371  ])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.25327462, -0.07000753,  0.02992744,  0.00426575, -0.03388349,
       -0.00596953, -0.0075746 , -0.08598156, -0.003371  ])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.20824628, -0.03748695, -0.04216809, -0.00553372, -0.01591361,
       -0.02042581,  0.00333312, -0.08506303, -0.00289796])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.20824628,  0.03748695,  0.04216809,  0.00553372,  0.01591361,
        0.02042581, -0.00333312,  0.08506303,  0.00289796])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.18201985, -0.07312466, -0.06058033, -0.02822404,  0.00582738,
        0.01716788, -0.00719187, -0.05362877,  0.00027677])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.18201985,  0.07312466,  0.06058033,  0.02822404, -0.00582738,
       -0.01716788,  0.00719187,  0.05362877, -0.00027677])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.08325423,  0.06191438,  0.0301036 ,  0.04340076, -0.02942545,
       -0.02581528,  0.01358577,  0.11122665, -0.00857881])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.08325423, -0.06191438, -0.0301036 , -0.04340076,  0.02942545,
        0.02581528, -0.01358577, -0.11122665,  0.00857881])
2025-07-15 22:42:57 - shap - INFO - num_full_subsets = 4
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([-0.19070921, -0.0087337 , -0.02663218,  0.00889125, -0.04071062,
       -0.02403417,  0.00280358, -0.02794332, -0.01637659])
2025-07-15 22:42:57 - shap - INFO - np.sum(w_aug) = 9.0
2025-07-15 22:42:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-07-15 22:42:57 - shap - INFO - phi = array([ 0.19070921,  0.0087337 ,  0.02663218, -0.00889125,  0.04071062,
        0.02403417, -0.00280358,  0.02794332,  0.01637659])
2025-07-15 22:42:57 - enhanced_shap_visualization - ERROR - Failed to create summary plot: The number of FixedLocator locations (2), usually from a call to set_ticks, does not match the number of labels (9).
2025-07-15 22:43:01 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-15 22:43:01 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:43:01 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:43:01 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -   stacking SHAP分析完成
