2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.25100447,  0.02797351,  0.00396604, -0.08139811, -0.05084689,
       -0.04187172, -0.06726121, -0.00972644, -0.06180406,  0.00749358])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.25100447, -0.02797351, -0.00396604,  0.08139811,  0.05084689,
        0.04187172,  0.06726121,  0.00972644,  0.06180406, -0.00749358])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.02774778, -0.09601994, -0.01029271, -0.15636521,  0.03185569,
       -0.04640338, -0.06530178, -0.03941265, -0.1071391 , -0.00509665])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.02774778,  0.09601994,  0.01029271,  0.15636521, -0.03185569,
        0.04640338,  0.06530178,  0.03941265,  0.1071391 ,  0.00509665])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.10152336, -0.18774537, -0.00492187, -0.11054987,  0.03012878,
        0.0062191 , -0.043755  ,  0.0053157 , -0.0822969 , -0.01180391])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.10152336,  0.18774537,  0.00492187,  0.11054987, -0.03012878,
       -0.0062191 ,  0.043755  , -0.0053157 ,  0.0822969 ,  0.01180391])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.07733442,  0.12296069, -0.02964038, -0.05458445,  0.02049793,
        0.06042787, -0.07828551, -0.12054894,  0.15014317, -0.0694883 ])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.07733442, -0.12296069,  0.02964038,  0.05458445, -0.02049793,
       -0.06042787,  0.07828551,  0.12054894, -0.15014317,  0.0694883 ])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 1.69787872e-01,  1.08200565e-01,  9.06149627e-05,  4.34931664e-03,
        1.11803227e-02,  2.54116179e-02,  3.75404771e-02, -5.59748674e-03,
        9.24445781e-02,  2.94710108e-02])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-1.69787872e-01, -1.08200565e-01, -9.06149627e-05, -4.34931664e-03,
       -1.11803227e-02, -2.54116179e-02, -3.75404771e-02,  5.59748674e-03,
       -9.24445781e-02, -2.94710108e-02])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([0.03155548, 0.03196745, 0.00776068, 0.04153747, 0.01321551,
       0.03046544, 0.0623211 , 0.02504534, 0.14824335, 0.02469612])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.03155548, -0.03196745, -0.00776068, -0.04153747, -0.01321551,
       -0.03046544, -0.0623211 , -0.02504534, -0.14824335, -0.02469612])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.08873815, -0.00803261, -0.00295794,  0.05280249, -0.11781354,
        0.03139813, -0.21688793, -0.00291079,  0.03972061,  0.08631684])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.08873815,  0.00803261,  0.00295794, -0.05280249,  0.11781354,
       -0.03139813,  0.21688793,  0.00291079, -0.03972061, -0.08631684])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.08679179, -0.02665585, -0.02287271, -0.20918165,  0.03732123,
        0.05039826, -0.06268444, -0.05017184, -0.19879889, -0.0489615 ])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.08679179,  0.02665585,  0.02287271,  0.20918165, -0.03732123,
       -0.05039826,  0.06268444,  0.05017184,  0.19879889,  0.0489615 ])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.09549313, -0.14557167, -0.01736047, -0.07953637,  0.04565253,
        0.00957405, -0.04687127,  0.00052373, -0.26882031, -0.08459112])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.09549313,  0.14557167,  0.01736047,  0.07953637, -0.04565253,
       -0.00957405,  0.04687127, -0.00052373,  0.26882031,  0.08459112])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.1094292 ,  0.06008213,  0.00923891,  0.18192237,  0.00538538,
       -0.06583849,  0.06704474,  0.03955245,  0.10246878, -0.03842525])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.1094292 , -0.06008213, -0.00923891, -0.18192237, -0.00538538,
        0.06583849, -0.06704474, -0.03955245, -0.10246878,  0.03842525])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.06221649,  0.10715794,  0.00778012,  0.2050637 , -0.04593296,
       -0.05671142,  0.00425925,  0.0367407 ,  0.02518924, -0.02894346])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.06221649, -0.10715794, -0.00778012, -0.2050637 ,  0.04593296,
        0.05671142, -0.00425925, -0.0367407 , -0.02518924,  0.02894346])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.01053397, -0.08121481, -0.01716854, -0.14466163,  0.02736128,
        0.02707188, -0.07379395, -0.02613792, -0.1973364 , -0.0485555 ])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.01053397,  0.08121481,  0.01716854,  0.14466163, -0.02736128,
       -0.02707188,  0.07379395,  0.02613792,  0.1973364 ,  0.0485555 ])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.02234971, -0.15861596, -0.00408542,  0.07923964, -0.00984569,
       -0.10268305, -0.080181  ,  0.00427941, -0.18566224, -0.04211036])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.02234971,  0.15861596,  0.00408542, -0.07923964,  0.00984569,
        0.10268305,  0.080181  , -0.00427941,  0.18566224,  0.04211036])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.19600083,  0.26139551, -0.01818463, -0.02907884,  0.01621637,
       -0.02126921, -0.01096637, -0.08790619,  0.08365535, -0.06148432])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.19600083, -0.26139551,  0.01818463,  0.02907884, -0.01621637,
        0.02126921,  0.01096637,  0.08790619, -0.08365535,  0.06148432])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.07452069,  0.01569454, -0.00032175,  0.02219378,  0.03915903,
        0.02409169,  0.05496891, -0.03084537,  0.24682262,  0.02741256])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.07452069, -0.01569454,  0.00032175, -0.02219378, -0.03915903,
       -0.02409169, -0.05496891,  0.03084537, -0.24682262, -0.02741256])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.08028585, -0.13877164, -0.00290931,  0.00270802,  0.00607988,
       -0.11732595, -0.0971519 ,  0.06918811, -0.26081372, -0.06341995])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.08028585,  0.13877164,  0.00290931, -0.00270802, -0.00607988,
        0.11732595,  0.0971519 , -0.06918811,  0.26081372,  0.06341995])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.20944714,  0.172427  ,  0.00970249,  0.06952666,  0.00878432,
       -0.08167999,  0.10194672,  0.03463278, -0.02764827, -0.02218419])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.20944714, -0.172427  , -0.00970249, -0.06952666, -0.00878432,
        0.08167999, -0.10194672, -0.03463278,  0.02764827,  0.02218419])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.05385537,  0.02390955, -0.00148718,  0.03126881,  0.00074598,
        0.14753781,  0.03179471, -0.00676017,  0.12857462,  0.04776919])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.05385537, -0.02390955,  0.00148718, -0.03126881, -0.00074598,
       -0.14753781, -0.03179471,  0.00676017, -0.12857462, -0.04776919])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.35019751, -0.21689886,  0.02435872, -0.04356983,  0.02028317,
       -0.01872989,  0.0840264 ,  0.03792967,  0.09079951,  0.07318018])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.35019751,  0.21689886, -0.02435872,  0.04356983, -0.02028317,
        0.01872989, -0.0840264 , -0.03792967, -0.09079951, -0.07318018])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.11673313,  0.07652138, -0.00029882,  0.07859002,  0.00871948,
        0.02606524,  0.02698218,  0.01396932,  0.15058244, -0.02350957])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.11673313, -0.07652138,  0.00029882, -0.07859002, -0.00871948,
       -0.02606524, -0.02698218, -0.01396932, -0.15058244,  0.02350957])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.07575428,  0.08246941,  0.01450664, -0.03472418,  0.03169632,
       -0.01451838,  0.12354388,  0.03748429,  0.1058825 ,  0.04422113])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.07575428, -0.08246941, -0.01450664,  0.03472418, -0.03169632,
        0.01451838, -0.12354388, -0.03748429, -0.1058825 , -0.04422113])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.01289779,  0.04516484,  0.01329703,  0.27308473, -0.05252775,
       -0.03619354,  0.063605  ,  0.02486145, -0.05616998,  0.00287317])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.01289779, -0.04516484, -0.01329703, -0.27308473,  0.05252775,
        0.03619354, -0.063605  , -0.02486145,  0.05616998, -0.00287317])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.15329468,  0.08801925,  0.01017094,  0.15025262,  0.01560931,
       -0.10066497,  0.10591613,  0.00544102,  0.0539537 , -0.00841451])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.15329468, -0.08801925, -0.01017094, -0.15025262, -0.01560931,
        0.10066497, -0.10591613, -0.00544102, -0.0539537 ,  0.00841451])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.04955329, -0.08301403, -0.00055859, -0.10868637,  0.09043909,
        0.10370527,  0.09396227, -0.05023035,  0.39598736,  0.06959109])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.04955329,  0.08301403,  0.00055859,  0.10868637, -0.09043909,
       -0.10370527, -0.09396227,  0.05023035, -0.39598736, -0.06959109])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.13633312, -0.0983583 ,  0.01466267,  0.05805373,  0.00490482,
        0.13309297,  0.10995983,  0.03010342,  0.08405901,  0.07300902])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.13633312,  0.0983583 , -0.01466267, -0.05805373, -0.00490482,
       -0.13309297, -0.10995983, -0.03010342, -0.08405901, -0.07300902])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.03594587, -0.01446102,  0.0176841 ,  0.08317845, -0.01069861,
        0.0515628 ,  0.087097  ,  0.05532796,  0.06438867,  0.10363282])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.03594587,  0.01446102, -0.0176841 , -0.08317845,  0.01069861,
       -0.0515628 , -0.087097  , -0.05532796, -0.06438867, -0.10363282])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.47654946, -0.03105238,  0.03518214,  0.06656618, -0.10491627,
       -0.08287408,  0.01117988,  0.11632058, -0.06251544,  0.05220205])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.47654946,  0.03105238, -0.03518214, -0.06656618,  0.10491627,
        0.08287408, -0.01117988, -0.11632058,  0.06251544, -0.05220205])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.0514841 , -0.09223288, -0.01571584, -0.07352036,  0.0205015 ,
       -0.08686979, -0.12547434, -0.03222617, -0.12598768, -0.04417308])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.0514841 ,  0.09223288,  0.01571584,  0.07352036, -0.0205015 ,
        0.08686979,  0.12547434,  0.03222617,  0.12598768,  0.04417308])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.05566848, -0.04656936,  0.01295718, -0.04169215, -0.02223011,
        0.29363236,  0.0747632 ,  0.07121712, -0.00576515,  0.14759636])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.05566848,  0.04656936, -0.01295718,  0.04169215,  0.02223011,
       -0.29363236, -0.0747632 , -0.07121712,  0.00576515, -0.14759636])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.09136455,  0.11727282,  0.02153977,  0.29808   , -0.15551532,
        0.01257391, -0.0445503 ,  0.14366212, -0.2833693 ,  0.06910172])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.09136455, -0.11727282, -0.02153977, -0.29808   ,  0.15551532,
       -0.01257391,  0.0445503 , -0.14366212,  0.2833693 , -0.06910172])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.06978894, -0.04205652, -0.00805919, -0.29802272,  0.02441452,
        0.07877141, -0.03593118,  0.01196483, -0.14372096, -0.01037077])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.06978894,  0.04205652,  0.00805919,  0.29802272, -0.02441452,
       -0.07877141,  0.03593118, -0.01196483,  0.14372096,  0.01037077])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.01587664, -0.12799689, -0.00784915, -0.19782864,  0.05311867,
       -0.01754455, -0.0380436 ,  0.01888726, -0.15130419, -0.04007618])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.01587664,  0.12799689,  0.00784915,  0.19782864, -0.05311867,
        0.01754455,  0.0380436 , -0.01888726,  0.15130419,  0.04007618])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.11138953,  0.12631728,  0.01355174,  0.06380633, -0.0609865 ,
        0.04895241, -0.00584739,  0.10180597, -0.04022128,  0.06712453])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.11138953, -0.12631728, -0.01355174, -0.06380633,  0.0609865 ,
       -0.04895241,  0.00584739, -0.10180597,  0.04022128, -0.06712453])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.21971063, -0.36161199, -0.01594636,  0.02521259,  0.06907927,
       -0.07556873, -0.10633321,  0.05248156, -0.18661817, -0.07227011])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.21971063,  0.36161199,  0.01594636, -0.02521259, -0.06907927,
        0.07556873,  0.10633321, -0.05248156,  0.18661817,  0.07227011])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.14062329,  0.07499259,  0.0084155 , -0.04805744, -0.05777051,
       -0.079755  , -0.00271874, -0.014079  , -0.19686034,  0.02052404])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.14062329, -0.07499259, -0.0084155 ,  0.04805744,  0.05777051,
        0.079755  ,  0.00271874,  0.014079  ,  0.19686034, -0.02052404])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.11765495,  0.04520464,  0.00450155,  0.04313513,  0.02759245,
        0.00132405,  0.07610772, -0.00657451,  0.13379456,  0.03201704])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.11765495, -0.04520464, -0.00450155, -0.04313513, -0.02759245,
       -0.00132405, -0.07610772,  0.00657451, -0.13379456, -0.03201704])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.17876209,  0.15350556,  0.00919612,  0.1632105 , -0.02107984,
       -0.07829332,  0.05435276,  0.03427876, -0.01114875, -0.01749017])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.17876209, -0.15350556, -0.00919612, -0.1632105 ,  0.02107984,
        0.07829332, -0.05435276, -0.03427876,  0.01114875,  0.01749017])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.12185514,  0.18066816,  0.00531765,  0.18115594, -0.1149252 ,
       -0.074875  , -0.09115451,  0.07212078, -0.16674012, -0.03475813])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.12185514, -0.18066816, -0.00531765, -0.18115594,  0.1149252 ,
        0.074875  ,  0.09115451, -0.07212078,  0.16674012,  0.03475813])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.03572422,  0.10269968,  0.00439245,  0.09248841, -0.05092667,
        0.09193497,  0.01226487,  0.04766618, -0.04611698,  0.00164625])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.03572422, -0.10269968, -0.00439245, -0.09248841,  0.05092667,
       -0.09193497, -0.01226487, -0.04766618,  0.04611698, -0.00164625])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.01559658, -0.03604484, -0.00886692, -0.29214801,  0.04021056,
       -0.09597049, -0.09024693, -0.04794089, -0.00888853,  0.03579128])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.01559658,  0.03604484,  0.00886692,  0.29214801, -0.04021056,
        0.09597049,  0.09024693,  0.04794089,  0.00888853, -0.03579128])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.17777237, -0.07586729, -0.00586541, -0.07443245,  0.00441137,
        0.05894891,  0.01468718, -0.04225164, -0.1713542 , -0.02321603])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.17777237,  0.07586729,  0.00586541,  0.07443245, -0.00441137,
       -0.05894891, -0.01468718,  0.04225164,  0.1713542 ,  0.02321603])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([ 0.07417585,  0.08229097, -0.01611556,  0.14908845,  0.01188859,
       -0.07678984, -0.05646802, -0.10609662,  0.29712934, -0.05465241])
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:41 - shap - INFO - phi = array([-0.07417585, -0.08229097,  0.01611556, -0.14908845, -0.01188859,
        0.07678984,  0.05646802,  0.10609662, -0.29712934,  0.05465241])
2025-07-16 01:33:41 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:41 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.10163036,  0.15623258,  0.00070019, -0.00614064, -0.00089685,
       -0.03823336,  0.05598835, -0.02796676, -0.04286837, -0.00113877])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.10163036, -0.15623258, -0.00070019,  0.00614064,  0.00089685,
        0.03823336, -0.05598835,  0.02796676,  0.04286837,  0.00113877])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.00342659, -0.15672964, -0.01409515, -0.0513725 ,  0.03372064,
       -0.01827297, -0.06337389, -0.01763264, -0.17935702, -0.0607719 ])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.00342659,  0.15672964,  0.01409515,  0.0513725 , -0.03372064,
        0.01827297,  0.06337389,  0.01763264,  0.17935702,  0.0607719 ])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.04604893, -0.00116806,  0.00735103,  0.03876485,  0.01701072,
        0.06683364,  0.0734887 ,  0.01536206,  0.15260837,  0.05569   ])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.04604893,  0.00116806, -0.00735103, -0.03876485, -0.01701072,
       -0.06683364, -0.0734887 , -0.01536206, -0.15260837, -0.05569   ])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.1160242 , -0.09206423,  0.00489699,  0.09317917, -0.04591166,
       -0.09296487, -0.05250854,  0.02556034, -0.20793639, -0.02907195])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.1160242 ,  0.09206423, -0.00489699, -0.09317917,  0.04591166,
        0.09296487,  0.05250854, -0.02556034,  0.20793639,  0.02907195])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.06634161,  0.03205331, -0.00892068,  0.14567775, -0.0816289 ,
        0.05185875, -0.0119272 , -0.03409002, -0.41032309, -0.09362854])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.06634161, -0.03205331,  0.00892068, -0.14567775,  0.0816289 ,
       -0.05185875,  0.0119272 ,  0.03409002,  0.41032309,  0.09362854])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.09565707, -0.04040436, -0.01010363, -0.03629026, -0.01182223,
        0.01745858, -0.03643267, -0.04761218, -0.21876232, -0.03835134])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.09565707,  0.04040436,  0.01010363,  0.03629026,  0.01182223,
       -0.01745858,  0.03643267,  0.04761218,  0.21876232,  0.03835134])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 6.19625507e-03, -4.90593262e-02,  2.48715595e-04,  6.74558694e-02,
        7.29312298e-02, -2.31304021e-02,  8.71845214e-02, -3.31791980e-02,
        3.72771882e-01, -3.36597820e-02])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-6.19625507e-03,  4.90593262e-02, -2.48715595e-04, -6.74558694e-02,
       -7.29312298e-02,  2.31304021e-02, -8.71845214e-02,  3.31791980e-02,
       -3.72771882e-01,  3.36597820e-02])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.14032627,  0.06260503, -0.0033196 , -0.02021727,  0.07155985,
       -0.01689279,  0.08759706, -0.03429904,  0.21289693, -0.02532467])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.14032627, -0.06260503,  0.0033196 ,  0.02021727, -0.07155985,
        0.01689279, -0.08759706,  0.03429904, -0.21289693,  0.02532467])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.05259924, -0.15871995, -0.00449546, -0.1923537 ,  0.06272693,
       -0.02371244, -0.03782713, -0.04628253,  0.10855876,  0.06985307])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.05259924,  0.15871995,  0.00449546,  0.1923537 , -0.06272693,
        0.02371244,  0.03782713,  0.04628253, -0.10855876, -0.06985307])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.06882565, -0.0343422 ,  0.00717777,  0.01756687,  0.05024991,
       -0.01246698,  0.08877327, -0.03465038,  0.26344431,  0.03601222])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.06882565,  0.0343422 , -0.00717777, -0.01756687, -0.05024991,
        0.01246698, -0.08877327,  0.03465038, -0.26344431, -0.03601222])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.3656017 ,  0.08242875,  0.01735253, -0.19940326, -0.06437458,
        0.00362436, -0.02165163,  0.05560999, -0.06350442,  0.04930268])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.3656017 , -0.08242875, -0.01735253,  0.19940326,  0.06437458,
       -0.00362436,  0.02165163, -0.05560999,  0.06350442, -0.04930268])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.13050766,  0.12002116,  0.00820942, -0.03098686, -0.11288173,
       -0.02465764, -0.07105556,  0.04318084, -0.29537622,  0.00446056])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.13050766, -0.12002116, -0.00820942,  0.03098686,  0.11288173,
        0.02465764,  0.07105556, -0.04318084,  0.29537622, -0.00446056])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.14480556,  0.03848241, -0.00501207, -0.00153682, -0.08376281,
        0.02390605, -0.1123119 , -0.01035973, -0.20774199, -0.01529508])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.14480556, -0.03848241,  0.00501207,  0.00153682,  0.08376281,
       -0.02390605,  0.1123119 ,  0.01035973,  0.20774199,  0.01529508])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.09589457,  0.02959108, -0.01168575, -0.0422223 ,  0.08990193,
       -0.00894126,  0.04654399, -0.06685092,  0.39498061, -0.05482754])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.09589457, -0.02959108,  0.01168575,  0.0422223 , -0.08990193,
        0.00894126, -0.04654399,  0.06685092, -0.39498061,  0.05482754])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.16266579, -0.05278235, -0.01202994, -0.09276437, -0.01654415,
        0.04399764, -0.09726339, -0.04222513, -0.06971085, -0.02251207])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.16266579,  0.05278235,  0.01202994,  0.09276437,  0.01654415,
       -0.04399764,  0.09726339,  0.04222513,  0.06971085,  0.02251207])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.01680957, -0.04693559, -0.0067715 , -0.02845384,  0.08154349,
        0.00936433,  0.05099727, -0.0785756 ,  0.46278297,  0.01118397])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.01680957,  0.04693559,  0.0067715 ,  0.02845384, -0.08154349,
       -0.00936433, -0.05099727,  0.0785756 , -0.46278297, -0.01118397])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.04396272, -0.02025293, -0.00662647,  0.01473215,  0.07754163,
        0.04735705,  0.1029535 , -0.07942494,  0.26420147, -0.03265266])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.04396272,  0.02025293,  0.00662647, -0.01473215, -0.07754163,
       -0.04735705, -0.1029535 ,  0.07942494, -0.26420147,  0.03265266])
2025-07-16 01:33:42 - shap - INFO - num_full_subsets = 5
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([ 0.13078961,  0.09294107,  0.0003293 , -0.01411189,  0.02019909,
        0.05414297,  0.05240959,  0.00205227,  0.11659693,  0.01905726])
2025-07-16 01:33:42 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 01:33:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 01:33:42 - shap - INFO - phi = array([-0.13078961, -0.09294107, -0.0003293 ,  0.01411189, -0.02019909,
       -0.05414297, -0.05240959, -0.00205227, -0.11659693, -0.01905726])
