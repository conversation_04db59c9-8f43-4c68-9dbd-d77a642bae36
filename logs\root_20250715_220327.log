2025-07-15 22:03:27 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:03:27 - model_ensemble - INFO - ============================================================
2025-07-15 22:03:27 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:03:27 - model_ensemble - INFO - ============================================================
2025-07-15 22:03:27 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 22:03:27 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 22:03:27 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:03:27 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:03:27 - model_training - INFO - 模型名称: Random Forest
2025-07-15 22:03:27 - model_training - INFO - 准确率: 0.9000
2025-07-15 22:03:27 - model_training - INFO - AUC: 0.9756
2025-07-15 22:03:27 - model_training - INFO - 混淆矩阵:
2025-07-15 22:03:27 - model_training - INFO - 
[[24  6]
 [ 0 30]]
2025-07-15 22:03:27 - model_training - INFO - 
分类报告:
2025-07-15 22:03:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.80      0.89        30
           1       0.83      1.00      0.91        30

    accuracy                           0.90        60
   macro avg       0.92      0.90      0.90        60
weighted avg       0.92      0.90      0.90        60

2025-07-15 22:03:27 - model_training - INFO - 训练时间: 0.09 秒
2025-07-15 22:03:27 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 22:03:27 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 22:03:27 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:03:27 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:03:27 - model_training - INFO - 模型名称: XGBoost
2025-07-15 22:03:27 - model_training - INFO - 准确率: 0.9333
2025-07-15 22:03:27 - model_training - INFO - AUC: 0.9733
2025-07-15 22:03:27 - model_training - INFO - 混淆矩阵:
2025-07-15 22:03:27 - model_training - INFO - 
[[28  2]
 [ 2 28]]
2025-07-15 22:03:27 - model_training - INFO - 
分类报告:
2025-07-15 22:03:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.93      0.93      0.93        30
           1       0.93      0.93      0.93        30

    accuracy                           0.93        60
   macro avg       0.93      0.93      0.93        60
weighted avg       0.93      0.93      0.93        60

2025-07-15 22:03:27 - model_training - INFO - 训练时间: 0.11 秒
2025-07-15 22:03:27 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 22:03:27 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 22:03:27 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:03:27 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 22:03:27 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:03:27 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:03:27 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:03:28 - model_ensemble - INFO -     voting_soft - 准确率: 0.9500, F1: 0.9500
2025-07-15 22:03:28 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:03:28 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:03:28 - model_ensemble - INFO -     voting_hard - 准确率: 0.9333, F1: 0.9333
2025-07-15 22:03:28 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:03:28 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:03:28 - model_ensemble - INFO -   stacking - 准确率: 0.9333, F1: 0.9333
2025-07-15 22:03:28 - model_ensemble - INFO - ============================================================
2025-07-15 22:03:28 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:03:28 - model_ensemble - INFO - ============================================================
2025-07-15 22:03:28 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 22:03:28 - model_ensemble - INFO - 最佳F1分数: 0.9500
2025-07-15 22:03:28 - model_ensemble - INFO - 最佳准确率: 0.9500
2025-07-15 22:03:28 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:03:28 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9500, 精确率: 0.9505, 召回率: 0.9500, F1: 0.9500, AUC: 0.9778
2025-07-15 22:03:28 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9333, 精确率: 0.9333, 召回率: 0.9333, F1: 0.9333, AUC: 0.0000
2025-07-15 22:03:28 - model_ensemble - INFO -   stacking        - 准确率: 0.9333, 精确率: 0.9333, 召回率: 0.9333, F1: 0.9333, AUC: 0.9778
2025-07-15 22:03:28 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:03:29 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_220328.joblib
2025-07-15 22:03:29 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 22:03:29 - safe_visualization - INFO - Summary report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_summary_report.txt
2025-07-15 22:03:29 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 22:03:29 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:03:29 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:03:29 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.00710803,  0.02387726, -0.0715467 ,  0.10142575, -0.04187757,
       -0.05842783, -0.26476455, -0.0084449 ])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.00710803, -0.02387726,  0.0715467 , -0.10142575,  0.04187757,
        0.05842784,  0.26476455,  0.0084449 ])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.00344664, -0.01039531,  0.14018732,  0.18782572,  0.03814792,
       -0.02352312,  0.14509115,  0.00309571])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.00344664,  0.01039531, -0.14018731, -0.18782572, -0.03814791,
        0.02352312, -0.14509115, -0.0030957 ])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.0120277 , -0.00993455, -0.1717956 , -0.10632469, -0.07900306,
       -0.02874503, -0.05339215, -0.00471225])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.0120277 ,  0.00993455,  0.1717956 ,  0.10632469,  0.07900306,
        0.02874503,  0.05339215,  0.00471225])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.01874032, -0.00517576, -0.15432142, -0.09656524,  0.01521118,
       -0.03471748, -0.18326846,  0.01077453])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.01874032,  0.00517576,  0.15432142,  0.09656524, -0.01521118,
        0.03471748,  0.18326846, -0.01077453])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.01287722, -0.00138628, -0.20347045, -0.02085175, -0.00301377,
       -0.03082333, -0.1806016 , -0.01863152])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([0.01287722, 0.00138628, 0.20347045, 0.02085175, 0.00301378,
       0.03082333, 0.1806016 , 0.01863152])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.00500227,  0.02399915, -0.10598292,  0.12776727,  0.03206429,
       -0.05853004, -0.27136046, -0.01460627])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.00500227, -0.02399915,  0.10598292, -0.12776727, -0.03206429,
        0.05853004,  0.27136046,  0.01460627])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.00392552,  0.00932305,  0.14994454,  0.17770967,  0.02634437,
       -0.0255491 ,  0.1646879 ,  0.01657545])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.00392552, -0.00932305, -0.14994453, -0.17770966, -0.02634437,
        0.0255491 , -0.1646879 , -0.01657544])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.01199036,  0.01400515,  0.04308029, -0.18019097, -0.08456421,
       -0.04658419, -0.17449745,  0.00551413])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.01199036, -0.01400515, -0.04308029,  0.18019097,  0.08456421,
        0.04658419,  0.17449745, -0.00551413])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.00667447, -0.01758775, -0.10461489, -0.00174298,  0.10364887,
        0.04586125,  0.15412234, -0.01705588])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.00667447,  0.01758775,  0.10461489,  0.00174298, -0.10364887,
       -0.04586125, -0.15412234,  0.01705588])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.00115073, -0.02106792, -0.08964023, -0.20965777, -0.15670388,
       -0.02034449,  0.06825881, -0.00652294])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.00115073,  0.02106792,  0.08964023,  0.20965777,  0.15670388,
        0.02034449, -0.06825881,  0.00652294])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.03470095, -0.01995152, -0.31619137,  0.04446163,  0.00460059,
        0.05192976, -0.07405223, -0.04131509])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.03470095,  0.01995152,  0.31619137, -0.04446163, -0.00460059,
       -0.05192976,  0.07405223,  0.04131509])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.00912063,  0.01603218, -0.22595403, -0.03040397,  0.11731979,
        0.0255718 , -0.2531896 , -0.01379615])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.00912063, -0.01603218,  0.22595404,  0.03040397, -0.11731979,
       -0.0255718 ,  0.2531896 ,  0.01379615])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.006211  , -0.01128769, -0.04899271,  0.10381608,  0.04237118,
        0.02547263,  0.15356368, -0.01270881])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.006211  ,  0.01128769,  0.04899272, -0.10381609, -0.04237117,
       -0.02547263, -0.15356368,  0.01270881])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.00626239, -0.03134015, -0.15782609, -0.18143147, -0.17365851,
        0.0863415 ,  0.06486257, -0.00620481])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.00626239,  0.03134015,  0.1578261 ,  0.18143147,  0.17365851,
       -0.0863415 , -0.06486257,  0.00620481])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.00332656,  0.00352377,  0.13354153,  0.17040284, -0.06066529,
       -0.03805914, -0.10375689,  0.00352883])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.00332656, -0.00352377, -0.13354153, -0.17040284,  0.06066529,
        0.03805914,  0.1037569 , -0.00352883])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.03151851, -0.0141398 ,  0.095878  , -0.09396059,  0.23093701,
       -0.03983713,  0.15779299,  0.02498678])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([-0.0315185 ,  0.0141398 , -0.095878  ,  0.09396059, -0.23093701,
        0.03983713, -0.15779299, -0.02498678])
2025-07-15 22:03:29 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:29 - shap - INFO - phi = array([ 0.10135689,  0.08355371, -0.07941303, -0.1562018 , -0.1089583 ,
        0.0507731 ,  0.10571337, -0.00504686])
2025-07-15 22:03:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.10135689, -0.08355371,  0.07941303,  0.1562018 ,  0.1089583 ,
       -0.0507731 , -0.10571337,  0.00504686])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00551651, -0.0031841 ,  0.14608841,  0.13315557,  0.10237951,
       -0.02960133,  0.14980094,  0.01193075])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00551651,  0.00318409, -0.14608841, -0.13315557, -0.10237952,
        0.02960133, -0.14980094, -0.01193076])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00324667, -0.00326921,  0.14571616,  0.14426531,  0.07995569,
       -0.02191585,  0.15992796,  0.00094928])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00324667,  0.00326921, -0.14571616, -0.14426531, -0.07995569,
        0.02191585, -0.15992797, -0.00094928])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.0097946 , -0.00238541, -0.08254853, -0.12678882, -0.06731977,
       -0.03641712, -0.15332037, -0.00378572])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([0.0097946 , 0.00238541, 0.08254853, 0.12678882, 0.06731977,
       0.03641712, 0.15332037, 0.00378572])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.02477759,  0.02472769,  0.06034457, -0.22222983, -0.00196748,
       -0.04697233, -0.21266669, -0.00530929])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.02477759, -0.02472769, -0.06034457,  0.22222983,  0.00196748,
        0.04697233,  0.21266669,  0.00530929])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.0063382 , -0.01257248,  0.05345528, -0.30323241, -0.17076255,
       -0.05815963,  0.06230576, -0.01549374])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.0063382 ,  0.01257248, -0.05345528,  0.30323241,  0.17076255,
        0.05815963, -0.06230576,  0.01549374])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00726159,  0.03059461, -0.08486217,  0.08608496, -0.04941124,
       -0.06121657, -0.24764678,  0.02056184])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00726159, -0.03059461,  0.08486217, -0.08608496,  0.04941124,
        0.06121657,  0.24764678, -0.02056184])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00099516, -0.00322076,  0.14285341,  0.13453057,  0.10797451,
       -0.02896299,  0.14925927,  0.01162409])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00099516,  0.00322076, -0.14285341, -0.13453057, -0.10797452,
        0.02896299, -0.14925927, -0.01162409])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.05097693,  0.01088819,  0.08944505, -0.02656584,  0.11073219,
       -0.06656256, -0.27627466,  0.00511641])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.05097693, -0.01088819, -0.08944505,  0.02656584, -0.11073219,
        0.06656256,  0.27627466, -0.00511641])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00370211, -0.0013652 ,  0.13116575,  0.22114112, -0.04206101,
        0.08770272, -0.05714374,  0.00196803])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00370211,  0.0013652 , -0.13116575, -0.22114112,  0.04206101,
       -0.08770272,  0.05714374, -0.00196803])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.03624676,  0.01201168,  0.092948  , -0.08126708,  0.22320686,
       -0.01377711,  0.17654177,  0.02564817])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.03624676, -0.01201168, -0.092948  ,  0.08126708, -0.22320686,
        0.0137771 , -0.17654177, -0.02564817])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00234984, -0.00557243,  0.14459841,  0.13116723,  0.10269951,
       -0.02240799,  0.15155927,  0.01035909])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00234984,  0.00557243, -0.14459841, -0.13116723, -0.10269952,
        0.02240799, -0.15155927, -0.01035909])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00956783, -0.0157786 , -0.17806156, -0.13227696, -0.10040381,
        0.10357909, -0.03458289, -0.00714902])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00956783,  0.0157786 ,  0.17806156,  0.13227696,  0.10040381,
       -0.10357909,  0.03458289,  0.00714902])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00792157, -0.01183292, -0.05966673,  0.10820715,  0.00271093,
        0.05339223,  0.13730636, -0.00647632])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00792157,  0.01183292,  0.05966673, -0.10820715, -0.00271093,
       -0.05339223, -0.13730636,  0.00647632])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.01292221,  0.01313392,  0.10538096,  0.03084591,  0.03895919,
        0.08812361,  0.07908501,  0.01846059])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.01292221, -0.01313392, -0.10538096, -0.03084591, -0.03895919,
       -0.08812361, -0.07908501, -0.01846059])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.01651783, -0.00466303,  0.09953198, -0.09036085,  0.22827774,
        0.00538585,  0.16756762,  0.02247282])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.01651783,  0.00466303, -0.09953198,  0.09036086, -0.22827774,
       -0.00538585, -0.16756762, -0.02247282])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00850146, -0.00998707,  0.10833987,  0.15745144,  0.04025333,
        0.02516466,  0.12178945, -0.02796867])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00850146,  0.00998707, -0.10833988, -0.15745144, -0.04025333,
       -0.02516466, -0.12178945,  0.02796867])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00730467, -0.00523218,  0.14277307,  0.13499758,  0.1087602 ,
       -0.03225743,  0.15325573, -0.00052898])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00730467,  0.00523218, -0.14277306, -0.13499758, -0.10876019,
        0.03225743, -0.15325573,  0.00052898])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00665221,  0.01110945,  0.0896481 , -0.04089277, -0.0814207 ,
       -0.06649702, -0.21911826, -0.00540173])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00665221, -0.01110945, -0.0896481 ,  0.04089277,  0.0814207 ,
        0.06649702,  0.21911826,  0.00540173])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([-0.00619304, -0.00856292, -0.14111778, -0.14792846, -0.10312495,
        0.01668623, -0.05390543,  0.0091219 ])
2025-07-15 22:03:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:30 - shap - INFO - phi = array([ 0.00619304,  0.00856292,  0.14111778,  0.14792846,  0.10312495,
       -0.01668623,  0.05390543, -0.0091219 ])
2025-07-15 22:03:30 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.0037552 , -0.00822958,  0.13159194,  0.23443585, -0.04943995,
       -0.04512964,  0.14588452,  0.0042476 ])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.0037552 ,  0.00822958, -0.13159194, -0.23443585,  0.04943995,
        0.04512964, -0.14588452, -0.00424759])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.0081092 ,  0.00301584, -0.26540918,  0.10049443, -0.03502838,
       -0.01082377, -0.18457231, -0.02845626])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.0081092 , -0.00301584,  0.26540918, -0.10049443,  0.03502838,
        0.01082377,  0.18457231,  0.02845626])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.01719029,  0.00482489, -0.19956344, -0.10245041, -0.09330462,
        0.05007766, -0.05727721, -0.01234192])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.01719029, -0.00482489,  0.19956344,  0.10245041,  0.09330462,
       -0.05007766,  0.05727721,  0.01234192])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00418515, -0.00953373,  0.13910866,  0.25853003, -0.09112695,
       -0.02492554,  0.13626544,  0.0083    ])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00418515,  0.00953373, -0.13910866, -0.25853003,  0.09112695,
        0.02492554, -0.13626544, -0.0083    ])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.0068655 ,  0.00132753, -0.17257404, -0.09155167, -0.01220173,
       -0.01247655, -0.17846373, -0.0093685 ])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.0068655 , -0.00132753,  0.17257404,  0.09155167,  0.01220173,
        0.01247655,  0.17846373,  0.0093685 ])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.002113  , -0.00199473,  0.13817371,  0.15653992,  0.04782395,
        0.02486232,  0.14550159,  0.00274778])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.002113  ,  0.00199472, -0.13817371, -0.15653992, -0.04782395,
       -0.02486232, -0.14550159, -0.00274778])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00019373,  0.02907409, -0.1726829 , -0.09999129, -0.06981542,
        0.09212194, -0.1669009 , -0.01480195])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00019373, -0.02907409,  0.1726829 ,  0.09999129,  0.06981542,
       -0.09212194,  0.1669009 ,  0.01480195])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.01707729, -0.04544449,  0.05561635, -0.28043484, -0.22733619,
        0.05372312,  0.06283866,  0.01120659])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.01707729,  0.04544449, -0.05561635,  0.28043484,  0.22733619,
       -0.05372312, -0.06283866, -0.01120659])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00643217, -0.00788197,  0.12877199,  0.12996072,  0.09639051,
        0.02286926,  0.13706239,  0.00080082])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00643217,  0.00788197, -0.128772  , -0.12996072, -0.09639051,
       -0.02286926, -0.13706239, -0.00080082])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00981571,  0.0143239 ,  0.13294674,  0.12676141,  0.03078548,
        0.10248496, -0.17462576,  0.03025796])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00981571, -0.0143239 , -0.13294674, -0.1267614 , -0.03078548,
       -0.10248496,  0.17462576, -0.03025796])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00921841, -0.01359505, -0.08706501, -0.20693133, -0.12076402,
       -0.02122642,  0.03294638, -0.0008925 ])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00921841,  0.01359505,  0.08706501,  0.20693133,  0.12076402,
        0.02122642, -0.03294637,  0.0008925 ])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00798825, -0.01857726,  0.09035279, -0.19472349, -0.23374065,
       -0.03946703,  0.07928371, -0.00428215])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00798825,  0.01857726, -0.09035279,  0.19472349,  0.23374065,
        0.03946704, -0.07928371,  0.00428215])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.0011021 ,  0.01540424,  0.14459159,  0.09507484,  0.11243429,
       -0.02365815,  0.15609449,  0.01528716])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.0011021 , -0.01540424, -0.14459159, -0.09507484, -0.11243429,
        0.02365815, -0.15609449, -0.01528716])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00104713, -0.00960048,  0.09722632, -0.07209571,  0.2136849 ,
        0.05547223,  0.15701298,  0.01576595])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00104713,  0.00960048, -0.09722632,  0.07209571, -0.21368491,
       -0.05547223, -0.15701298, -0.01576595])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.0237744 ,  0.00782944, -0.0400866 , -0.07630361,  0.21026002,
        0.12000294,  0.12887071,  0.03380711])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.0237744 , -0.00782943,  0.0400866 ,  0.07630361, -0.21026002,
       -0.12000294, -0.12887071, -0.03380711])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.01662347, -0.00696982,  0.15798444,  0.14114386,  0.02760058,
       -0.02330215,  0.16421073,  0.00112822])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.01662347,  0.00696982, -0.15798444, -0.14114386, -0.02760058,
        0.02330215, -0.16421073, -0.00112822])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.07874539,  0.06109075, -0.15478313, -0.10014518, -0.13029357,
        0.15047788, -0.03008477, -0.00421259])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.07874539, -0.06109075,  0.15478313,  0.10014518,  0.13029357,
       -0.15047788,  0.03008477,  0.00421259])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00424742, -0.0208792 , -0.17046687, -0.17350097, -0.14501223,
       -0.00463226,  0.05548596, -0.00351247])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00424742,  0.0208792 ,  0.17046687,  0.17350097,  0.14501223,
        0.00463226, -0.05548596,  0.00351247])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00987679, -0.04241419, -0.05027282, -0.22022008, -0.20897649,
        0.01452352,  0.05726738, -0.0118386 ])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00987679,  0.04241419,  0.05027282,  0.22022008,  0.20897649,
       -0.01452352, -0.05726738,  0.0118386 ])
2025-07-15 22:03:31 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([-0.00175121,  0.01210649,  0.12768532,  0.09394493,  0.11403791,
       -0.00643537,  0.15002943,  0.01725954])
2025-07-15 22:03:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:31 - shap - INFO - phi = array([ 0.00175121, -0.01210648, -0.12768531, -0.09394493, -0.11403791,
        0.00643538, -0.15002943, -0.01725954])
2025-07-15 22:03:32 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:32 - shap - INFO - phi = array([ 0.0130109 , -0.01477129, -0.1985116 , -0.10735773, -0.08393852,
        0.02049837, -0.05017612, -0.02015506])
2025-07-15 22:03:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:32 - shap - INFO - phi = array([-0.0130109 ,  0.01477129,  0.1985116 ,  0.10735773,  0.08393852,
       -0.02049837,  0.05017612,  0.02015506])
2025-07-15 22:03:32 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:32 - shap - INFO - phi = array([-0.04212935,  0.02961849, -0.18255065, -0.11613295,  0.08620416,
        0.11100792, -0.24528158,  0.01769982])
2025-07-15 22:03:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:32 - shap - INFO - phi = array([ 0.04212935, -0.02961849,  0.18255065,  0.11613295, -0.08620416,
       -0.11100792,  0.24528158, -0.01769982])
2025-07-15 22:03:32 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:32 - shap - INFO - phi = array([-0.00028048, -0.0099346 ,  0.14542205,  0.17505982,  0.0581998 ,
       -0.0274598 ,  0.15165162,  0.00121762])
2025-07-15 22:03:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:32 - shap - INFO - phi = array([ 0.00028048,  0.0099346 , -0.14542205, -0.17505982, -0.05819979,
        0.0274598 , -0.15165162, -0.00121762])
2025-07-15 22:03:32 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:32 - shap - INFO - phi = array([-0.0054568 ,  0.01850174,  0.14360359,  0.17674757,  0.00393847,
       -0.05674005, -0.23262783,  0.00743755])
2025-07-15 22:03:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:32 - shap - INFO - phi = array([ 0.0054568 , -0.01850173, -0.14360359, -0.17674757, -0.00393847,
        0.05674005,  0.23262783, -0.00743755])
2025-07-15 22:03:36 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_soft
2025-07-15 22:03:36 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:03:36 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:03:36 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:03:36 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:03:36 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:03:36 - enhanced_shap_visualization - INFO - 检测到集成模型，使用KernelExplainer
2025-07-15 22:03:36 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:36 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:36 - shap - INFO - phi = array([ 0.01314286, -0.01547619,  0.09128571, -0.12104762,  0.04466667,
        0.10504762,  0.35185714,  0.03052381])
2025-07-15 22:03:36 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:36 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:36 - shap - INFO - phi = array([ 0.00304762,  0.01138095, -0.1772381 , -0.1947619 , -0.05490476,
        0.02809524, -0.11728571,  0.00166667])
2025-07-15 22:03:36 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:36 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:36 - shap - INFO - phi = array([-0.00135714,  0.00764286,  0.19702381,  0.12640476,  0.10116667,
        0.02264286,  0.04688095, -0.00040476])
2025-07-15 22:03:36 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:36 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:36 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:36 - shap - INFO - phi = array([ 0.00935714,  0.00416667,  0.15754762,  0.12945238, -0.01764286,
        0.03354762,  0.18521429, -0.00164286])
2025-07-15 22:03:36 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:37 - shap - INFO - phi = array([0.01214286, 0.00942857, 0.21642857, 0.0332381 , 0.02242857,
       0.04090476, 0.15019048, 0.0152381 ])
2025-07-15 22:03:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:37 - shap - INFO - phi = array([ 0.00897619, -0.02064286,  0.20778571, -0.14869048, -0.0392619 ,
        0.12069048,  0.35978571,  0.01135714])
2025-07-15 22:03:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:37 - shap - INFO - phi = array([ 0.0047381 ,  0.00559524, -0.18559524, -0.17307143, -0.03392857,
        0.02669048, -0.13016667, -0.0142619 ])
2025-07-15 22:03:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:37 - shap - INFO - phi = array([ 0.01411905, -0.02883333, -0.03321429,  0.28859524,  0.09488095,
        0.04664286,  0.13216667, -0.01435714])
2025-07-15 22:03:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:37 - shap - INFO - phi = array([ 0.00916667,  0.02235714,  0.01154762, -0.03254762, -0.21645238,
       -0.04983333, -0.24911905,  0.00488095])
2025-07-15 22:03:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:37 - shap - INFO - phi = array([-0.00288095,  0.01135714,  0.03054762,  0.30945238,  0.20411905,
        0.0225    , -0.07778571,  0.00269048])
2025-07-15 22:03:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:37 - shap - INFO - phi = array([-0.01930952,  0.02011905,  0.46292857, -0.07411905, -0.01902381,
       -0.08102381,  0.19611905,  0.01430952])
2025-07-15 22:03:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:37 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:37 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:37 - shap - INFO - phi = array([ 0.00630952, -0.0127381 ,  0.29635714,  0.01511905, -0.11092857,
       -0.03978571,  0.33507143,  0.01059524])
2025-07-15 22:03:37 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:38 - shap - INFO - phi = array([ 0.00669048,  0.0177381 , -0.03811905, -0.15040476, -0.07545238,
       -0.04245238, -0.22497619,  0.00697619])
2025-07-15 22:03:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:38 - shap - INFO - phi = array([ 0.01092857,  0.01978571,  0.14664286,  0.18607143,  0.28959524,
       -0.08778571, -0.07302381,  0.00778571])
2025-07-15 22:03:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:38 - shap - INFO - phi = array([ 0.00957143, -0.01538095, -0.20057143, -0.3342381 ,  0.04414286,
        0.03161905, -0.01471429, -0.02042857])
2025-07-15 22:03:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:38 - shap - INFO - phi = array([-0.03304762,  0.00571429, -0.09052381,  0.07628571, -0.3152381 ,
        0.03133333, -0.15438095, -0.02014286])
2025-07-15 22:03:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:38 - shap - INFO - phi = array([-0.21207143, -0.19040476,  0.01045238,  0.16088095,  0.09802381,
       -0.1667381 , -0.20602381,  0.00588095])
2025-07-15 22:03:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:38 - shap - INFO - phi = array([ 0.00369048,  0.0075    , -0.14740476, -0.14459524, -0.10521429,
        0.02035714, -0.12316667, -0.01116667])
2025-07-15 22:03:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:38 - shap - INFO - phi = array([ 0.0012381 ,  0.00238095, -0.1427619 , -0.15761905, -0.09509524,
        0.02257143, -0.1297619 , -0.00095238])
2025-07-15 22:03:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:38 - shap - INFO - phi = array([0.00519048, 0.00552381, 0.09171429, 0.14304762, 0.05928571,
       0.0287619 , 0.16452381, 0.00195238])
2025-07-15 22:03:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:38 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:38 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:38 - shap - INFO - phi = array([ 0.02485714, -0.0327619 , -0.06042857,  0.32552381,  0.01033333,
        0.05209524,  0.16933333,  0.01104762])
2025-07-15 22:03:38 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:39 - shap - INFO - phi = array([ 0.00678571,  0.01478571, -0.05140476,  0.34469048,  0.2107381 ,
        0.04392857, -0.0727381 ,  0.00321429])
2025-07-15 22:03:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:39 - shap - INFO - phi = array([ 0.00907143, -0.03783333,  0.18716667, -0.14216667,  0.03154762,
        0.12183333,  0.35469048, -0.02430952])
2025-07-15 22:03:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:39 - shap - INFO - phi = array([ 0.00157143,  0.00590476, -0.13971429, -0.13852381, -0.1142381 ,
        0.01909524, -0.12804762, -0.00604762])
2025-07-15 22:03:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:39 - shap - INFO - phi = array([ 0.04995238,  0.00528571, -0.09766667,  0.14809524, -0.09509524,
        0.13980952,  0.351     , -0.00138095])
2025-07-15 22:03:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:39 - shap - INFO - phi = array([ 0.00466667,  0.007     , -0.17995238, -0.30047619,  0.04128571,
       -0.07266667, -0.00285714,  0.003     ])
2025-07-15 22:03:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:39 - shap - INFO - phi = array([-0.0342619 ,  0.00230952, -0.08945238,  0.07240476, -0.28521429,
        0.01402381, -0.16859524, -0.01121429])
2025-07-15 22:03:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:39 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:39 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:39 - shap - INFO - phi = array([ 0.00602381,  0.00788095, -0.14802381, -0.1365    , -0.10916667,
        0.01497619, -0.12664286, -0.00854762])
2025-07-15 22:03:39 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:40 - shap - INFO - phi = array([ 0.01035714,  0.01397619,  0.20416667,  0.14878571,  0.1217381 ,
       -0.08611905,  0.0805    ,  0.00659524])
2025-07-15 22:03:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:40 - shap - INFO - phi = array([ 0.01161905,  0.03395238, -0.03128571, -0.24242857, -0.0247619 ,
       -0.06895238, -0.19590476,  0.0177619 ])
2025-07-15 22:03:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:40 - shap - INFO - phi = array([ 0.01171429, -0.00390476, -0.11090476, -0.0712381 , -0.06952381,
       -0.11885714, -0.11861905, -0.01866667])
2025-07-15 22:03:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:40 - shap - INFO - phi = array([-0.01045238,  0.00783333, -0.09240476,  0.08140476, -0.29340476,
        0.00292857, -0.18483333, -0.01107143])
2025-07-15 22:03:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:40 - shap - INFO - phi = array([ 0.00461905,  0.01866667, -0.11771429, -0.16280952, -0.07671429,
       -0.03947619, -0.1342381 ,  0.00766667])
2025-07-15 22:03:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:40 - shap - INFO - phi = array([-0.0042619 ,  0.00607143, -0.13511905, -0.13145238, -0.12559524,
        0.01869048, -0.12883333,  0.0005    ])
2025-07-15 22:03:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:40 - shap - INFO - phi = array([ 0.00697619, -0.01935714, -0.09883333,  0.11854762,  0.12488095,
        0.10364286,  0.27097619, -0.00683333])
2025-07-15 22:03:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:40 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:40 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:40 - shap - INFO - phi = array([ 0.00883333,  0.00764286,  0.1687381 ,  0.15711905,  0.12464286,
       -0.0155    ,  0.05435714, -0.00583333])
2025-07-15 22:03:40 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:41 - shap - INFO - phi = array([-0.00342857,  0.0162381 , -0.15757143, -0.29090476,  0.05147619,
        0.04471429, -0.15947619, -0.00104762])
2025-07-15 22:03:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:41 - shap - INFO - phi = array([ 0.00952381,  0.00333333,  0.38133333, -0.10404762,  0.05352381,
        0.00747619,  0.1242381 ,  0.02461905])
2025-07-15 22:03:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:41 - shap - INFO - phi = array([-0.01121429, -0.00078571,  0.22421429,  0.12302381,  0.11007143,
       -0.05183333,  0.10183333,  0.00469048])
2025-07-15 22:03:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:41 - shap - INFO - phi = array([ 2.38095238e-04,  6.47619048e-03, -1.75000000e-01, -3.12428571e-01,
        7.18095238e-02,  1.41904762e-02, -1.03523810e-01, -1.76190476e-03])
2025-07-15 22:03:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:41 - shap - INFO - phi = array([0.00521429, 0.00183333, 0.19007143, 0.10954762, 0.0155    ,
       0.01564286, 0.15083333, 0.01135714])
2025-07-15 22:03:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:41 - shap - INFO - phi = array([ 0.00561905,  0.01428571, -0.12880952, -0.15938095, -0.07333333,
       -0.03266667, -0.12952381,  0.00380952])
2025-07-15 22:03:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:41 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:41 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:41 - shap - INFO - phi = array([ 0.00892857, -0.02383333,  0.20407143,  0.12630952,  0.08702381,
       -0.07321429,  0.16335714,  0.00735714])
2025-07-15 22:03:41 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:42 - shap - INFO - phi = array([-0.01383333,  0.06788095, -0.05464286,  0.34454762,  0.31683333,
       -0.05954762, -0.09045238, -0.01078571])
2025-07-15 22:03:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:42 - shap - INFO - phi = array([ 0.00807143,  0.01235714, -0.12788095, -0.13364286, -0.10959524,
       -0.02483333, -0.12807143,  0.00359524])
2025-07-15 22:03:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:42 - shap - INFO - phi = array([ 0.012     , -0.02095238, -0.18385714, -0.26014286, -0.03047619,
       -0.09042857,  0.08847619, -0.01461905])
2025-07-15 22:03:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:42 - shap - INFO - phi = array([ 0.00697619,  0.0112619 ,  0.04116667,  0.32383333,  0.1275    ,
        0.02135714, -0.03359524,  0.0015    ])
2025-07-15 22:03:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:42 - shap - INFO - phi = array([ 0.00859524,  0.01754762, -0.09692857,  0.28935714,  0.36288095,
        0.03683333, -0.11611905, -0.00216667])
2025-07-15 22:03:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:42 - shap - INFO - phi = array([ 0.00409524, -0.01552381, -0.13390476, -0.12528571, -0.10919048,
        0.01842857, -0.12957143, -0.00904762])
2025-07-15 22:03:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:42 - shap - INFO - phi = array([ 0.0015    ,  0.00521429, -0.0942619 ,  0.06078571, -0.22064286,
       -0.06935714, -0.17935714, -0.00388095])
2025-07-15 22:03:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:42 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:42 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:42 - shap - INFO - phi = array([-0.01459524,  0.00978571, -0.00278571,  0.07216667, -0.30378571,
       -0.0962619 , -0.14797619, -0.01654762])
2025-07-15 22:03:42 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:43 - shap - INFO - phi = array([-0.01585714,  0.023     , -0.17733333, -0.167     , -0.03671429,
        0.02942857, -0.15819048,  0.00266667])
2025-07-15 22:03:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:43 - shap - INFO - phi = array([-0.07671429, -0.0517619 ,  0.27604762,  0.16933333,  0.24295238,
       -0.15309524,  0.08138095,  0.01185714])
2025-07-15 22:03:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:43 - shap - INFO - phi = array([ 0.00504762,  0.012     ,  0.17809524,  0.169     ,  0.17261905,
        0.009     , -0.0577619 ,  0.012     ])
2025-07-15 22:03:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:43 - shap - INFO - phi = array([-0.02483333,  0.02245238,  0.00545238,  0.29611905,  0.30716667,
       -0.02111905, -0.08997619,  0.0047381 ])
2025-07-15 22:03:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:43 - shap - INFO - phi = array([ 0.00540476, -0.01430952, -0.11897619, -0.13230952, -0.11088095,
        0.01145238, -0.1337381 , -0.00664286])
2025-07-15 22:03:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:43 - shap - INFO - phi = array([-0.00845238,  0.01369048,  0.21169048,  0.13088095,  0.10935714,
       -0.03216667,  0.06035714,  0.01464286])
2025-07-15 22:03:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:43 - shap - INFO - phi = array([ 0.0452619 , -0.02554762,  0.17569048,  0.13478571, -0.09654762,
       -0.10492857,  0.38502381, -0.0137381 ])
2025-07-15 22:03:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:43 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:43 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:43 - shap - INFO - phi = array([ 0.00240476,  0.00716667, -0.14116667, -0.19911905, -0.06754762,
        0.0255    , -0.1275    ,  0.0002619 ])
2025-07-15 22:03:43 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:44 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:44 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:44 - shap - INFO - phi = array([ 0.00202381, -0.03411905, -0.26540476, -0.33130952, -0.02721429,
        0.04211905,  0.11792857, -0.00402381])
2025-07-15 22:03:48 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for voting_hard
2025-07-15 22:03:48 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:03:48 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:03:48 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:03:48 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:03:48 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:03:48 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._stacking.StackingClassifier'>, falling back to KernelExplainer
2025-07-15 22:03:48 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.00676218,  0.02352384, -0.07873103,  0.10029542, -0.03953805,
       -0.06311501, -0.28680159, -0.00904306])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.00676218, -0.02352384,  0.07873103, -0.10029542,  0.03953805,
        0.06311501,  0.28680159,  0.00904306])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.00046041, -0.00991789,  0.13145072,  0.17604525,  0.04271834,
       -0.02518637,  0.13511663,  0.00118936])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.00046041,  0.00991789, -0.13145072, -0.17604525, -0.04271834,
        0.02518637, -0.13511663, -0.00118936])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.00889701, -0.00770429, -0.16513852, -0.10472533, -0.07795841,
       -0.0265406 , -0.04777776, -0.00495096])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.00889701,  0.00770429,  0.16513852,  0.10472533,  0.07795841,
        0.0265406 ,  0.04777776,  0.00495096])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.0143045 , -0.00296154, -0.14909339, -0.09112476,  0.01754653,
       -0.03180132, -0.17432128,  0.01035918])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.0143045 ,  0.00296154,  0.14909339,  0.09112476, -0.01754653,
        0.03180132,  0.17432128, -0.01035918])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.01149061, -0.00305866, -0.18993193, -0.02167073,  0.00100527,
       -0.03062166, -0.16786072, -0.01384019])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.01149061,  0.00305866,  0.18993193,  0.02167073, -0.00100527,
        0.03062166,  0.16786072,  0.01384019])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.00500328,  0.02439522, -0.11422947,  0.12287758,  0.03232381,
       -0.06645715, -0.29349786, -0.01531105])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.00500328, -0.02439522,  0.11422947, -0.12287758, -0.03232381,
        0.06645715,  0.29349786,  0.01531105])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.00529472,  0.00498421,  0.13337438,  0.16361951,  0.02936311,
       -0.02752436,  0.14586911,  0.01393052])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.00529472, -0.00498421, -0.13337438, -0.16361951, -0.02936311,
        0.02752436, -0.14586911, -0.01393052])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.01014659,  0.01764014,  0.0404629 , -0.18712108, -0.08303659,
       -0.04436639, -0.16395953,  0.00747975])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.01014659, -0.01764014, -0.0404629 ,  0.18712108,  0.08303659,
        0.04436639,  0.16395953, -0.00747975])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.00901766, -0.01898593, -0.06413477,  0.00757403,  0.12413958,
        0.05504594,  0.17044107, -0.01703013])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.00901766,  0.01898593,  0.06413477, -0.00757403, -0.12413958,
       -0.05504594, -0.17044107,  0.01703013])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.00158847, -0.01709249, -0.0651998 , -0.22252236, -0.16048452,
       -0.01745734,  0.06643672, -0.00585071])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.00158847,  0.01709249,  0.0651998 ,  0.22252236,  0.16048452,
        0.01745734, -0.06643672,  0.00585071])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.03130642, -0.0204538 , -0.35165079,  0.04838474,  0.00516203,
        0.05296847, -0.08136495, -0.03601625])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.03130642,  0.0204538 ,  0.35165079, -0.04838474, -0.00516203,
       -0.05296847,  0.08136495,  0.03601625])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.00860313,  0.01922552, -0.22625254, -0.03183294,  0.10454462,
        0.03119952, -0.26714054, -0.01283605])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.00860313, -0.01922552,  0.22625254,  0.03183294, -0.10454462,
       -0.03119952,  0.26714054,  0.01283605])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([-0.00701358, -0.01238045, -0.00355679,  0.12280376,  0.05039954,
        0.02646896,  0.16930243, -0.01141607])
2025-07-15 22:03:48 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:48 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:48 - shap - INFO - phi = array([ 0.00701358,  0.01238045,  0.00355679, -0.12280376, -0.05039954,
       -0.02646896, -0.16930243,  0.01141607])
2025-07-15 22:03:48 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00534124, -0.02687582, -0.1485853 , -0.17788564, -0.18529482,
        0.07885324,  0.06260246, -0.00662566])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00534124,  0.02687582,  0.1485853 ,  0.17788564,  0.18529482,
       -0.07885324, -0.06260246,  0.00662566])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00497773,  0.0073479 ,  0.14878767,  0.19822819, -0.05903242,
       -0.03994189, -0.06667577,  0.00448169])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00497773, -0.0073479 , -0.14878767, -0.19822819,  0.05903242,
        0.03994189,  0.06667577, -0.00448169])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.03066381, -0.01352288,  0.0931089 , -0.07773504,  0.23788078,
       -0.0372311 ,  0.1678905 ,  0.0198989 ])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.03066381,  0.01352288, -0.0931089 ,  0.07773504, -0.23788078,
        0.0372311 , -0.1678905 , -0.0198989 ])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.11345187,  0.09477093, -0.06991451, -0.16593522, -0.11790789,
        0.05916595,  0.11786514, -0.00674079])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.11345187, -0.09477093,  0.06991451,  0.16593522,  0.11790789,
       -0.05916595, -0.11786514,  0.00674079])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00369797, -0.00651457,  0.12845589,  0.12354993,  0.09500058,
       -0.02651344,  0.13493402,  0.01087165])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00369797,  0.00651457, -0.12845589, -0.12354993, -0.09500058,
        0.02651344, -0.13493402, -0.01087165])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00063063, -0.00651337,  0.13290205,  0.12871168,  0.07974857,
       -0.02353863,  0.14678056, -0.0017798 ])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00063063,  0.00651337, -0.13290205, -0.12871168, -0.07974857,
        0.02353863, -0.14678056,  0.0017798 ])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00642543, -0.00156456, -0.06546631, -0.12240173, -0.06415431,
       -0.03118268, -0.14592518, -0.00410923])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([0.00642543, 0.00156456, 0.06546631, 0.12240173, 0.06415431,
       0.03118268, 0.14592518, 0.00410923])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.02045109,  0.02655519,  0.05366943, -0.2221888 , -0.0012885 ,
       -0.04489481, -0.21138285, -0.00024574])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.02045109, -0.02655519, -0.05366943,  0.2221888 ,  0.0012885 ,
        0.04489481,  0.21138285,  0.00024574])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00537457, -0.01187829,  0.04993264, -0.29703709, -0.17488973,
       -0.04686244,  0.06170202, -0.00512678])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00537457,  0.01187829, -0.04993264,  0.29703709,  0.17488973,
        0.04686244, -0.06170202,  0.00512678])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00754111,  0.02957429, -0.0938351 ,  0.08930645, -0.0467543 ,
       -0.06912134, -0.27037312,  0.01903919])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00754111, -0.02957429,  0.0938351 , -0.08930645,  0.0467543 ,
        0.06912134,  0.27037312, -0.01903919])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00070898, -0.0065111 ,  0.12438287,  0.12401425,  0.0988293 ,
       -0.02598439,  0.13356184,  0.01072522])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00070898,  0.0065111 , -0.12438287, -0.12401425, -0.0988293 ,
        0.02598439, -0.13356184, -0.01072522])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.05625367,  0.01455801,  0.08844879, -0.04374592,  0.11033556,
       -0.0804902 , -0.28700383,  0.00688255])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.05625367, -0.01455801, -0.08844879,  0.04374592, -0.11033556,
        0.0804902 ,  0.28700383, -0.00688255])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00448842, -0.00343584,  0.1387563 ,  0.23994684, -0.03999074,
        0.08416916, -0.01941753, -0.00055041])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00448842,  0.00343584, -0.1387563 , -0.23994684,  0.03999074,
       -0.08416916,  0.01941753,  0.00055041])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.03202183,  0.00236251,  0.08129935, -0.07015697,  0.22283537,
       -0.01486051,  0.17585169,  0.01828278])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.03202183, -0.00236251, -0.08129935,  0.07015697, -0.22283537,
        0.01486051, -0.17585169, -0.01828278])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00256354, -0.00748613,  0.12635296,  0.12130646,  0.09653196,
       -0.02279798,  0.13564499,  0.01022383])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00256354,  0.00748613, -0.12635296, -0.12130646, -0.09653196,
        0.02279798, -0.13564499, -0.01022383])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00943888, -0.01482653, -0.17533953, -0.13530659, -0.10805743,
        0.09292927, -0.03405868, -0.00810859])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00943888,  0.01482653,  0.17533953,  0.13530659,  0.10805743,
       -0.09292927,  0.03405868,  0.00810859])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.00901065, -0.01420546, -0.01457248,  0.1327201 ,  0.01223211,
        0.05595421,  0.15328995, -0.00739055])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.00901065,  0.01420546,  0.01457248, -0.1327201 , -0.01223211,
       -0.05595421, -0.15328995,  0.00739055])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.01186647,  0.01086304,  0.11101379,  0.03988496,  0.05151083,
        0.08849495,  0.09964285,  0.01749083])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.01186647, -0.01086304, -0.11101379, -0.03988496, -0.05151083,
       -0.08849495, -0.09964285, -0.01749083])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.01422636, -0.00741513,  0.08879477, -0.07423014,  0.2320705 ,
        0.00149749,  0.16732691,  0.01751973])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.01422636,  0.00741513, -0.08879477,  0.07423014, -0.2320705 ,
       -0.00149749, -0.16732691, -0.01751973])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([-0.0060219 , -0.00945361,  0.11073505,  0.15121208,  0.04733686,
        0.02714618,  0.11985925, -0.01421085])
2025-07-15 22:03:49 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:49 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:49 - shap - INFO - phi = array([ 0.0060219 ,  0.00945361, -0.11073505, -0.15121208, -0.04733686,
       -0.02714618, -0.11985925,  0.01421085])
2025-07-15 22:03:49 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00409744, -0.00765085,  0.12571791,  0.12257379,  0.10119984,
       -0.02683115,  0.13942022, -0.00154015])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00409744,  0.00765085, -0.12571791, -0.12257379, -0.10119984,
        0.02683115, -0.13942022,  0.00154015])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00792476,  0.01714922,  0.08224695, -0.05506012, -0.08221088,
       -0.0760188 , -0.23113044, -0.00275809])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00792476, -0.01714922, -0.08224695,  0.05506012,  0.08221088,
        0.0760188 ,  0.23113044,  0.00275809])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00521423, -0.006806  , -0.14295054, -0.14095136, -0.10219422,
        0.01631819, -0.05025021,  0.00909167])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00521423,  0.006806  ,  0.14295054,  0.14095136,  0.10219422,
       -0.01631819,  0.05025021, -0.00909167])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.01081989, -0.01055617,  0.13118312,  0.24152255, -0.04491566,
       -0.03689076,  0.13894193,  0.00032235])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.01081989,  0.01055617, -0.13118312, -0.24152255,  0.04491566,
        0.03689076, -0.13894193, -0.00032235])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00663197,  0.00346024, -0.27124081,  0.09304662, -0.03678076,
       -0.00840616, -0.17103106, -0.02266072])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00663197, -0.00346024,  0.27124081, -0.09304662,  0.03678076,
        0.00840616,  0.17103106,  0.02266072])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.01647247,  0.00151721, -0.19914077, -0.10451164, -0.09785763,
        0.05029282, -0.05789711, -0.01137298])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.01647247, -0.00151721,  0.19914077,  0.10451164,  0.09785763,
       -0.05029282,  0.05789711,  0.01137298])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.0015445 , -0.01004442,  0.13625158,  0.26629186, -0.07209159,
       -0.02394465,  0.12863227,  0.00487054])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.0015445 ,  0.01004442, -0.13625158, -0.26629186,  0.07209159,
        0.02394465, -0.12863227, -0.00487054])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00585952,  0.00187534, -0.16028428, -0.08741069, -0.00711014,
       -0.00959191, -0.16438874, -0.00839396])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00585952, -0.00187534,  0.16028428,  0.08741069,  0.00711014,
        0.00959191,  0.16438874,  0.00839396])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00236262, -0.00650395,  0.12353008,  0.14055828,  0.05221471,
        0.02455219,  0.12638964, -0.00082587])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00236262,  0.00650395, -0.12353008, -0.14055828, -0.05221471,
       -0.02455219, -0.12638964,  0.00082587])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00303304,  0.02906559, -0.16821875, -0.10290929, -0.07182485,
        0.08340215, -0.16152396, -0.01282275])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00303304, -0.02906559,  0.16821875,  0.10290929,  0.07182485,
       -0.08340215,  0.16152396,  0.01282275])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.01726342, -0.0451667 ,  0.05369296, -0.28878056, -0.24201813,
        0.04818633,  0.06777054,  0.01114847])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.01726342,  0.0451667 , -0.05369296,  0.28878056,  0.24201813,
       -0.04818633, -0.06777054, -0.01114847])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00592232, -0.00799686,  0.11157678,  0.11997084,  0.0934951 ,
        0.02340975,  0.12154319, -0.00078885])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00592232,  0.00799686, -0.11157678, -0.11997084, -0.0934951 ,
       -0.02340975, -0.12154319,  0.00078885])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00978903,  0.01682681,  0.14716265,  0.1533515 ,  0.03419044,
        0.11112714, -0.1412563 ,  0.02836734])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00978903, -0.01682681, -0.14716265, -0.1533515 , -0.03419044,
       -0.11112714,  0.1412563 , -0.02836734])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00731173, -0.01220256, -0.06627379, -0.22263798, -0.12412267,
       -0.01911645,  0.03545994, -0.00311954])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00731173,  0.01220256,  0.06627379,  0.22263798,  0.12412267,
        0.01911645, -0.03545994,  0.00311954])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.0104421 , -0.01892824,  0.07935799, -0.21173347, -0.24801961,
       -0.03917171,  0.07973014, -0.00355636])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.0104421 ,  0.01892824, -0.07935799,  0.21173347,  0.24801961,
        0.03917171, -0.07973014,  0.00355636])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00032107,  0.01462973,  0.12332775,  0.09365827,  0.10108693,
       -0.02417131,  0.13763723,  0.0127456 ])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00032107, -0.01462973, -0.12332775, -0.09365827, -0.10108693,
        0.02417131, -0.13763723, -0.0127456 ])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.00362069, -0.01171085,  0.08776495, -0.06183944,  0.21383158,
        0.04945061,  0.15539577,  0.01410102])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.00362069,  0.01171085, -0.08776495,  0.06183944, -0.21383158,
       -0.04945061, -0.15539577, -0.01410102])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.02194553,  0.00213981, -0.01501261, -0.07014939,  0.2179647 ,
        0.11018381,  0.13278663,  0.02725697])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.02194553, -0.00213981,  0.01501261,  0.07014939, -0.2179647 ,
       -0.11018381, -0.13278663, -0.02725697])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([ 0.01514657, -0.00926337,  0.14889903,  0.13764887,  0.03270698,
       -0.02561577,  0.15153271, -0.00151948])
2025-07-15 22:03:50 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:50 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:50 - shap - INFO - phi = array([-0.01514657,  0.00926337, -0.14889903, -0.13764887, -0.03270698,
        0.02561577, -0.15153271,  0.00151948])
2025-07-15 22:03:50 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([ 0.08418113,  0.06711534, -0.1689743 , -0.11134011, -0.15253306,
        0.15969514, -0.02639695, -0.00672668])
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([-0.08418113, -0.06711534,  0.1689743 ,  0.11134011,  0.15253306,
       -0.15969514,  0.02639695,  0.00672668])
2025-07-15 22:03:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([-0.00282742, -0.01538048, -0.16053594, -0.15743321, -0.14434152,
       -0.00177801,  0.05144156, -0.00483177])
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([ 0.00282742,  0.01538048,  0.16053594,  0.15743321,  0.14434152,
        0.00177801, -0.05144156,  0.00483177])
2025-07-15 22:03:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([ 0.01606859, -0.03468945, -0.03520227, -0.22849802, -0.21902138,
        0.01655033,  0.06452561, -0.00959493])
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([-0.01606859,  0.03468945,  0.03520227,  0.22849802,  0.21902138,
       -0.01655033, -0.06452561,  0.00959493])
2025-07-15 22:03:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([-0.00248364,  0.01344592,  0.10942883,  0.09400229,  0.10264043,
       -0.00663066,  0.13208446,  0.01402597])
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([ 0.00248364, -0.01344592, -0.10942883, -0.09400229, -0.10264043,
        0.00663066, -0.13208446, -0.01402597])
2025-07-15 22:03:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([ 0.01347278, -0.01238769, -0.19325689, -0.10726251, -0.08510702,
        0.02500366, -0.0497128 , -0.0163749 ])
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([-0.01347278,  0.01238769,  0.19325689,  0.10726251,  0.08510702,
       -0.02500366,  0.0497128 ,  0.0163749 ])
2025-07-15 22:03:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([-0.041613  ,  0.03029752, -0.18504556, -0.11176487,  0.0857205 ,
        0.1039751 , -0.26896544,  0.01709936])
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([ 0.041613  , -0.03029752,  0.18504556,  0.11176487, -0.0857205 ,
       -0.1039751 ,  0.26896544, -0.01709936])
2025-07-15 22:03:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([-0.00181344, -0.00934922,  0.1342545 ,  0.16109485,  0.05728711,
       -0.02654191,  0.14034683, -0.00183211])
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([ 0.00181344,  0.00934922, -0.1342545 , -0.16109485, -0.05728711,
        0.02654191, -0.14034683,  0.00183211])
2025-07-15 22:03:51 - shap - INFO - num_full_subsets = 4
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([-0.0052912 ,  0.02634407,  0.1562228 ,  0.19733201,  0.0062252 ,
       -0.06027596, -0.21432397,  0.01089626])
2025-07-15 22:03:51 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-07-15 22:03:51 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-07-15 22:03:51 - shap - INFO - phi = array([ 0.0052912 , -0.02634407, -0.1562228 , -0.19733201, -0.0062252 ,
        0.06027596,  0.21432397, -0.01089626])
2025-07-15 22:03:55 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for stacking
2025-07-15 22:03:55 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:03:55 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:03:55 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:03:55 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:03:59 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for FeatureNameTest
