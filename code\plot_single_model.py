#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端，避免GUI警告
import shap
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, recall_score, f1_score, roc_curve, auc, ConfusionMatrixDisplay, confusion_matrix, precision_score
from sklearn.calibration import calibration_curve
from sklearn.preprocessing import label_binarize
import numpy as np
from joblib import load, dump
from pathlib import Path
import os

# 尝试导入配置模块和绘图工具
try:
    from config import OUTPUT_PATH, CACHE_PATH, MODEL_DISPLAY_NAMES, PLOT_CONFIG
    from plot_utils import translate_term, get_save_path, save_plot as utils_save_plot
except ImportError:
    # 通过获取当前文件的路径确定项目根目录
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    # 使用默认配置
    from config import MODEL_DISPLAY_NAMES, PLOT_CONFIG

# 尝试导入日志模块
try:
    from logger import get_default_logger
    logger = get_default_logger("plot_single_model")
except ImportError:
    import logging
    logger = logging.getLogger("plot_single_model")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

# 配置字典
CONFIG = {
    'output_path': OUTPUT_PATH,
    'cache_path': CACHE_PATH,
    'thresholds': np.linspace(0, 0.5, 51),
    'dpi': PLOT_CONFIG.get('dpi', 150),
    'figsize': PLOT_CONFIG.get('figsize', (10, 8))
}

# 配置matplotlib - 从plot_utils导入
try:
    from plot_utils import get_font_properties
except ImportError:
    # 如果无法导入，提供一个简单的实现
    def get_font_properties():
        import platform
        try:
            from matplotlib.font_manager import FontProperties
            if platform.system() == 'Windows':
                try:
                    return FontProperties(fname=r"C:\Windows\Fonts\msyh.ttc")  # 微软雅黑
                except:
                    try:
                        return FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf")  # 黑体
                    except:
                        return FontProperties(family=['Microsoft YaHei', 'SimHei', 'SimSun', 'sans-serif'])
            else:
                return FontProperties(family=['SimHei', 'DejaVu Sans', 'sans-serif'])
        except Exception as e:
            logger.warning(f"获取字体属性失败: {e}")
            return None

# 英文术语映射
TERM_MAPPING = {
    '假阳性率': 'False Positive Rate (FPR)',
    '真阳性率': 'True Positive Rate (TPR)',
    'ROC曲线': 'ROC Curve',
    '混淆矩阵': 'Confusion Matrix',
    '预测类别': 'Predicted Class',
    '真实类别': 'True Class',
    '决策曲线分析': 'Decision Curve Analysis',
    '阈值概率': 'Threshold Probability',
    '净收益': 'Net Benefit',
    '临床影响曲线': 'Clinical Impact Curve',
    '风险阈值': 'Risk Threshold',
    '高风险比例': 'High-risk Proportion',
    '校准曲线': 'Calibration Curve',
    '预测概率': 'Predicted Probability',
    '真实概率': 'Actual Probability',
    '完美校准': 'Perfect Calibration',
    'SHAP摘要图': 'SHAP Summary Plot',
    'SHAP依赖图': 'SHAP Dependence Plot',
    '决策图': 'Decision Plot',
    '瀑布图': 'Waterfall Plot',
    '模型性能雷达图': 'Model Performance Radar Chart',
    '样本': 'Sample',
    '准确率': 'Accuracy',
    '精确率': 'Precision',
    '召回率': 'Recall',
    '特异性': 'Specificity',
    'F1分数': 'F1 Score'
}

def translate(term):
    """将中文术语翻译为英文"""
    return TERM_MAPPING.get(term, term)

def save_plot(fig, filename, model_name, plot_type=None):
    """
    保存图表到特定模型的目录
    
    Args:
        fig: 图形对象
        filename: 文件名
        model_name: 模型名称
        plot_type: 绘图类型
    """
    try:
        # 确保目标目录存在
        model_dir = CONFIG['output_path'] / model_name
        if not os.path.exists(model_dir):
            os.makedirs(model_dir)

        # 指定完整的保存路径
        filepath = model_dir / filename
        
        # 保存图表
        fig.savefig(filepath, dpi=CONFIG['dpi'], bbox_inches='tight')
        plt.close(fig)
        logger.info(f"图表已保存至: {filepath}")
    except Exception as e:
        logger.error(f"保存图表 {filename} 失败: {e}")
        plt.close(fig)

def plot_curve(ax, x, y, label, title, xlabel, ylabel, color=None, linestyle='-'):
    ax.plot(x, y, label=label, color=color, linestyle=linestyle)
    ax.set_title(translate(title))
    ax.set_xlabel(translate(xlabel))
    ax.set_ylabel(translate(ylabel))
    ax.legend(loc='best')

def calculate_dca(y_true, y_prob, thresholds):
    net_benefit = []
    for thresh in thresholds:
        y_pred = (y_prob >= thresh).astype(int)
        tp = np.sum((y_true == 1) & (y_pred == 1))
        fp = np.sum((y_true == 0) & (y_pred == 1))
        nb = (tp / len(y_true)) - (fp / len(y_true)) * (thresh / (1 - thresh))
        net_benefit.append(nb)
    return np.array(net_benefit)

def calculate_cic(y_prob, thresholds):
    cic_values = []
    for thresh in thresholds:
        high_risk = np.mean(y_prob >= thresh)
        cic_values.append(high_risk)
    return np.array(cic_values)

def plot_model_visualizations(model_name, enable_shap=True):
    """
    为指定模型生成可视化图表

    Args:
        model_name: 模型名称
        enable_shap: 是否启用SHAP分析
    """
    cache_file = CACHE_PATH / f"{model_name}_results.joblib"
    if not cache_file.exists():
        logger.error(f"缓存文件不存在: {cache_file}")
        return
    data = load(cache_file)
    logger.info(f"加载模型 {model_name} 的缓存结果")
    model = data['model']
    X_test = data['X_test']
    y_test = data['y_true']
    y_pred = data['y_pred']
    y_pred_proba = data['y_pred_proba'] if 'y_pred_proba' in data and data['y_pred_proba'].shape == y_test.shape else y_pred
    colors = sns.color_palette('tab10', 1)

    # 确保模型目录存在
    model_dir = CONFIG['output_path'] / model_name
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)
    
    # 获取模型显示名称
    display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)

    # 1. DCA 曲线
    fig, ax = plt.subplots(figsize=CONFIG['figsize'])
    dca_data = calculate_dca(y_test, y_pred_proba, CONFIG['thresholds'])
    plot_curve(ax, CONFIG['thresholds'], dca_data, display_name, '决策曲线分析', '阈值概率', '净收益', color=colors[0])
    ax.plot(CONFIG['thresholds'], np.zeros_like(CONFIG['thresholds']), 'k--')
    save_plot(fig, f"{model_name}_dca.png", model_name, 'dca')

    # 2. CIC 曲线
    fig, ax = plt.subplots(figsize=CONFIG['figsize'])
    cic_data = calculate_cic(y_pred_proba, CONFIG['thresholds'])
    plot_curve(ax, CONFIG['thresholds'], cic_data, display_name, '临床影响曲线', '风险阈值', '高风险比例', color=colors[0])
    save_plot(fig, f"{model_name}_cic.png", model_name, 'cic')

    # 3. ROC 曲线
    fig, ax = plt.subplots(figsize=CONFIG['figsize'])
    fpr, tpr, _ = roc_curve(y_test, y_pred_proba) if y_pred_proba.shape == y_test.shape else ([0, 1], [0, 1], [0, 1])
    roc_auc = auc(fpr, tpr) if y_pred_proba.shape == y_test.shape else 0.5
    plot_curve(ax, fpr, tpr, f"{display_name} (AUC={roc_auc:.2f})", 'ROC曲线', '假阳性率', '真阳性率', color=colors[0])
    ax.plot([0, 1], [0, 1], 'k--')
    save_plot(fig, f"{model_name}_roc.png", model_name, 'roc')

    # 4. 校准曲线
    fig, ax = plt.subplots(figsize=CONFIG['figsize'])
    prob_true, prob_pred = calibration_curve(y_test, y_pred_proba, n_bins=10) if y_pred_proba.shape == y_test.shape else ([0, 1], [0, 1])
    ax.plot(prob_pred, prob_true, 'o-', label=display_name, color=colors[0])
    ax.plot([0, 1], [0, 1], 'k:', label=translate('完美校准'))
    ax.set_title(translate('校准曲线'))
    ax.set_xlabel(translate('预测概率'))
    ax.set_ylabel(translate('真实概率'))
    ax.legend(loc='best')
    save_plot(fig, f"{model_name}_cal.png", model_name, 'calibration')

    # 5. 混淆矩阵
    fig, ax = plt.subplots(figsize=CONFIG['figsize'])
    ConfusionMatrixDisplay.from_predictions(y_test, y_pred, ax=ax, cmap='Blues')
    ax.set_title(translate('混淆矩阵'))
    save_plot(fig, f"{model_name}_cm.png", model_name, 'confusion_matrix')

    # 6. SHAP 分析（如果启用）
    if enable_shap:
        try:
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X_test)
        except Exception as e:
            logger.warning(f"TreeExplainer 失败: {e}, 回退到 KernelExplainer")
            try:
                explainer = shap.KernelExplainer(model.predict_proba, X_test)
                shap_values = explainer.shap_values(X_test)
            except Exception as e2:
                logger.error(f"SHAP分析失败: {e2}")
                enable_shap = False

        # 确保我们只为摘要图使用正类（class 1）的SHAP值
        shap_values_for_summary = None
        if isinstance(shap_values, list) and len(shap_values) > 1:
            shap_values_for_summary = shap_values[1] # 正类
        elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
            shap_values_for_summary = shap_values[:, :, 1] # 正类
        elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 2:
            shap_values_for_summary = shap_values # 已经是我们需要的格式

        if shap_values_for_summary is None:
            logger.warning(f"无法为模型 '{model_name}' 提取用于摘要图的SHAP值。跳过SHAP分析。")
        else:
            # 创建新的图形并绘制SHAP摘要图
            fig, ax = plt.subplots(figsize=(10, 6))
            shap.summary_plot(
                shap_values_for_summary,
                X_test,
                feature_names=X_test.columns.tolist() if hasattr(X_test, 'columns') else None,
                show=False,
                max_display=15
            )
            plt.title(f'{model_name} Model - SHAP Summary Plot', fontsize=14, pad=20)
            plt.tight_layout()
            summary_path = model_dir / f'{model_name}_shap_summary.png'
            save_plot(fig, summary_path.name, model_name, 'shap_summary')

            # 7. 决策图
            fig, ax = plt.subplots(figsize=(10, 6))
    # 确保X_test是DataFrame格式，如果是numpy数组则转换
    if isinstance(X_test, np.ndarray):
        import pandas as pd
        # 尝试从原始数据中获取列名
        try:
            # 从缓存中获取原始列名
            feature_names_cache_file = CONFIG['cache_path'] / f"{model_name}_feature_names.joblib"
            if feature_names_cache_file.exists():
                feature_names = load(feature_names_cache_file)
                logger.info(f"从缓存加载特征名称: {len(feature_names)} 个特征")
            else:
                # 如果缓存不存在，尝试从数据文件中获取列名
                cache_data_file = CONFIG['cache_path'] / 'last_used_data_file.txt'
                if cache_data_file.exists():
                    with open(cache_data_file, 'r', encoding='utf-8') as f:
                        data_file = f.read().strip()
                    # 加载原始数据以获取列名
                    import pandas as pd
                    df = pd.read_csv(data_file)
                    # 假设最后一列是目标列
                    feature_names = df.columns[:-1].tolist()
                    # 保存到缓存
                    dump(feature_names, feature_names_cache_file)
                    logger.info(f"从原始数据中提取特征名称: {len(feature_names)} 个特征")
                else:
                    # 如果无法获取原始列名，则使用默认的特征编号
                    feature_names = [f'feature_{i}' for i in range(X_test.shape[1])]
                    logger.warning("无法获取原始特征名称，使用默认特征编号")
        except Exception as e:
            logger.warning(f"获取特征名称失败: {e}，使用默认特征编号")
            feature_names = [f'feature_{i}' for i in range(X_test.shape[1])]
            
        # 确保特征名称数量与特征数量匹配
        if len(feature_names) != X_test.shape[1]:
            logger.warning(f"特征名称数量 ({len(feature_names)}) 与特征数量 ({X_test.shape[1]}) 不匹配，使用默认特征编号")
        feature_names = [f'feature_{i}' for i in range(X_test.shape[1])]
            
        X_test_df = pd.DataFrame(X_test, columns=feature_names)
        X_test_subset = X_test_df.iloc[:50]
    else:
        X_test_subset = X_test.iloc[:50]
    
    if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
        expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value,
                                                                 (list, tuple, np.ndarray)) and len(
            explainer.expected_value) > 1 else explainer.expected_value
        shap_val = shap_values[..., 1][:50] if shap_values.ndim == 3 else shap_values[1][:50]
        shap.decision_plot(expected_val, shap_val, X_test_subset, show=False)
    else:
        shap.decision_plot(explainer.expected_value, shap_values[:50], X_test_subset, show=False)
    plt.title(translate('决策图') + ' (Top 50 Samples)')
    plt.tight_layout()
    save_plot(fig, f"{model_name}_decision_plot.png", model_name, 'decision_plot')

    # 8. 瀑布图
    for idx in [0, 5, 10]:
        fig, ax = plt.subplots(figsize=(10, 6))
        # 处理X_test的数据格式
        if isinstance(X_test, np.ndarray):
            sample_data = X_test[idx]
            # 使用上面获取的feature_names
        else:
            sample_data = X_test.iloc[idx].values
            feature_names = list(X_test.columns)
            
        if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
            expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value,
                                                                     (list, tuple, np.ndarray)) and len(
                explainer.expected_value) > 1 else explainer.expected_value
            shap_val = shap_values[..., 1][idx] if shap_values.ndim == 3 else shap_values[1][idx]
            exp = shap.Explanation(values=shap_val,
                                   base_values=expected_val,
                                   data=sample_data,
                                   feature_names=feature_names)
        else:
            exp = shap.Explanation(values=shap_values[idx],
                                   base_values=explainer.expected_value,
                                   data=sample_data,
                                   feature_names=feature_names)
        shap.plots.waterfall(exp, max_display=15, show=False)
        plt.title(f'{translate("瀑布图")} ({translate("样本")} {idx})')
        plt.tight_layout()
        save_plot(fig, f"{model_name}_waterfall_{idx}.png", model_name, f'waterfall_{idx}')

    # 9. 性能雷达图
    # 计算混淆矩阵用于特异性计算
    tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    # 扩展评估指标：添加精确率和特异性
    categories = [translate('准确率'), translate('精确率'), translate('召回率'), 
                  translate('特异性'), translate('F1分数'), 'AUC']
    categories_values = [
        accuracy_score(y_test, y_pred), 
        precision_score(y_test, y_pred, zero_division=0),
        recall_score(y_test, y_pred), 
        specificity,
        f1_score(y_test, y_pred),
        roc_auc
    ]
    N = len(categories)

    angles = np.linspace(0, 2 * np.pi, N, endpoint=False).tolist()
    angles += angles[:1]

    # 使用更适合论文的配色方案
    plt.figure(figsize=(10, 10), facecolor='white')
    ax = plt.subplot(111, polar=True, facecolor='white')
    ax.set_theta_offset(np.pi / 2)
    ax.set_theta_direction(-1)
    ax.set_rlabel_position(0)

    # 设置网格样式
    ax.grid(True, color='lightgray', linestyle='-', linewidth=0.5, alpha=0.7)
    ax.set_ylim(0, 1)
    
    # 设置角度标签
    plt.xticks(angles[:-1], categories, fontsize=12, fontweight='bold')
    
    # 设置径向标签
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=10, alpha=0.7)

    values = categories_values + categories_values[:1]
    
    # 使用论文级别的配色：深蓝色线条，浅蓝色填充
    line_color = '#1f77b4'  # 深蓝色
    fill_color = '#aec7e8'  # 浅蓝色
    
    ax.plot(angles, values, linewidth=2.5, linestyle='solid', color=line_color, marker='o', markersize=8)
    ax.fill(angles, values, color=fill_color, alpha=0.25)

    plt.title(f'{display_name} {translate("模型性能雷达图")}', size=16, fontweight='bold', pad=20)
    plt.tight_layout()
    save_plot(plt.gcf(), f"{model_name}_radar.png", model_name, 'radar')

    # 打印性能指标
    logger.info(f"模型 {model_name} 性能指标:")
    logger.info(f"{translate('准确率')}: {accuracy_score(y_test, y_pred):.4f}")
    logger.info(f"{translate('精确率')}: {precision_score(y_test, y_pred, zero_division=0):.4f}")
    logger.info(f"{translate('召回率')}: {recall_score(y_test, y_pred):.4f}")
    logger.info(f"{translate('特异性')}: {specificity:.4f}")
    logger.info(f"{translate('F1分数')}: {f1_score(y_test, y_pred):.4f}")
    logger.info(f"AUC: {roc_auc:.4f}")

def plot_shap_summary(model, X, feature_names=None, model_name='model', max_display=20, plot_type='bar', output_dir=None):
    """
    生成SHAP摘要图
    
    Args:
        model: 训练好的模型
        X: 特征数据
        feature_names: 特征名称列表，如果为None且X是DataFrame则使用X的列名
        model_name: 模型名称，用于保存文件
        max_display: 显示的最大特征数量
        plot_type: 图表类型，'bar'或'dot'
        output_dir: 输出目录，如果为None则使用默认目录
    
    Returns:
        str: 保存的文件路径
    """
    logger.info(f"为模型 {model_name} 生成SHAP摘要图")
    
    # 确保X是DataFrame格式，如果是numpy数组则转换
    if isinstance(X, np.ndarray):
        import pandas as pd
        # 如果提供了特征名称，使用它们
        if feature_names is not None:
            # 确保特征名称数量与特征数量匹配
            if len(feature_names) != X.shape[1]:
                logger.warning(f"特征名称数量 ({len(feature_names)}) 与特征数量 ({X.shape[1]}) 不匹配，使用默认特征编号")
                feature_names = [f'feature_{i}' for i in range(X.shape[1])]
        else:
            # 如果没有提供特征名称，使用默认的特征编号
            feature_names = [f'feature_{i}' for i in range(X.shape[1])]
            
        X = pd.DataFrame(X, columns=feature_names)
    
    try:
        # 尝试使用TreeExplainer
        try:
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X)
            logger.info(f"TreeExplainer成功，shap_values类型: {type(shap_values)}, 形状: {np.array(shap_values).shape if isinstance(shap_values, list) else shap_values.shape}")
        except Exception as e:
            logger.warning(f"TreeExplainer 失败: {e}, 回退到 KernelExplainer")
            # 使用较小的背景数据集以提高效率
            background_size = min(100, len(X))
            background_data = X.sample(n=background_size, random_state=42) if hasattr(X, 'sample') else X[np.random.choice(X.shape[0], background_size, replace=False)]
            
            # 确保模型有predict_proba方法
            if hasattr(model, 'predict_proba'):
                predict_func = model.predict_proba
            else:
                predict_func = model.predict
                
            explainer = shap.KernelExplainer(predict_func, background_data)
            
            # 如果数据集太大，使用子集计算SHAP值
            sample_size = min(200, len(X))
            X_sample = X.sample(n=sample_size, random_state=42) if hasattr(X, 'sample') else X[np.random.choice(X.shape[0], sample_size, replace=False)]
            shap_values = explainer.shap_values(X_sample)
            X = X_sample  # 更新X为采样数据
            logger.info(f"KernelExplainer成功，shap_values类型: {type(shap_values)}, 形状: {np.array(shap_values).shape if isinstance(shap_values, list) else shap_values.shape}")
        
        # 创建新的图形并绘制SHAP摘要图
        plt.figure(figsize=(12, 8))
        
        # 处理不同类型的shap_values
        if isinstance(shap_values, list):
            # 二分类问题，通常我们关注正类
            logger.info(f"shap_values是列表，长度: {len(shap_values)}")
            if len(shap_values) > 1:
                # 二分类情况，使用正类的SHAP值
                shap_values_to_plot = shap_values[1]
                logger.info(f"使用正类(索引1)的SHAP值，形状: {shap_values_to_plot.shape}")
            else:
                shap_values_to_plot = shap_values[0]
                logger.info(f"使用索引0的SHAP值，形状: {shap_values_to_plot.shape}")
        elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
            # 三维数组，可能是多分类问题
            logger.info(f"shap_values是三维数组，形状: {shap_values.shape}")
            shap_values_to_plot = shap_values[:, :, 1] if shap_values.shape[2] > 1 else shap_values[:, :, 0]
        else:
            # 其他情况，直接使用
            shap_values_to_plot = shap_values
            logger.info(f"直接使用shap_values，形状: {shap_values_to_plot.shape}")
        
        # 确保特征名称与数据维度匹配
        if hasattr(X, 'columns'):
            feature_names_to_use = X.columns.tolist()
        else:
            feature_names_to_use = feature_names
            
        logger.info(f"使用特征名称: {feature_names_to_use[:5]}... (共{len(feature_names_to_use)}个)")
        
        # 绘制SHAP摘要图
        if plot_type == 'bar':
            shap.summary_plot(
                shap_values_to_plot, 
                X, 
                plot_type="bar", 
                max_display=max_display,
                feature_names=feature_names_to_use,
                show=False
            )
            plt.title(f"{model_name} - {translate('SHAP特征重要性')}")
        else:
            shap.summary_plot(
                shap_values_to_plot, 
                X, 
                max_display=max_display,
                feature_names=feature_names_to_use,
                show=False
            )
            plt.title(f"{model_name} - {translate('SHAP特征影响分布')}")
            
        # 添加图例说明
        if plot_type != 'bar':
            # 在图例下方添加说明文本
            plt.figtext(0.5, 0.01, "颜色表示特征值（红色=高，蓝色=低）\nSHAP值表示对预测的影响（正值=增加预测概率，负值=降低预测概率）", 
                       ha='center', fontsize=10, bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        plt.tight_layout()
        
        # 保存图形
        if output_dir is None:
            output_dir = CONFIG['output_path'] / model_name
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        filepath = output_dir / f"{model_name}_shap_summary_{plot_type}.png"
        plt.savefig(filepath, dpi=CONFIG['dpi'], bbox_inches='tight')
        plt.close()
        
        logger.info(f"SHAP摘要图已保存至: {filepath}")
        return str(filepath)
    
    except Exception as e:
        logger.error(f"生成SHAP摘要图失败: {e}")
        import traceback
        traceback.print_exc()
        plt.close()
        return None

def plot_shap_dependence(model, X, feature, model_name='model', interaction_feature=None, output_dir=None):
    """
    生成SHAP依赖图
    
    Args:
        model: 训练好的模型
        X: 特征数据
        feature: 要分析的特征名称或索引
        model_name: 模型名称，用于保存文件
        interaction_feature: 交互特征名称或索引，如果为None则不显示交互
        output_dir: 输出目录，如果为None则使用默认目录
    
    Returns:
        str: 保存的文件路径
    """
    logger.info(f"为模型 {model_name} 生成特征 '{feature}' 的SHAP依赖图")
    
    # 确保X是DataFrame格式，如果是numpy数组则转换
    if isinstance(X, np.ndarray):
        import pandas as pd
        # 如果feature是字符串，则需要转换为索引
        if isinstance(feature, str):
            logger.warning(f"X是numpy数组，但feature是字符串 '{feature}'，将尝试使用索引")
            try:
                feature_idx = int(feature.replace('feature_', ''))
                feature = feature_idx
            except:
                logger.error(f"无法将特征名称 '{feature}' 转换为索引")
                return None
        
        # 创建默认特征名称
        feature_names = [f'feature_{i}' for i in range(X.shape[1])]
        X = pd.DataFrame(X, columns=feature_names)
        
        # 更新feature为字符串名称
        if isinstance(feature, int):
            if 0 <= feature < len(feature_names):
                feature = feature_names[feature]
            else:
                logger.error(f"特征索引 {feature} 超出范围 [0, {len(feature_names)-1}]")
                return None
    
    try:
        # 尝试使用TreeExplainer
        try:
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X)
        except Exception as e:
            logger.warning(f"TreeExplainer 失败: {e}, 回退到 KernelExplainer")
            explainer = shap.KernelExplainer(model.predict_proba, X)
            shap_values = explainer.shap_values(X)
        
        # 创建新的图形并绘制SHAP依赖图
        plt.figure(figsize=(10, 6))
        
        # 处理不同类型的shap_values
        if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
            # 二分类问题，通常我们关注正类
            shap_values_to_plot = shap_values[..., 1] if shap_values.ndim == 3 else shap_values[1]
        else:
            shap_values_to_plot = shap_values
        
        # 绘制依赖图
        if interaction_feature is not None:
            shap.dependence_plot(
                feature, 
                shap_values_to_plot, 
                X, 
                interaction_index=interaction_feature,
                show=False
            )
        else:
            shap.dependence_plot(
                feature, 
                shap_values_to_plot, 
                X,
                show=False
            )
        
        # 设置标题
        if interaction_feature:
            plt.title(f"{translate('SHAP依赖图')}: {feature} (交互: {interaction_feature})")
        else:
            plt.title(f"{translate('SHAP依赖图')}: {feature}")
        
        plt.tight_layout()
        
        # 保存图形
        if output_dir is None:
            output_dir = CONFIG['output_path'] / model_name
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 处理特征名称中可能存在的非法文件名字符
        safe_feature_name = str(feature).replace('/', '_').replace('\\', '_').replace(':', '_')
        
        if interaction_feature:
            safe_interaction_name = str(interaction_feature).replace('/', '_').replace('\\', '_').replace(':', '_')
            filepath = output_dir / f"{model_name}_shap_dependence_{safe_feature_name}_with_{safe_interaction_name}.png"
        else:
            filepath = output_dir / f"{model_name}_shap_dependence_{safe_feature_name}.png"
        
        plt.savefig(filepath, dpi=CONFIG['dpi'], bbox_inches='tight')
        plt.close()
        
        logger.info(f"SHAP依赖图已保存至: {filepath}")
        return str(filepath)
    
    except Exception as e:
        logger.error(f"生成SHAP依赖图失败: {e}")
        plt.close()
        return None
