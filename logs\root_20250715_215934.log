2025-07-15 21:59:34 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:59:34 - model_ensemble - INFO - ============================================================
2025-07-15 21:59:34 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:59:34 - model_ensemble - INFO - ============================================================
2025-07-15 21:59:34 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:59:34 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 21:59:34 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:59:34 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:59:34 - model_training - INFO - 模型名称: Random Forest
2025-07-15 21:59:34 - model_training - INFO - 准确率: 0.9444
2025-07-15 21:59:34 - model_training - INFO - AUC: 0.9926
2025-07-15 21:59:34 - model_training - INFO - 混淆矩阵:
2025-07-15 21:59:34 - model_training - INFO - 
[[41  3]
 [ 2 44]]
2025-07-15 21:59:34 - model_training - INFO - 
分类报告:
2025-07-15 21:59:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.93      0.94        44
           1       0.94      0.96      0.95        46

    accuracy                           0.94        90
   macro avg       0.94      0.94      0.94        90
weighted avg       0.94      0.94      0.94        90

2025-07-15 21:59:34 - model_training - INFO - 训练时间: 0.13 秒
2025-07-15 21:59:34 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 21:59:34 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 21:59:34 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:59:34 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:59:34 - model_training - INFO - 模型名称: XGBoost
2025-07-15 21:59:34 - model_training - INFO - 准确率: 0.9222
2025-07-15 21:59:34 - model_training - INFO - AUC: 0.9862
2025-07-15 21:59:34 - model_training - INFO - 混淆矩阵:
2025-07-15 21:59:34 - model_training - INFO - 
[[39  5]
 [ 2 44]]
2025-07-15 21:59:34 - model_training - INFO - 
分类报告:
2025-07-15 21:59:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.89      0.92        44
           1       0.90      0.96      0.93        46

    accuracy                           0.92        90
   macro avg       0.92      0.92      0.92        90
weighted avg       0.92      0.92      0.92        90

2025-07-15 21:59:34 - model_training - INFO - 训练时间: 0.12 秒
2025-07-15 21:59:34 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 21:59:34 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 21:59:34 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:59:34 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:59:34 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:59:34 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:59:34 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:59:34 - model_ensemble - INFO -     voting_soft - 准确率: 0.9222, F1: 0.9221
2025-07-15 21:59:34 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:59:34 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:59:34 - model_ensemble - INFO -     voting_hard - 准确率: 0.9556, F1: 0.9556
2025-07-15 21:59:34 - model_ensemble - INFO - ============================================================
2025-07-15 21:59:34 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:59:34 - model_ensemble - INFO - ============================================================
2025-07-15 21:59:34 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 21:59:34 - model_ensemble - INFO - 最佳F1分数: 0.9556
2025-07-15 21:59:34 - model_ensemble - INFO - 最佳准确率: 0.9556
2025-07-15 21:59:34 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:59:34 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9222, 精确率: 0.9240, 召回率: 0.9222, F1: 0.9221, AUC: 0.9911
2025-07-15 21:59:34 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9556, 精确率: 0.9556, 召回率: 0.9556, F1: 0.9556, AUC: 0.0000
2025-07-15 21:59:34 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:59:35 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_215934.joblib
2025-07-15 21:59:35 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-07-15 21:59:35 - safe_visualization - INFO - Summary report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_summary_report.txt
2025-07-15 21:59:35 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 21:59:35 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 21:59:35 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 21:59:35 - enhanced_shap_visualization - WARNING - Failed to create SHAP explainer: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._voting.VotingClassifier'>
2025-07-15 21:59:35 - model_ensemble - WARNING -   voting_soft SHAP分析未生成任何图表
2025-07-15 21:59:35 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 21:59:35 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 21:59:35 - enhanced_shap_visualization - WARNING - Failed to create SHAP explainer: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._voting.VotingClassifier'>
2025-07-15 21:59:35 - model_ensemble - WARNING -   voting_hard SHAP分析未生成任何图表
2025-07-15 21:59:35 - model_ensemble - INFO -   voting_hard SHAP分析完成
