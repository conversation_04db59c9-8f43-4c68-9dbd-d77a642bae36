2025-07-16 22:45:04 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 22:45:04 - GUI - INFO - GUI界面初始化完成
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:46:04 - model_training - INFO - 模型名称: Decision Tree
2025-07-16 22:46:04 - model_training - INFO - 准确率: 0.8250
2025-07-16 22:46:04 - model_training - INFO - AUC: 0.9028
2025-07-16 22:46:04 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:04 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-07-16 22:46:04 - model_training - INFO - 
分类报告:
2025-07-16 22:46:04 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-07-16 22:46:04 - model_training - INFO - 训练时间: 0.02 秒
2025-07-16 22:46:04 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-07-16 22:46:04 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:46:04 - model_training - INFO - 模型名称: Random Forest
2025-07-16 22:46:04 - model_training - INFO - 准确率: 0.8250
2025-07-16 22:46:04 - model_training - INFO - AUC: 0.9015
2025-07-16 22:46:04 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:04 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-16 22:46:04 - model_training - INFO - 
分类报告:
2025-07-16 22:46:04 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-16 22:46:04 - model_training - INFO - 训练时间: 0.09 秒
2025-07-16 22:46:04 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-16 22:46:04 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 22:46:04 - model_training - INFO - 模型名称: XGBoost
2025-07-16 22:46:04 - model_training - INFO - 准确率: 0.7750
2025-07-16 22:46:04 - model_training - INFO - AUC: 0.8568
2025-07-16 22:46:04 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:04 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-16 22:46:04 - model_training - INFO - 
分类报告:
2025-07-16 22:46:04 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-16 22:46:04 - model_training - INFO - 训练时间: 0.05 秒
2025-07-16 22:46:04 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-16 22:46:04 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 22:46:06 - model_training - INFO - 模型名称: LightGBM
2025-07-16 22:46:06 - model_training - INFO - 准确率: 0.7500
2025-07-16 22:46:06 - model_training - INFO - AUC: 0.8568
2025-07-16 22:46:06 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:06 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-07-16 22:46:06 - model_training - INFO - 
分类报告:
2025-07-16 22:46:06 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-07-16 22:46:06 - model_training - INFO - 训练时间: 1.71 秒
2025-07-16 22:46:06 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-16 22:46:06 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-16 22:46:06 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 22:46:06 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 22:46:07 - model_training - INFO - 模型名称: CatBoost
2025-07-16 22:46:07 - model_training - INFO - 准确率: 0.8750
2025-07-16 22:46:07 - model_training - INFO - AUC: 0.9054
2025-07-16 22:46:07 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:07 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-07-16 22:46:07 - model_training - INFO - 
分类报告:
2025-07-16 22:46:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 22:46:07 - model_training - INFO - 训练时间: 1.19 秒
2025-07-16 22:46:07 - model_training - INFO - 模型 CatBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-07-16 22:46:07 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_feature_names.joblib
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:46:07 - model_training - INFO - 模型名称: Logistic Regression
2025-07-16 22:46:07 - model_training - INFO - 准确率: 0.8250
2025-07-16 22:46:07 - model_training - INFO - AUC: 0.9028
2025-07-16 22:46:07 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:07 - model_training - INFO - 
[[19  4]
 [ 3 14]]
2025-07-16 22:46:07 - model_training - INFO - 
分类报告:
2025-07-16 22:46:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.83      0.84        23
           1       0.78      0.82      0.80        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.83      0.82      0.83        40

2025-07-16 22:46:07 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:46:07 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-16 22:46:07 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:46:07 - model_training - INFO - 模型名称: SVM
2025-07-16 22:46:07 - model_training - INFO - 准确率: 0.8750
2025-07-16 22:46:07 - model_training - INFO - AUC: 0.9309
2025-07-16 22:46:07 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:07 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-07-16 22:46:07 - model_training - INFO - 
分类报告:
2025-07-16 22:46:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 22:46:07 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:46:07 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-07-16 22:46:07 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 22:46:07 - model_training - INFO - 模型名称: KNN
2025-07-16 22:46:07 - model_training - INFO - 准确率: 0.9000
2025-07-16 22:46:07 - model_training - INFO - AUC: 0.8913
2025-07-16 22:46:07 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:07 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-07-16 22:46:07 - model_training - INFO - 
分类报告:
2025-07-16 22:46:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-07-16 22:46:07 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:46:07 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-07-16 22:46:07 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 22:46:07 - model_training - INFO - 模型名称: Naive Bayes
2025-07-16 22:46:07 - model_training - INFO - 准确率: 0.8750
2025-07-16 22:46:07 - model_training - INFO - AUC: 0.9079
2025-07-16 22:46:07 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:07 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-07-16 22:46:07 - model_training - INFO - 
分类报告:
2025-07-16 22:46:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-07-16 22:46:07 - model_training - INFO - 训练时间: 0.01 秒
2025-07-16 22:46:07 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-07-16 22:46:07 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 22:46:07 - model_training - INFO - 模型名称: Neural Network
2025-07-16 22:46:07 - model_training - INFO - 准确率: 0.8500
2025-07-16 22:46:07 - model_training - INFO - AUC: 0.9156
2025-07-16 22:46:07 - model_training - INFO - 混淆矩阵:
2025-07-16 22:46:07 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-07-16 22:46:07 - model_training - INFO - 
分类报告:
2025-07-16 22:46:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-07-16 22:46:07 - model_training - INFO - 训练时间: 0.32 秒
2025-07-16 22:46:07 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-07-16 22:46:07 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO - 模型多样性矩阵计算完成
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.510, 多样性=0.490
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.402, 多样性=0.598
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.361, 多样性=0.639
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.524, 多样性=0.476
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.402, 多样性=0.598
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.524, 多样性=0.476
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.556, 多样性=0.444
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.510, 多样性=0.490
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.455, 多样性=0.545
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.698, 多样性=0.302
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.738, 多样性=0.262
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.899, 多样性=0.101
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.800, 多样性=0.200
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.792, 多样性=0.208
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.846, 多样性=0.154
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.792, 多样性=0.208
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.743, 多样性=0.257
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.856, 多样性=0.144
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.706, 多样性=0.294
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.697, 多样性=0.303
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.601, 多样性=0.399
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.646, 多样性=0.354
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.595, 多样性=0.405
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.646, 多样性=0.354
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.731, 多样性=0.269
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.753, 多样性=0.247
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.623, 多样性=0.377
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.692, 多样性=0.308
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.632, 多样性=0.368
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.588, 多样性=0.412
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.811, 多样性=0.189
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.890, 多样性=0.110
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.854, 多样性=0.146
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.792, 多样性=0.208
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.854, 多样性=0.146
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.811, 多样性=0.189
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.849, 多样性=0.151
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.800, 多样性=0.200
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.849, 多样性=0.151
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.854, 多样性=0.146
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.792, 多样性=0.208
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.854, 多样性=0.146
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.950, 多样性=0.050
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.795, 多样性=0.205
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.743, 多样性=0.257
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'NaiveBayes', 'NeuralNet']
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO - 综合得分: 0.7617
