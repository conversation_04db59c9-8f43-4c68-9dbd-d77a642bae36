#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的模型多样性分析器
解决模型相关性过高的问题
"""

import numpy as np
import pandas as pd
from sklearn.metrics import accuracy_score, f1_score
from sklearn.model_selection import cross_val_score
from sklearn.preprocessing import StandardScaler
from scipy.stats import entropy
from scipy.spatial.distance import pdist, squareform
import warnings
warnings.filterwarnings('ignore')

from logger import get_logger

logger = get_logger(__name__)

class ImprovedDiversityAnalyzer:
    """
    改进的模型多样性分析器
    使用多层次多样性度量方法
    """
    
    def __init__(self, correlation_threshold=0.3, adaptive_threshold=True):
        """
        初始化分析器
        
        Args:
            correlation_threshold: 基础相关性阈值
            adaptive_threshold: 是否根据数据复杂度自适应调整阈值
        """
        self.correlation_threshold = correlation_threshold
        self.adaptive_threshold = adaptive_threshold
        self.data_complexity_score = None
        self.adjusted_threshold = correlation_threshold
        
    def analyze_data_complexity(self, X, y):
        """
        分析数据复杂度，用于自适应调整相关性阈值
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            float: 数据复杂度得分 (0-1，越高越复杂)
        """
        complexity_factors = []
        
        # 1. 特征维度复杂度
        n_samples, n_features = X.shape
        dim_complexity = min(n_features / n_samples, 1.0)
        complexity_factors.append(dim_complexity)
        
        # 2. 类别平衡度
        class_counts = np.bincount(y)
        class_balance = min(class_counts) / max(class_counts)
        balance_complexity = 1 - class_balance  # 不平衡度越高，复杂度越高
        complexity_factors.append(balance_complexity)
        
        # 3. 特征相关性复杂度
        if X.shape[1] > 1:
            corr_matrix = np.corrcoef(X.T)
            avg_corr = np.mean(np.abs(corr_matrix[np.triu_indices_from(corr_matrix, k=1)]))
            corr_complexity = avg_corr  # 特征间相关性越高，复杂度越高
            complexity_factors.append(corr_complexity)
        
        # 4. 数据分离度
        try:
            from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
            lda = LinearDiscriminantAnalysis()
            lda.fit(X, y)
            lda_score = lda.score(X, y)
            separation_complexity = 1 - lda_score  # 分离度越低，复杂度越高
            complexity_factors.append(separation_complexity)
        except:
            pass
        
        # 综合复杂度得分
        self.data_complexity_score = np.mean(complexity_factors)
        
        # 自适应调整阈值
        if self.adaptive_threshold:
            if self.data_complexity_score < 0.3:  # 简单数据
                self.adjusted_threshold = min(0.6, self.correlation_threshold * 2)
            elif self.data_complexity_score > 0.7:  # 复杂数据
                self.adjusted_threshold = max(0.2, self.correlation_threshold * 0.7)
            else:  # 中等复杂度
                self.adjusted_threshold = self.correlation_threshold
        
        logger.info(f"数据复杂度得分: {self.data_complexity_score:.3f}")
        logger.info(f"调整后相关性阈值: {self.adjusted_threshold:.3f}")
        
        return self.data_complexity_score
    
    def calculate_prediction_diversity(self, models, X_test, y_test, use_proba=True):
        """
        计算基于预测结果的多样性
        
        Args:
            models: 模型字典
            X_test: 测试特征
            y_test: 测试标签
            use_proba: 是否使用预测概率（推荐）
            
        Returns:
            dict: 多样性分析结果
        """
        model_names = list(models.keys())
        n_models = len(model_names)
        
        if n_models < 2:
            return {'diversity_matrix': np.array([[1.0]]), 'avg_diversity': 1.0}
        
        # 获取预测结果
        predictions = {}
        probabilities = {}
        
        for name, model in models.items():
            pred = model.predict(X_test)
            predictions[name] = pred
            
            # 尝试获取预测概率
            if use_proba and hasattr(model, 'predict_proba'):
                try:
                    prob = model.predict_proba(X_test)
                    if prob.shape[1] == 2:  # 二分类
                        probabilities[name] = prob[:, 1]
                    else:
                        probabilities[name] = np.max(prob, axis=1)
                except:
                    probabilities[name] = pred.astype(float)
            elif use_proba and hasattr(model, 'decision_function'):
                try:
                    probabilities[name] = model.decision_function(X_test)
                except:
                    probabilities[name] = pred.astype(float)
            else:
                probabilities[name] = pred.astype(float)
        
        # 计算多样性矩阵
        if use_proba and probabilities:
            # 使用预测概率计算相关性
            prob_matrix = np.array([probabilities[name] for name in model_names])
            correlation_matrix = np.corrcoef(prob_matrix)
        else:
            # 使用硬预测计算相关性
            pred_matrix = np.array([predictions[name] for name in model_names])
            correlation_matrix = np.corrcoef(pred_matrix)
        
        # 处理NaN值
        correlation_matrix = np.nan_to_num(correlation_matrix, nan=1.0)
        
        # 多样性 = 1 - |相关性|
        diversity_matrix = 1 - np.abs(correlation_matrix)
        
        # 计算平均多样性（排除对角线）
        mask = ~np.eye(n_models, dtype=bool)
        avg_diversity = np.mean(diversity_matrix[mask])
        
        return {
            'diversity_matrix': diversity_matrix,
            'correlation_matrix': correlation_matrix,
            'avg_diversity': avg_diversity,
            'model_names': model_names
        }
    
    def calculate_feature_importance_diversity(self, models):
        """
        计算基于特征重要性的多样性
        
        Args:
            models: 模型字典
            
        Returns:
            dict: 特征重要性多样性分析结果
        """
        model_names = list(models.keys())
        feature_importances = {}
        
        # 提取特征重要性
        for name, model in models.items():
            importance = None
            
            if hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
            elif hasattr(model, 'coef_'):
                importance = np.abs(model.coef_).flatten()
            elif hasattr(model, 'feature_importance_'):  # XGBoost/LightGBM
                importance = model.feature_importance_
            
            if importance is not None:
                # 归一化重要性
                importance = importance / (np.sum(importance) + 1e-8)
                feature_importances[name] = importance
        
        if len(feature_importances) < 2:
            return {'avg_diversity': 1.0, 'available_models': list(feature_importances.keys())}
        
        # 计算重要性相关性
        importance_matrix = np.array([feature_importances[name] for name in feature_importances.keys()])
        correlation_matrix = np.corrcoef(importance_matrix)
        correlation_matrix = np.nan_to_num(correlation_matrix, nan=1.0)
        
        # 多样性计算
        diversity_matrix = 1 - np.abs(correlation_matrix)
        n_models = len(feature_importances)
        mask = ~np.eye(n_models, dtype=bool)
        avg_diversity = np.mean(diversity_matrix[mask])
        
        return {
            'diversity_matrix': diversity_matrix,
            'correlation_matrix': correlation_matrix,
            'avg_diversity': avg_diversity,
            'available_models': list(feature_importances.keys()),
            'feature_importances': feature_importances
        }
    
    def calculate_algorithm_type_diversity(self, model_names):
        """
        计算基于算法类型的多样性
        
        Args:
            model_names: 模型名称列表
            
        Returns:
            dict: 算法类型多样性分析结果
        """
        # 算法类型分类
        algorithm_types = {
            'linear': ['Logistic', 'SVM'],
            'tree': ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost'],
            'probabilistic': ['NaiveBayes'],
            'instance': ['KNN'],
            'neural': ['NeuralNet', 'MLPClassifier']
        }
        
        # 统计每种类型的数量
        type_counts = {}
        model_type_mapping = {}
        
        for model_name in model_names:
            assigned_type = 'other'
            for algo_type, models in algorithm_types.items():
                if model_name in models:
                    assigned_type = algo_type
                    break
            
            model_type_mapping[model_name] = assigned_type
            type_counts[assigned_type] = type_counts.get(assigned_type, 0) + 1
        
        # 计算类型多样性
        n_types = len(type_counts)
        n_models = len(model_names)
        max_possible_types = min(n_models, len(algorithm_types))
        
        # 基础多样性得分
        base_diversity = n_types / max_possible_types
        
        # 平衡性惩罚（避免某一类型过多）
        balance_penalty = 0
        for count in type_counts.values():
            if count > n_models // 2:
                balance_penalty += (count - n_models // 2) * 0.1
        
        type_diversity = max(0, base_diversity - balance_penalty)
        
        return {
            'type_diversity': type_diversity,
            'type_counts': type_counts,
            'model_type_mapping': model_type_mapping,
            'n_types': n_types,
            'balance_penalty': balance_penalty
        }
    
    def comprehensive_diversity_analysis(self, models, X_test, y_test):
        """
        综合多样性分析
        
        Args:
            models: 模型字典
            X_test: 测试特征
            y_test: 测试标签
            
        Returns:
            dict: 综合多样性分析结果
        """
        logger.info("开始综合多样性分析...")
        
        # 分析数据复杂度
        self.analyze_data_complexity(X_test, y_test)
        
        # 1. 预测多样性分析
        pred_diversity = self.calculate_prediction_diversity(models, X_test, y_test, use_proba=True)
        
        # 2. 特征重要性多样性分析
        importance_diversity = self.calculate_feature_importance_diversity(models)
        
        # 3. 算法类型多样性分析
        type_diversity = self.calculate_algorithm_type_diversity(list(models.keys()))
        
        # 4. 综合多样性得分
        weights = {
            'prediction': 0.5,
            'importance': 0.3,
            'algorithm_type': 0.2
        }
        
        comprehensive_score = (
            weights['prediction'] * pred_diversity['avg_diversity'] +
            weights['importance'] * importance_diversity['avg_diversity'] +
            weights['algorithm_type'] * type_diversity['type_diversity']
        )
        
        result = {
            'comprehensive_score': comprehensive_score,
            'data_complexity': self.data_complexity_score,
            'adjusted_threshold': self.adjusted_threshold,
            'prediction_diversity': pred_diversity,
            'importance_diversity': importance_diversity,
            'algorithm_type_diversity': type_diversity,
            'weights': weights
        }
        
        logger.info(f"综合多样性得分: {comprehensive_score:.3f}")
        
        return result
