2025-07-15 22:57:51 - plot_single_model - INFO - 为模型 OriginalReference 生成SHAP摘要图
2025-07-15 22:57:51 - plot_single_model - INFO - TreeExplainer成功，shap_values类型: <class 'numpy.ndarray'>, 形状: (50, 8, 2)
2025-07-15 22:57:51 - plot_single_model - INFO - shap_values是三维数组，形状: (50, 8, 2)
2025-07-15 22:57:51 - plot_single_model - INFO - 使用特征名称: ['Ctmin', 'Dmean', 'S', 'Ctmax', 'V']... (共8个)
2025-07-15 22:57:51 - plot_single_model - INFO - SHAP摘要图已保存至: comparison_output\OriginalReference_shap_summary_dot.png
2025-07-15 22:57:51 - plot_single_model - INFO - 为模型 OriginalReference 生成特征 'Ctmin' 的SHAP依赖图
2025-07-15 22:57:51 - plot_single_model - INFO - SHAP依赖图已保存至: comparison_output\OriginalReference_shap_dependence_Ctmin.png
