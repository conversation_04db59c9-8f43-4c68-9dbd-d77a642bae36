2025-07-15 19:39:34 - GUI - INFO - GUI界面初始化完成
2025-07-15 19:40:10 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 9)
2025-07-15 19:40:10 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 19:40:10 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 193, 'max_depth': 21, 'min_samples_split': 14, 'min_samples_leaf': 2, 'max_features': 'sqrt'}
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9707
2025-07-15 19:40:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250715_194024.html
2025-07-15 19:40:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250715_194024.html
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.74 秒
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 20
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 150, 'max_depth': 10, 'learning_rate': 0.09664322129632669, 'subsample': 0.601194064031334, 'colsample_bytree': 0.9950507433179563}
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9675
2025-07-15 19:40:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250715_194028.html
2025-07-15 19:40:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250715_194028.html
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.28 秒
2025-07-15 19:40:28 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 20
2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 52, 'max_depth': 3, 'learning_rate': 0.10663368377403017, 'feature_fraction': 0.7760322736126682, 'bagging_fraction': 0.689390897018191}
2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9708
2025-07-15 19:40:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250715_194031.html
2025-07-15 19:40:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250715_194031.html
2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.83 秒
2025-07-15 19:40:31 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 20
2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 8.624492190155658, 'solver': 'liblinear'}
2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9483
2025-07-15 19:40:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250715_194032.html
2025-07-15 19:40:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250715_194032.html
2025-07-15 19:40:32 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.53 秒
2025-07-15 19:40:42 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 9)
2025-07-15 19:40:42 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-15 19:40:42 - model_training - INFO - 模型名称: Random Forest
2025-07-15 19:40:42 - model_training - INFO - 准确率: 0.8250
2025-07-15 19:40:42 - model_training - INFO - AUC: 0.9015
2025-07-15 19:40:42 - model_training - INFO - 混淆矩阵:
2025-07-15 19:40:42 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-07-15 19:40:42 - model_training - INFO - 
分类报告:
2025-07-15 19:40:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-07-15 19:40:42 - model_training - INFO - 训练时间: 0.09 秒
2025-07-15 19:40:42 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-15 19:40:42 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-07-15 19:40:42 - model_training - INFO - 模型名称: XGBoost
2025-07-15 19:40:42 - model_training - INFO - 准确率: 0.7750
2025-07-15 19:40:42 - model_training - INFO - AUC: 0.8568
2025-07-15 19:40:42 - model_training - INFO - 混淆矩阵:
2025-07-15 19:40:42 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-07-15 19:40:42 - model_training - INFO - 
分类报告:
2025-07-15 19:40:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-07-15 19:40:42 - model_training - INFO - 训练时间: 0.05 秒
2025-07-15 19:40:42 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-07-15 19:40:42 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-07-15 19:40:43 - model_training - INFO - 模型名称: LightGBM
2025-07-15 19:40:43 - model_training - INFO - 准确率: 0.7500
2025-07-15 19:40:43 - model_training - INFO - AUC: 0.8568
2025-07-15 19:40:43 - model_training - INFO - 混淆矩阵:
2025-07-15 19:40:43 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-07-15 19:40:43 - model_training - INFO - 
分类报告:
2025-07-15 19:40:43 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-07-15 19:40:43 - model_training - INFO - 训练时间: 0.22 秒
2025-07-15 19:40:43 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-07-15 19:40:43 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-07-15 19:40:43 - model_training - INFO - 模型名称: Logistic Regression
2025-07-15 19:40:43 - model_training - INFO - 准确率: 0.8250
2025-07-15 19:40:43 - model_training - INFO - AUC: 0.9028
2025-07-15 19:40:43 - model_training - INFO - 混淆矩阵:
2025-07-15 19:40:43 - model_training - INFO - 
[[19  4]
 [ 3 14]]
2025-07-15 19:40:43 - model_training - INFO - 
分类报告:
2025-07-15 19:40:43 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.83      0.84        23
           1       0.78      0.82      0.80        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.83      0.82      0.83        40

2025-07-15 19:40:43 - model_training - INFO - 训练时间: 0.01 秒
2025-07-15 19:40:43 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-07-15 19:40:43 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
