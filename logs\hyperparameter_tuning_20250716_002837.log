2025-07-16 00:28:37 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 160, 'max_depth': 10, 'min_samples_split': 13, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9835
2025-07-16 00:29:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250716_002911.html
2025-07-16 00:29:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250716_002911.html
2025-07-16 00:29:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 33.61 秒
