2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00683363, -0.00585078,  0.24013071,  0.0715186 , -0.08129342,
        0.11653834, -0.03289942, -0.00603739,  0.01149431, -0.01333022])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00683363,  0.00585078, -0.24013071, -0.0715186 ,  0.08129342,
       -0.11653834,  0.03289942,  0.00603739, -0.01149431,  0.01333022])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00732716, -0.0275054 , -0.45073087, -0.0085699 ,  0.04189826,
       -0.04945373, -0.01986342,  0.05872417,  0.04375618, -0.03444905])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00732716,  0.0275054 ,  0.45073087,  0.0085699 , -0.04189826,
        0.04945373,  0.01986342, -0.05872417, -0.04375618,  0.03444905])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.0092473 , -0.01564054,  0.40362674, -0.1925817 ,  0.02452475,
        0.01809111, -0.00141908, -0.1743597 , -0.12463634,  0.02618731])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.0092473 ,  0.01564054, -0.40362674,  0.1925817 , -0.02452475,
       -0.01809111,  0.00141908,  0.1743597 ,  0.12463634, -0.02618731])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 2.93323601e-03,  1.49755763e-02, -5.20991461e-01,  1.21066555e-01,
        1.78595373e-02, -4.11814971e-02, -3.53961902e-04,  2.39164628e-02,
        2.46738530e-02, -1.59338872e-02])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-2.93323601e-03, -1.49755763e-02,  5.20991461e-01, -1.21066555e-01,
       -1.78595373e-02,  4.11814971e-02,  3.53961902e-04, -2.39164628e-02,
       -2.46738530e-02,  1.59338872e-02])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.01370654,  0.04088033,  0.4091763 ,  0.04839641, -0.14848653,
        0.11795515, -0.01459731, -0.00347816, -0.10098442, -0.00074248])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.01370654, -0.04088033, -0.4091763 , -0.04839641,  0.14848653,
       -0.11795515,  0.01459731,  0.00347816,  0.10098442,  0.00074248])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00060312,  0.0696044 , -0.09472714, -0.08454695,  0.108588  ,
        0.13034225, -0.02455954,  0.09960315, -0.09052193,  0.07980501])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00060312, -0.0696044 ,  0.09472714,  0.08454695, -0.108588  ,
       -0.13034225,  0.02455954, -0.09960315,  0.09052193, -0.07980501])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.0004784 ,  0.05072299,  0.03691822,  0.03137078,  0.06369537,
        0.09794824, -0.01836594,  0.20942201, -0.02616825,  0.03081141])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.0004784 , -0.05072299, -0.03691822, -0.03137078, -0.06369537,
       -0.09794824,  0.01836594, -0.20942201,  0.02616825, -0.03081141])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00787499,  0.01566469,  0.28134081,  0.08592265, -0.06531583,
        0.17123439, -0.0295122 ,  0.08069647, -0.04427038, -0.0216571 ])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00787499, -0.01566469, -0.28134081, -0.08592265,  0.06531583,
       -0.17123439,  0.0295122 , -0.08069647,  0.04427038,  0.0216571 ])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00609825, -0.049679  , -0.29075285, -0.06864934,  0.07025057,
       -0.02312903,  0.00190251, -0.19157705,  0.08815465,  0.02143287])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00609825,  0.049679  ,  0.29075285,  0.06864934, -0.07025057,
        0.02312903, -0.00190251,  0.19157705, -0.08815465, -0.02143287])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.01843096, -0.03398709, -0.26574175, -0.1427767 ,  0.1724985 ,
        0.08695964, -0.00336189,  0.26723659,  0.07390919, -0.01828988])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.01843096,  0.03398709,  0.26574175,  0.1427767 , -0.1724985 ,
       -0.08695964,  0.00336189, -0.26723659, -0.07390919,  0.01828988])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00960422,  0.0962609 ,  0.07664245,  0.13872646, -0.02075315,
       -0.14948673,  0.06357082, -0.21231189, -0.05587959,  0.10743537])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00960422, -0.0962609 , -0.07664245, -0.13872646,  0.02075315,
        0.14948673, -0.06357082,  0.21231189,  0.05587959, -0.10743537])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00621265, -0.04613382, -0.26090687, -0.07939766,  0.03397895,
       -0.05580519, -0.0029587 , -0.11986351,  0.06813733,  0.00156744])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00621265,  0.04613382,  0.26090687,  0.07939766, -0.03397895,
        0.05580519,  0.0029587 ,  0.11986351, -0.06813733, -0.00156744])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00042508,  0.03462791, -0.14818736, -0.04420703, -0.16834   ,
       -0.14565846, -0.02691647,  0.00201477,  0.01961848,  0.03769759])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00042508, -0.03462791,  0.14818736,  0.04420703,  0.16834   ,
        0.14565846,  0.02691647, -0.00201477, -0.01961848, -0.03769759])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00783271,  0.07731634, -0.07989802,  0.13299   , -0.00246435,
       -0.30123817, -0.038593  , -0.00880099, -0.05775919,  0.04590164])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00783271, -0.07731634,  0.07989802, -0.13299   ,  0.00246435,
        0.30123817,  0.038593  ,  0.00880099,  0.05775919, -0.04590164])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00450531,  0.05168322, -0.07167747,  0.18192766, -0.10197283,
        0.23713737, -0.02397149,  0.19962199, -0.02295472, -0.01764196])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00450531, -0.05168322,  0.07167747, -0.18192766,  0.10197283,
       -0.23713737,  0.02397149, -0.19962199,  0.02295472,  0.01764196])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00945758,  0.01225524,  0.55607997,  0.05047713,  0.0200113 ,
       -0.09779156, -0.04315418,  0.07491335, -0.08146173, -0.02762501])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00945758, -0.01225524, -0.55607997, -0.05047713, -0.0200113 ,
        0.09779156,  0.04315418, -0.07491335,  0.08146173,  0.02762501])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 2.06346952e-04,  1.29222302e-02, -2.19904428e-01, -1.44357808e-02,
        1.13530929e-01, -1.74228043e-01,  2.75820348e-03, -2.40156872e-01,
        1.71009459e-02,  7.20383754e-02])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-2.06346952e-04, -1.29222302e-02,  2.19904428e-01,  1.44357808e-02,
       -1.13530929e-01,  1.74228043e-01, -2.75820348e-03,  2.40156872e-01,
       -1.71009459e-02, -7.20383754e-02])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00306408, -0.00633121, -0.17901556, -0.09915352,  0.01880953,
       -0.08482098,  0.01469338, -0.21529734,  0.05032289,  0.06403022])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00306408,  0.00633121,  0.17901556,  0.09915352, -0.01880953,
        0.08482098, -0.01469338,  0.21529734, -0.05032289, -0.06403022])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00144159, -0.0254443 , -0.19693255,  0.10242972,  0.06348515,
       -0.2129074 , -0.02222319, -0.0619299 , -0.0059138 , -0.05473953])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00144159,  0.0254443 ,  0.19693255, -0.10242972, -0.06348515,
        0.2129074 ,  0.02222319,  0.0619299 ,  0.0059138 ,  0.05473953])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00675852,  0.00682342,  0.29095227,  0.10345985, -0.18151873,
        0.24385615,  0.        ,  0.06281759,  0.00120754, -0.03176359])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00675852, -0.00682342, -0.29095227, -0.10345985,  0.18151873,
       -0.24385615,  0.        , -0.06281759, -0.00120754,  0.03176359])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00045588,  0.00586332, -0.20580889, -0.01305592, -0.01295881,
       -0.16906129, -0.02685114, -0.0675064 ,  0.01970361,  0.02063213])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00045588, -0.00586332,  0.20580889,  0.01305592,  0.01295881,
        0.16906129,  0.02685114,  0.0675064 , -0.01970361, -0.02063213])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00340555,  0.00065943,  0.08458825, -0.11407817, -0.01904628,
        0.06246276, -0.00499497, -0.13633485, -0.02321459,  0.04845406])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00340555, -0.00065943, -0.08458825,  0.11407817,  0.01904628,
       -0.06246276,  0.00499497,  0.13633485,  0.02321459, -0.04845406])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 8.27422426e-03, -8.69303880e-03, -4.06435194e-01, -1.07491766e-01,
        1.09803386e-01, -6.36120894e-02, -2.01113338e-04, -7.89637881e-02,
        4.92820374e-02,  4.96501394e-02])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-8.27422426e-03,  8.69303880e-03,  4.06435194e-01,  1.07491766e-01,
       -1.09803386e-01,  6.36120894e-02,  2.01113338e-04,  7.89637881e-02,
       -4.92820374e-02, -4.96501394e-02])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.01165592,  0.03774174,  0.40726484,  0.09046301, -0.241739  ,
        0.15514232,  0.01074211,  0.00586008, -0.04329927, -0.00644832])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.01165592, -0.03774174, -0.40726484, -0.09046301,  0.241739  ,
       -0.15514232, -0.01074211, -0.00586008,  0.04329927,  0.00644832])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00159931,  0.01046197,  0.27385993,  0.11473683, -0.11444251,
        0.07807487,  0.02285578,  0.085373  ,  0.0475996 , -0.01439873])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00159931, -0.01046197, -0.27385993, -0.11473683,  0.11444251,
       -0.07807487, -0.02285578, -0.085373  , -0.0475996 ,  0.01439873])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00197416, -0.03217966,  0.43769317, -0.10387411,  0.23515614,
        0.04854201, -0.03447649,  0.04765551, -0.07394532, -0.01626352])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00197416,  0.03217966, -0.43769317,  0.10387411, -0.23515614,
       -0.04854201,  0.03447649, -0.04765551,  0.07394532,  0.01626352])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.0086823 ,  0.0408613 ,  0.05276754,  0.28955518, -0.05011152,
       -0.15759592,  0.01027808,  0.09519541, -0.07596387, -0.07536626])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.0086823 , -0.0408613 , -0.05276754, -0.28955518,  0.05011152,
        0.15759592, -0.01027808, -0.09519541,  0.07596387,  0.07536626])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.0054018 , -0.02250905, -0.38852454,  0.01172198,  0.10940178,
       -0.12787374, -0.02107723,  0.01745577,  0.01826755, -0.02748935])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.0054018 ,  0.02250905,  0.38852454, -0.01172198, -0.10940178,
        0.12787374,  0.02107723, -0.01745577, -0.01826755,  0.02748935])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00622605, -0.06340374, -0.17138396, -0.03187527,  0.13868582,
       -0.05993952,  0.01995152, -0.12578451,  0.04930972, -0.02479405])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00622605,  0.06340374,  0.17138396,  0.03187527, -0.13868582,
        0.05993952, -0.01995152,  0.12578451, -0.04930972,  0.02479405])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00463781, -0.05115392,  0.5930374 , -0.13581599,  0.03226543,
        0.10105176,  0.01400821, -0.10224446, -0.04844499, -0.01670013])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00463781,  0.05115392, -0.5930374 ,  0.13581599, -0.03226543,
       -0.10105176, -0.01400821,  0.10224446,  0.04844499,  0.01670013])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00564515,  0.00126481,  0.6160973 , -0.0991021 , -0.01815792,
       -0.04156164,  0.03896189, -0.06486197, -0.07025976,  0.01562861])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00564515, -0.00126481, -0.6160973 ,  0.0991021 ,  0.01815792,
        0.04156164, -0.03896189,  0.06486197,  0.07025976, -0.01562861])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.0026699 , -0.04546484,  0.13445811,  0.13021873, -0.180491  ,
       -0.03224647,  0.02007341, -0.0288822 ,  0.0635919 , -0.08602626])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.0026699 ,  0.04546484, -0.13445811, -0.13021873,  0.180491  ,
        0.03224647, -0.02007341,  0.0288822 , -0.0635919 ,  0.08602626])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.01910473, -0.11204363, -0.01201998, -0.34403478,  0.15127368,
        0.16483803,  0.07970764, -0.10405486,  0.120425  ,  0.01751183])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.01910473,  0.11204363,  0.01201998,  0.34403478, -0.15127368,
       -0.16483803, -0.07970764,  0.10405486, -0.120425  , -0.01751183])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00993517, -0.04713703, -0.39135192,  0.0076198 ,  0.03274896,
        0.07081433, -0.00471138,  0.03744707,  0.10133634, -0.03249707])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00993517,  0.04713703,  0.39135192, -0.0076198 , -0.03274896,
       -0.07081433,  0.00471138, -0.03744707, -0.10133634,  0.03249707])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-1.05178667e-03,  9.39513224e-02, -1.33682067e-01,  2.05918193e-01,
       -2.02190326e-01, -7.26685401e-03,  8.28878184e-03,  2.79402013e-01,
       -2.08421995e-04, -7.08064213e-03])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 1.05178667e-03, -9.39513224e-02,  1.33682067e-01, -2.05918193e-01,
        2.02190326e-01,  7.26685401e-03, -8.28878184e-03, -2.79402013e-01,
        2.08421995e-04,  7.08064213e-03])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00236568,  0.00954593,  0.04447846, -0.14128425,  0.24335398,
        0.08969845, -0.0505797 ,  0.02661031, -0.12642803,  0.03253605])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00236568, -0.00954593, -0.04447846,  0.14128425, -0.24335398,
       -0.08969845,  0.0505797 , -0.02661031,  0.12642803, -0.03253605])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00780638, -0.04570315,  0.20328004, -0.23413602, -0.21282376,
        0.08632735,  0.02602049,  0.0893702 ,  0.07693252, -0.01601445])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00780638,  0.04570315, -0.20328004,  0.23413602,  0.21282376,
       -0.08632735, -0.02602049, -0.0893702 , -0.07693252,  0.01601445])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.01074101, -0.09035823, -0.18250619, -0.05052746,  0.05980894,
        0.00489434,  0.0356794 ,  0.08132157,  0.03068559, -0.12130872])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.01074101,  0.09035823,  0.18250619,  0.05052746, -0.05980894,
       -0.00489434, -0.0356794 , -0.08132157, -0.03068559,  0.12130872])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.00288562,  0.01556329, -0.41203652,  0.06436146,  0.09674447,
       -0.10161876,  0.01522314, -0.08009308,  0.00885967,  0.02207934])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.00288562, -0.01556329,  0.41203652, -0.06436146, -0.09674447,
        0.10161876, -0.01522314,  0.08009308, -0.00885967, -0.02207934])
2025-07-16 02:26:02 - shap - INFO - num_full_subsets = 5
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([ 0.0063924 ,  0.02956427, -0.05918071,  0.12670964, -0.13627125,
        0.01456443,  0.06096065,  0.1778776 ,  0.08794191, -0.012843  ])
2025-07-16 02:26:02 - shap - INFO - np.sum(w_aug) = 9.999999999999998
2025-07-16 02:26:02 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999999
2025-07-16 02:26:02 - shap - INFO - phi = array([-0.0063924 , -0.02956427,  0.05918071, -0.12670964,  0.13627125,
       -0.01456443, -0.06096065, -0.1778776 , -0.08794191,  0.012843  ])
