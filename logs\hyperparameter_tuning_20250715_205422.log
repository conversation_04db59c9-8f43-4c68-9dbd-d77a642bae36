2025-07-15 20:54:22 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 235, 'max_depth': 15, 'min_samples_split': 2, 'min_samples_leaf': 17, 'max_features': 'log2'}
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9834
2025-07-15 20:54:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250715_205435.html
2025-07-15 20:54:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250715_205435.html
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.15 秒
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 20
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:35 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:36 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:37 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:37 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:37 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:37 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:38 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:38 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:38 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:38 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 135, 'max_depth': 9, 'learning_rate': 0.1704482693220943, 'subsample': 0.8433573654439638, 'colsample_bytree': 0.5610514446454544}
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9869
2025-07-15 20:54:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250715_205439.html
2025-07-15 20:54:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250715_205439.html
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.33 秒
2025-07-15 20:54:39 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 20
2025-07-15 20:54:42 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 187, 'max_depth': 4, 'learning_rate': 0.12091061097011385, 'feature_fraction': 0.7806902520482117, 'bagging_fraction': 0.9230958055354184}
2025-07-15 20:54:42 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9853
2025-07-15 20:54:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:42 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250715_205442.html
2025-07-15 20:54:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250715_205443.html
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.49 秒
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 20
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 5.587456935185252, 'solver': 'liblinear'}
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-07-15 20:54:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250715_205443.html
2025-07-15 20:54:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250715_205443.html
2025-07-15 20:54:43 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.66 秒
